#!/usr/bin/env python3
"""
Dual Telegram Notifier for Live Trading System
Sends trade notifications to multiple Telegram chats simultaneously.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
import httpx

logger = logging.getLogger(__name__)

class DualTelegramNotifier:
    """Dual Telegram notifier that sends trade alerts to multiple chats."""

    def __init__(self, bot_token: Optional[str] = None, primary_chat_id: Optional[str] = None,
                 secondary_chat_id: Optional[str] = None):
        """Initialize dual Telegram notifier."""
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN')
        self.primary_chat_id = primary_chat_id or os.getenv('TELEGRAM_CHAT_ID')
        self.secondary_chat_id = secondary_chat_id or "-1002232263415"  # New chat ID (with group prefix)

        # Check if credentials are available
        self.enabled = bool(self.bot_token and self.primary_chat_id)
        self.dual_enabled = bool(self.bot_token and self.primary_chat_id and self.secondary_chat_id)

        # HTTP client for async requests
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Session tracking for PnL calculations
        self.session_start_balance = None

        if not self.enabled:
            logger.warning("Telegram credentials not found - notifications disabled")
        elif not self.dual_enabled:
            logger.warning("Secondary chat ID not configured - dual notifications disabled")
        else:
            logger.info(f"Dual Telegram notifier initialized:")
            logger.info(f"  Primary chat: {self.primary_chat_id}")
            logger.info(f"  Secondary chat: {self.secondary_chat_id}")

    async def send_message_to_chat(self, message: str, chat_id: str, parse_mode: str = "Markdown") -> bool:
        """Send a message to a specific Telegram chat."""
        if not self.enabled:
            logger.debug(f"Telegram disabled, would send to {chat_id}: {message[:50]}...")
            return False

        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                "chat_id": chat_id,
                "text": message,
                "parse_mode": parse_mode
            }

            response = await self.http_client.post(url, json=data)
            response.raise_for_status()

            result = response.json()
            if result.get("ok"):
                logger.debug(f"Message sent successfully to chat {chat_id}")
                return True
            else:
                logger.error(f"Telegram API error for chat {chat_id}: {result.get('description')}")
                return False

        except Exception as e:
            logger.error(f"Error sending message to chat {chat_id}: {e}")
            return False

    async def send_message_dual(self, message: str, parse_mode: str = "Markdown") -> Dict[str, bool]:
        """Send a message to both primary and secondary chats."""
        results = {}

        # Send to primary chat
        if self.primary_chat_id:
            results['primary'] = await self.send_message_to_chat(message, self.primary_chat_id, parse_mode)

        # Send to secondary chat
        if self.secondary_chat_id:
            results['secondary'] = await self.send_message_to_chat(message, self.secondary_chat_id, parse_mode)

        return results

    async def send_trade_alert_only(self, message: str, parse_mode: str = "Markdown") -> bool:
        """Send trade alert only to secondary chat (not profitability analysis)."""
        if not self.secondary_chat_id:
            logger.warning("Secondary chat ID not configured")
            return False

        return await self.send_message_to_chat(message, self.secondary_chat_id, parse_mode)

    def set_session_start_balance(self, balance: float):
        """Set the session start balance for PnL calculations."""
        self.session_start_balance = balance
        logger.info(f"Session start balance set to {balance:.6f} SOL")

    async def notify_trade_executed(self, trade_data: Dict[str, Any]) -> bool:
        """Send trade execution notification to both chats."""
        try:
            signal = trade_data.get('signal', {})
            position_data = trade_data.get('position_data', {})
            result = trade_data.get('transaction_result', {})

            # Extract trade information
            action = signal.get('action', 'UNKNOWN').upper()
            token = signal.get('token', 'UNKNOWN')
            price = signal.get('price', 0)
            size_sol = position_data.get('position_size_sol', 0)
            size_usd = position_data.get('position_size_usd', 0)
            confidence = signal.get('confidence', 0)
            signature = result.get('signature', 'N/A')
            execution_time = result.get('execution_time', 0)

            # Calculate PnL if session start balance is set
            pnl_info = ""
            if self.session_start_balance and position_data.get('total_wallet_sol'):
                current_balance = position_data['total_wallet_sol']
                pnl_sol = current_balance - self.session_start_balance
                pnl_percent = (pnl_sol / self.session_start_balance) * 100
                pnl_usd = pnl_sol * price

                pnl_emoji = "📈" if pnl_sol >= 0 else "📉"
                pnl_info = f"\n{pnl_emoji} *Session PnL*: {pnl_sol:+.6f} SOL (${pnl_usd:+.2f}) | {pnl_percent:+.2f}%"

            # Action emoji
            action_emoji = "🟢" if action == "BUY" else "🔴" if action == "SELL" else "🔄"

            # Build message
            message = f"""
{action_emoji} *TRADE EXECUTED* {action_emoji}

*Action*: {action}
*Token*: {token}
*Price*: ${price:.4f}
*Size*: {size_sol:.6f} SOL (${size_usd:.2f})
*Confidence*: {confidence:.2f}

*Execution*: {execution_time:.2f}s
*Signature*: `{signature[:16]}...`{pnl_info}

*Time*: {datetime.now().strftime('%H:%M:%S')}
"""

            # Send to both chats
            results = await self.send_message_dual(message)

            # Log results
            success_count = sum(results.values())
            total_chats = len(results)

            if success_count == total_chats:
                logger.info(f"✅ Trade notification sent to all {total_chats} chats")
                return True
            elif success_count > 0:
                logger.warning(f"⚠️ Trade notification sent to {success_count}/{total_chats} chats")
                return True
            else:
                logger.error("❌ Failed to send trade notification to any chat")
                return False

        except Exception as e:
            logger.error(f"Error sending trade notification: {e}")
            return False

    async def notify_pnl_milestone(self, pnl_metrics: Dict[str, Any], milestone_type: str) -> bool:
        """Send PnL milestone notification to both chats."""
        try:
            milestone_emoji = "🎉" if milestone_type == "profit" else "⚠️"

            message = f"""
{milestone_emoji} *PnL MILESTONE* {milestone_emoji}

*Type*: {milestone_type.upper()}
*Session PnL*: {pnl_metrics['pnl_sol']:+.6f} SOL (${pnl_metrics['pnl_usd']:+.2f})
*ROI*: {pnl_metrics['pnl_percent']:+.2f}%
*Current Balance*: {pnl_metrics['current_balance']:.6f} SOL
*Starting Balance*: {pnl_metrics['start_balance']:.6f} SOL

*Time*: {datetime.now().strftime('%H:%M:%S')}
"""

            results = await self.send_message_dual(message)
            return any(results.values())

        except Exception as e:
            logger.error(f"Error sending PnL milestone notification: {e}")
            return False

    async def notify_system_status(self, status: str, details: str = "") -> bool:
        """Send system status notification to both chats."""
        try:
            status_emoji = "✅" if status == "online" else "❌" if status == "error" else "⚠️"

            message = f"""
{status_emoji} *SYSTEM STATUS* {status_emoji}

*Status*: {status.upper()}
{details}

*Time*: {datetime.now().strftime('%H:%M:%S')}
"""

            results = await self.send_message_dual(message)
            return any(results.values())

        except Exception as e:
            logger.error(f"Error sending system status notification: {e}")
            return False

    async def test_connection(self) -> Dict[str, bool]:
        """Test connection to both Telegram chats."""
        if not self.enabled:
            logger.warning("Telegram not configured")
            return {'primary': False, 'secondary': False}

        test_message = f"""
🧪 *TEST MESSAGE* 🧪

Dual Telegram notifier is working correctly!

*Primary Chat*: {self.primary_chat_id}
*Secondary Chat*: {self.secondary_chat_id}
*Time*: {datetime.now().strftime('%H:%M:%S')}
"""

        results = await self.send_message_dual(test_message)

        for chat_type, success in results.items():
            if success:
                logger.info(f"✅ {chat_type.capitalize()} chat connection successful")
            else:
                logger.error(f"❌ {chat_type.capitalize()} chat connection failed")

        return results

    async def close(self):
        """Close the HTTP client."""
        if hasattr(self, 'http_client'):
            await self.http_client.aclose()


# Global instance for easy access
_dual_telegram_notifier = None

def get_dual_telegram_notifier() -> DualTelegramNotifier:
    """Get the global dual Telegram notifier instance."""
    global _dual_telegram_notifier

    if _dual_telegram_notifier is None:
        _dual_telegram_notifier = DualTelegramNotifier()

    return _dual_telegram_notifier
