---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/HedgeFund/solana_tx_utils/target/release/deps/libspl_transfer_hook_interface-d2051244973449b5.dylib'
relocations:
  - { offset: 0x43, size: 0x8, addend: 0x0, symName: __ZN12wasm_bindgen4__rt14GLOBAL_EXNDATA17h95569d95484d6845E, symObjAddr: 0x198E8, symBinAddr: 0x48C5C, symSize: 0x0 }
  - { offset: 0x12F, size: 0x8, addend: 0x0, symName: ___wbindgen_malloc, symObjAddr: 0x5C4, symBinAddr: 0x9D0, symSize: 0x5C }
  - { offset: 0x1FD, size: 0x8, addend: 0x0, symName: ___wbindgen_realloc, symObjAddr: 0x620, symBinAddr: 0xA2C, symSize: 0x64 }
  - { offset: 0x2D7, size: 0x8, addend: 0x0, symName: __ZN12wasm_bindgen4__rt14malloc_failure17hf0d5ed092be1cc84E, symObjAddr: 0x684, symBinAddr: 0x349F4, symSize: 0xC }
  - { offset: 0x2F1, size: 0x8, addend: 0x0, symName: ___wbindgen_free, symObjAddr: 0x690, symBinAddr: 0xA90, symSize: 0xC }
  - { offset: 0x377, size: 0x8, addend: 0x0, symName: ___wbindgen_exn_store, symObjAddr: 0x6A0, symBinAddr: 0xA9C, symSize: 0x14 }
  - { offset: 0x39F, size: 0x8, addend: 0x0, symName: ___wbindgen_exn_store, symObjAddr: 0x6A0, symBinAddr: 0xA9C, symSize: 0x14 }
  - { offset: 0x3C6, size: 0x8, addend: 0x0, symName: ___wbindgen_exn_store, symObjAddr: 0x6A0, symBinAddr: 0xA9C, symSize: 0x14 }
  - { offset: 0x3ED, size: 0x8, addend: 0x0, symName: ___wbindgen_exn_store, symObjAddr: 0x6A0, symBinAddr: 0xA9C, symSize: 0x14 }
  - { offset: 0x402, size: 0x8, addend: 0x0, symName: ___wbindgen_exn_store, symObjAddr: 0x6A0, symBinAddr: 0xA9C, symSize: 0x14 }
  - { offset: 0x175C, size: 0x8, addend: 0x0, symName: '__ZN12wasm_bindgen4__rt21LazyCell$LT$T$C$F$GT$8try_with17h73cb7c063ca7a61eE', symObjAddr: 0x470, symBinAddr: 0x904, symSize: 0xCC }
  - { offset: 0x210C, size: 0x8, addend: 0x0, symName: __ZN12wasm_bindgen9externref9HEAP_SLAB17he3dbdc1becf011b2E, symObjAddr: 0xB80, symBinAddr: 0x481B8, symSize: 0x0 }
  - { offset: 0x2195, size: 0x8, addend: 0x0, symName: ___externref_table_alloc, symObjAddr: 0x4AC, symBinAddr: 0xBDC, symSize: 0x20 }
  - { offset: 0x21C6, size: 0x8, addend: 0x0, symName: ___externref_table_dealloc, symObjAddr: 0x4CC, symBinAddr: 0xBFC, symSize: 0x54 }
  - { offset: 0x222A, size: 0x8, addend: 0x0, symName: ___externref_drop_slice, symObjAddr: 0x520, symBinAddr: 0xC50, symSize: 0x64 }
  - { offset: 0x22EB, size: 0x8, addend: 0x0, symName: ___externref_heap_live_count, symObjAddr: 0x584, symBinAddr: 0xCB4, symSize: 0x7C }
  - { offset: 0x24B2, size: 0x8, addend: 0x0, symName: __ZN12wasm_bindgen9externref31__wbindgen_externref_table_grow17h9b6cf93b25dfb249E, symObjAddr: 0x71C, symBinAddr: 0xD30, symSize: 0x40 }
  - { offset: 0x2AFC, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr113drop_in_place$LT$core..result..Result$LT$$LP$$RP$$C$core..cell..Cell$LT$wasm_bindgen..externref..Slab$GT$$GT$$GT$17h576283a4b0d29d46E', symObjAddr: 0x5C, symBinAddr: 0xAC4, symSize: 0x24 }
  - { offset: 0x3018, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function6FnOnce9call_once17h879e7439cff955b9E, symObjAddr: 0x48, symBinAddr: 0xAB0, symSize: 0x14 }
  - { offset: 0x3D9E, size: 0x8, addend: 0x0, symName: '__ZN9once_cell6unsync17OnceCell$LT$T$GT$15get_or_try_init17h2392ab4a80a61e43E', symObjAddr: 0x1C0, symBinAddr: 0xAE8, symSize: 0xF4 }
  - { offset: 0x4E77, size: 0x8, addend: 0x0, symName: _blake3_compress_in_place_portable, symObjAddr: 0xB9C, symBinAddr: 0xD70, symSize: 0x4 }
  - { offset: 0x4FCE, size: 0x8, addend: 0x0, symName: __ZN6blake38portable17compress_in_place17he56ff1761ae754d8E, symObjAddr: 0xBA0, symBinAddr: 0xD74, symSize: 0xE04 }
  - { offset: 0xAA4C, size: 0x8, addend: 0x0, symName: '__ZN5alloc11collections5btree3map25IntoIter$LT$K$C$V$C$A$GT$10dying_next17h1d88f957430bad1dE', symObjAddr: 0xECA8, symBinAddr: 0xC9C8, symSize: 0x224 }
  - { offset: 0xAE33, size: 0x8, addend: 0x0, symName: '__ZN5alloc11collections5btree3map25IntoIter$LT$K$C$V$C$A$GT$10dying_next17hfaa5a005d7e39d8eE', symObjAddr: 0xF0F0, symBinAddr: 0xCBEC, symSize: 0x224 }
  - { offset: 0xBA3B, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h095dc4f4c91ff6b9E', symObjAddr: 0x114B8, symBinAddr: 0xD1FC, symSize: 0xD0 }
  - { offset: 0xBB8F, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h3a19a4ab2b92359dE', symObjAddr: 0x11658, symBinAddr: 0xD2CC, symSize: 0xC0 }
  - { offset: 0xBCE3, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h40071feec6bbd372E', symObjAddr: 0x11718, symBinAddr: 0xD38C, symSize: 0xD0 }
  - { offset: 0xBE37, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h4a39c02819ac1667E', symObjAddr: 0x117E8, symBinAddr: 0xD45C, symSize: 0xD0 }
  - { offset: 0xBF8B, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h4a92fc1fe476e477E', symObjAddr: 0x118B8, symBinAddr: 0xD52C, symSize: 0xC0 }
  - { offset: 0xC0DF, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h4fe34a41a9b5eb13E', symObjAddr: 0x11978, symBinAddr: 0xD5EC, symSize: 0xC0 }
  - { offset: 0xC233, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h51ebdaf7922ccb0fE', symObjAddr: 0x11A38, symBinAddr: 0xD6AC, symSize: 0xD0 }
  - { offset: 0xC387, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h5fa1addd75b9c473E', symObjAddr: 0x11B08, symBinAddr: 0xD77C, symSize: 0xC0 }
  - { offset: 0xC4DB, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h86dfd012ac0923eeE', symObjAddr: 0x11BC8, symBinAddr: 0xD83C, symSize: 0xCC }
  - { offset: 0xC62F, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h94e52b7d38af74feE', symObjAddr: 0x11C94, symBinAddr: 0xD908, symSize: 0xCC }
  - { offset: 0xC783, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17hbaffb1ea2421dae5E', symObjAddr: 0x11D60, symBinAddr: 0xD9D4, symSize: 0xCC }
  - { offset: 0xCE60, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec11finish_grow17hd37f20dcfa74548bE, symObjAddr: 0x11430, symBinAddr: 0x34B78, symSize: 0x88 }
  - { offset: 0xCEF9, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17h59c9ec03f2a10329E', symObjAddr: 0x11E2C, symBinAddr: 0x34C00, symSize: 0xF0 }
  - { offset: 0xDF64, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h382cff577ce4ab40E', symObjAddr: 0x10FD0, symBinAddr: 0xCE10, symSize: 0x100 }
  - { offset: 0xE293, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h5608352b196d8cb3E', symObjAddr: 0x110D0, symBinAddr: 0xCF10, symSize: 0xA0 }
  - { offset: 0xE5B6, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h5ede859a095ca78aE', symObjAddr: 0x111E4, symBinAddr: 0xCFB0, symSize: 0x78 }
  - { offset: 0xE781, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h7cf1d5f987884bcfE', symObjAddr: 0x1125C, symBinAddr: 0xD028, symSize: 0xC0 }
  - { offset: 0xEAA5, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h90e2e762cfbfaa1eE', symObjAddr: 0x1131C, symBinAddr: 0xD0E8, symSize: 0x44 }
  - { offset: 0xEB81, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h9659d3e2cd59fce8E', symObjAddr: 0x11360, symBinAddr: 0xD12C, symSize: 0xD0 }
  - { offset: 0xF08C, size: 0x8, addend: 0x0, symName: '__ZN64_$LT$alloc..ffi..c_str..NulError$u20$as$u20$core..fmt..Debug$GT$3fmt17h18ce3d950e35fa91E', symObjAddr: 0x18D64, symBinAddr: 0x148E8, symSize: 0x4C }
  - { offset: 0xF42F, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$alloc..string..String$u20$as$u20$core..fmt..Debug$GT$3fmt17hdbacb71d50eff926E', symObjAddr: 0xD6B8, symBinAddr: 0xC7D4, symSize: 0x1C }
  - { offset: 0xF4E9, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$alloc..string..String$u20$as$u20$core..fmt..Write$GT$10write_char17h67c485e816708459E', symObjAddr: 0xD6D4, symBinAddr: 0xC7F0, symSize: 0x158 }
  - { offset: 0xF74C, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$alloc..string..String$u20$as$u20$core..fmt..Write$GT$9write_str17h4bc2793cacc1d696E', symObjAddr: 0xD82C, symBinAddr: 0xC948, symSize: 0x80 }
  - { offset: 0xF8A4, size: 0x8, addend: 0x0, symName: '__ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17ha0099383a97e68c2E', symObjAddr: 0x18D48, symBinAddr: 0x148CC, symSize: 0x1C }
  - { offset: 0x10A25, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr107drop_in_place$LT$core..pin..Pin$LT$alloc..boxed..Box$LT$std..sys..pal..unix..sync..mutex..Mutex$GT$$GT$$GT$17hc07c140671e79445E', symObjAddr: 0x1CB0, symBinAddr: 0x2340, symSize: 0x2C }
  - { offset: 0x10B16, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr108drop_in_place$LT$alloc..collections..btree..map..BTreeMap$LT$u64$C$gimli..read..abbrev..Abbreviation$GT$$GT$17haa74a0211d9ed923E', symObjAddr: 0x1CDC, symBinAddr: 0x236C, symSize: 0xB0 }
  - { offset: 0x10CE9, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr109drop_in_place$LT$alloc..sync..Arc$LT$std..sync..poison..mutex..Mutex$LT$alloc..vec..Vec$LT$u8$GT$$GT$$GT$$GT$17h61b1cac8a1fede90E', symObjAddr: 0x1D8C, symBinAddr: 0x241C, symSize: 0x30 }
  - { offset: 0x10D7D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr118drop_in_place$LT$std..io..Write..write_fmt..Adapter$LT$std..io..cursor..Cursor$LT$$RF$mut$u20$$u5b$u8$u5d$$GT$$GT$$GT$17h5c63d0c0bee45058E', symObjAddr: 0x1E54, symBinAddr: 0x244C, symSize: 0x20 }
  - { offset: 0x10DB3, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr123drop_in_place$LT$addr2line..Context$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17hc6d5be859abd218dE', symObjAddr: 0x1E74, symBinAddr: 0x246C, symSize: 0x70 }
  - { offset: 0x10F06, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr129drop_in_place$LT$addr2line..unit..ResUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h3200663a94d9b140E', symObjAddr: 0x202C, symBinAddr: 0x24DC, symSize: 0xE4 }
  - { offset: 0x11123, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr129drop_in_place$LT$addr2line..unit..SupUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17hea048140b03f01afE', symObjAddr: 0x2110, symBinAddr: 0x25C0, symSize: 0x54 }
  - { offset: 0x111FA, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr130drop_in_place$LT$addr2line..unit..ResUnits$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17he56c193886414dbaE', symObjAddr: 0x2164, symBinAddr: 0x2614, symSize: 0xD0 }
  - { offset: 0x1139E, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr130drop_in_place$LT$addr2line..unit..SupUnits$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h74d4d2bc946c0774E', symObjAddr: 0x2234, symBinAddr: 0x26E4, symSize: 0xE4 }
  - { offset: 0x1155A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr130drop_in_place$LT$gimli..read..dwarf..Dwarf$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h733659cc5e33a4cbE', symObjAddr: 0x2318, symBinAddr: 0x27C8, symSize: 0x58 }
  - { offset: 0x11623, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr131drop_in_place$LT$$u5b$core..option..Option$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$$u5d$$GT$17h1b55ae3732cae68aE', symObjAddr: 0x2370, symBinAddr: 0x2820, symSize: 0xCC }
  - { offset: 0x116CC, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr137drop_in_place$LT$gimli..read..dwarf..Unit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$usize$GT$$GT$17h90351d6bab11b82cE', symObjAddr: 0x2500, symBinAddr: 0x28EC, symSize: 0x54 }
  - { offset: 0x117C1, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x2940, symSize: 0x64 }
  - { offset: 0x117D8, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x2940, symSize: 0x64 }
  - { offset: 0x117ED, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x2940, symSize: 0x64 }
  - { offset: 0x11802, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x2940, symSize: 0x64 }
  - { offset: 0x11942, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr144drop_in_place$LT$alloc..vec..Vec$LT$core..option..Option$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$$GT$$GT$17hd5b41028903956eeE', symObjAddr: 0x25B8, symBinAddr: 0x29A4, symSize: 0x78 }
  - { offset: 0x11AA8, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr152drop_in_place$LT$alloc..vec..Vec$LT$addr2line..unit..ResUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17hdd35552c1680b2ebE', symObjAddr: 0x26FC, symBinAddr: 0x2A1C, symSize: 0xC4 }
  - { offset: 0x11C14, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr152drop_in_place$LT$alloc..vec..Vec$LT$addr2line..unit..SupUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17h6431a2f92897bef0E', symObjAddr: 0x27C0, symBinAddr: 0x2AE0, symSize: 0xF4 }
  - { offset: 0x11E2A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr156drop_in_place$LT$alloc..boxed..Box$LT$$u5b$core..option..Option$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$$u5d$$GT$$GT$17h61627ecd782bd92dE', symObjAddr: 0x28B4, symBinAddr: 0x2BD4, symSize: 0xF8 }
  - { offset: 0x11F51, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr159drop_in_place$LT$alloc..sync..ArcInner$LT$gimli..read..dwarf..Dwarf$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17h71c073e2ea863d36E', symObjAddr: 0x29AC, symBinAddr: 0x2CCC, symSize: 0x58 }
  - { offset: 0x1202A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr161drop_in_place$LT$alloc..vec..Vec$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17h938f97b5a308fc3cE', symObjAddr: 0x2A04, symBinAddr: 0x2D24, symSize: 0xAC }
  - { offset: 0x12265, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr172drop_in_place$LT$core..result..Result$LT$addr2line..Context$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$C$gimli..read..Error$GT$$GT$17h8205db00afecd676E', symObjAddr: 0x2B5C, symBinAddr: 0x2DD0, symSize: 0x80 }
  - { offset: 0x122F9, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr173drop_in_place$LT$alloc..boxed..Box$LT$$u5b$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$u5d$$GT$$GT$17h141b901e37828fa3E', symObjAddr: 0x2BDC, symBinAddr: 0x2E50, symSize: 0x9C }
  - { offset: 0x12310, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr173drop_in_place$LT$alloc..boxed..Box$LT$$u5b$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$u5d$$GT$$GT$17h141b901e37828fa3E', symObjAddr: 0x2BDC, symBinAddr: 0x2E50, symSize: 0x9C }
  - { offset: 0x1254C, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr181drop_in_place$LT$core..option..Option$LT$gimli..read..line..IncompleteLineProgram$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$usize$GT$$GT$$GT$17h0ce7bfce5e4abb6bE', symObjAddr: 0x2CE8, symBinAddr: 0x2EEC, symSize: 0x98 }
  - { offset: 0x127FF, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr181drop_in_place$LT$core..result..Result$LT$addr2line..frame..FrameIter$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$C$gimli..read..Error$GT$$GT$17hc32763af75cf70e4E', symObjAddr: 0x2D80, symBinAddr: 0x2F84, symSize: 0x44 }
  - { offset: 0x12939, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr184drop_in_place$LT$core..result..Result$LT$addr2line..function..Functions$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$C$gimli..read..Error$GT$$GT$17hae974801a783d12aE', symObjAddr: 0x2DC4, symBinAddr: 0x2FC8, symSize: 0xD8 }
  - { offset: 0x132C8, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr223drop_in_place$LT$std..sys..thread_local..native..lazy..State$LT$core..cell..Cell$LT$core..option..Option$LT$alloc..sync..Arc$LT$std..sync..poison..mutex..Mutex$LT$alloc..vec..Vec$LT$u8$GT$$GT$$GT$$GT$$GT$$C$$LP$$RP$$GT$$GT$17h8e1dbbdc8aa6e2b2E', symObjAddr: 0x2F10, symBinAddr: 0x30A0, symSize: 0x40 }
  - { offset: 0x133B0, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr231drop_in_place$LT$core..result..Result$LT$core..option..Option$LT$alloc..boxed..Box$LT$addr2line..unit..DwoUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$$C$gimli..read..Error$GT$$GT$17h48abe14940fae1d7E', symObjAddr: 0x2F50, symBinAddr: 0x30E0, symSize: 0xD4 }
  - { offset: 0x13596, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr275drop_in_place$LT$gimli..read..line..LineRows$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$gimli..read..line..IncompleteLineProgram$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$usize$GT$$C$usize$GT$$GT$17h1fb49ddc3575d792E', symObjAddr: 0x304C, symBinAddr: 0x31B4, symSize: 0x8C }
  - { offset: 0x13808, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr280drop_in_place$LT$$LT$alloc..collections..btree..map..IntoIter$LT$K$C$V$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$..drop..DropGuard$LT$u64$C$core..result..Result$LT$alloc..sync..Arc$LT$gimli..read..abbrev..Abbreviations$GT$$C$gimli..read..Error$GT$$C$alloc..alloc..Global$GT$$GT$17h412edb4cf40f508aE', symObjAddr: 0x30D8, symBinAddr: 0x3240, symSize: 0x84 }
  - { offset: 0x1399B, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h49e49416d7fc736bE', symObjAddr: 0x3330, symBinAddr: 0x32C4, symSize: 0x28 }
  - { offset: 0x13A70, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h2076e24185186a1eE', symObjAddr: 0x3358, symBinAddr: 0x32EC, symSize: 0xAC }
  - { offset: 0x13C84, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr44drop_in_place$LT$object..read..ObjectMap$GT$17hefa7b8970618855fE', symObjAddr: 0x3404, symBinAddr: 0x3398, symSize: 0x58 }
  - { offset: 0x13DB2, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17he05f3434a0e55321E', symObjAddr: 0x350C, symBinAddr: 0x33F0, symSize: 0x28 }
  - { offset: 0x13E3D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$std..io..stdio..StdinLock$GT$17h7f23f0068af34be6E', symObjAddr: 0x3534, symBinAddr: 0x3418, symSize: 0x54 }
  - { offset: 0x13E54, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$std..io..stdio..StdinLock$GT$17h7f23f0068af34be6E', symObjAddr: 0x3534, symBinAddr: 0x3418, symSize: 0x54 }
  - { offset: 0x13E69, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$std..io..stdio..StdinLock$GT$17h7f23f0068af34be6E', symObjAddr: 0x3534, symBinAddr: 0x3418, symSize: 0x54 }
  - { offset: 0x13F4B, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr48drop_in_place$LT$alloc..ffi..c_str..NulError$GT$17hf807e8c3d992613aE', symObjAddr: 0x35E0, symBinAddr: 0x346C, symSize: 0x28 }
  - { offset: 0x14006, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr55drop_in_place$LT$gimli..read..abbrev..Abbreviations$GT$17h5028fd7f23a66a5dE', symObjAddr: 0x38D4, symBinAddr: 0x3494, symSize: 0x84 }
  - { offset: 0x141BD, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr60drop_in_place$LT$gimli..read..abbrev..AbbreviationsCache$GT$17h109635aebb38ad94E', symObjAddr: 0x39E8, symBinAddr: 0x3518, symSize: 0xCC }
  - { offset: 0x1437F, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr62drop_in_place$LT$std..sys..sync..rwlock..queue..PanicGuard$GT$17ha6ff6fed8f58b75aE', symObjAddr: 0x3AB4, symBinAddr: 0x35E4, symSize: 0x44 }
  - { offset: 0x143C6, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr64drop_in_place$LT$std..sys..pal..unix..sync..mutex..AttrGuard$GT$17hfae1c338ebc17455E', symObjAddr: 0x3AF8, symBinAddr: 0x3628, symSize: 0x48 }
  - { offset: 0x14401, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr65drop_in_place$LT$alloc..vec..Vec$LT$alloc..string..String$GT$$GT$17h0e8d3320a68b6c20E', symObjAddr: 0x3B40, symBinAddr: 0x3670, symSize: 0x80 }
  - { offset: 0x14598, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr65drop_in_place$LT$std..backtrace_rs..symbolize..gimli..Context$GT$17he1a9681aa2b789c5E', symObjAddr: 0x3BC0, symBinAddr: 0x36F0, symSize: 0xE8 }
  - { offset: 0x14829, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr65drop_in_place$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$17h1ad26fdf3d4d883aE', symObjAddr: 0x3CA8, symBinAddr: 0x37D8, symSize: 0x50 }
  - { offset: 0x14893, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr66drop_in_place$LT$std..backtrace_rs..backtrace..libunwind..Bomb$GT$17hcf55bbc6e2408f91E', symObjAddr: 0x3D50, symBinAddr: 0x3828, symSize: 0x40 }
  - { offset: 0x14938, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr70drop_in_place$LT$std..backtrace_rs..symbolize..gimli..stash..Stash$GT$17h823b9ed5294710a1E', symObjAddr: 0x3E8C, symBinAddr: 0x3868, symSize: 0xB8 }
  - { offset: 0x14BDB, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr71drop_in_place$LT$std..backtrace_rs..symbolize..gimli..macho..Object$GT$17h7d766a5d50e6fcdbE', symObjAddr: 0x3F44, symBinAddr: 0x3920, symSize: 0xD8 }
  - { offset: 0x14E9F, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr72drop_in_place$LT$core..option..Option$LT$object..read..ObjectMap$GT$$GT$17hc34cda9cf8984625E', symObjAddr: 0x4088, symBinAddr: 0x39F8, symSize: 0x64 }
  - { offset: 0x15017, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr73drop_in_place$LT$alloc..vec..Vec$LT$addr2line..line..LineSequence$GT$$GT$17h0feaef6467c5bb06E', symObjAddr: 0x40EC, symBinAddr: 0x3A5C, symSize: 0x84 }
  - { offset: 0x1516E, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr77drop_in_place$LT$alloc..boxed..Box$LT$$u5b$alloc..string..String$u5d$$GT$$GT$17h8657c1556d878703E', symObjAddr: 0x41C8, symBinAddr: 0x3AE0, symSize: 0x70 }
  - { offset: 0x15185, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr77drop_in_place$LT$alloc..boxed..Box$LT$$u5b$alloc..string..String$u5d$$GT$$GT$17h8657c1556d878703E', symObjAddr: 0x41C8, symBinAddr: 0x3AE0, symSize: 0x70 }
  - { offset: 0x1528E, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr77drop_in_place$LT$std..panicking..begin_panic_handler..FormatStringPayload$GT$17h1141e34cc3e1cd8cE', symObjAddr: 0x4238, symBinAddr: 0x3B50, symSize: 0x34 }
  - { offset: 0x15349, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr79drop_in_place$LT$std..sync..poison..rwlock..RwLockReadGuard$LT$$LP$$RP$$GT$$GT$17h3c7acdfaefb6864dE', symObjAddr: 0x435C, symBinAddr: 0x3B84, symSize: 0x68 }
  - { offset: 0x153E2, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr81drop_in_place$LT$$LP$usize$C$std..backtrace_rs..symbolize..gimli..Mapping$RP$$GT$17h0fc60d1e178e2af3E', symObjAddr: 0x4458, symBinAddr: 0x3BEC, symSize: 0x54 }
  - { offset: 0x15463, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr81drop_in_place$LT$core..result..Result$LT$$LP$$RP$$C$std..io..error..Error$GT$$GT$17h32d66e7d6b4290f9E', symObjAddr: 0x44AC, symBinAddr: 0x3C40, symSize: 0x20 }
  - { offset: 0x15483, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr84drop_in_place$LT$alloc..sync..ArcInner$LT$gimli..read..abbrev..Abbreviations$GT$$GT$17hc1742a6ee5f4f5c4E', symObjAddr: 0x455C, symBinAddr: 0x3C60, symSize: 0x84 }
  - { offset: 0x1564A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr87drop_in_place$LT$alloc..sync..ArcInner$LT$std..sys..pal..unix..fs..InnerReadDir$GT$$GT$17h22f7f5fc9c3aa8bdE', symObjAddr: 0x45E0, symBinAddr: 0x3CE4, symSize: 0x60 }
  - { offset: 0x1582D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr88drop_in_place$LT$alloc..vec..Vec$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17heda6be67225cff8cE', symObjAddr: 0x4674, symBinAddr: 0x3D44, symSize: 0x98 }
  - { offset: 0x15A4C, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr92drop_in_place$LT$core..result..Result$LT$addr2line..line..Lines$C$gimli..read..Error$GT$$GT$17h75873180c89d56d0E', symObjAddr: 0x47E8, symBinAddr: 0x3DDC, symSize: 0xE4 }
  - { offset: 0x15C90, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr93drop_in_place$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$17h8ea9f8655928f4fcE', symObjAddr: 0x48CC, symBinAddr: 0x3EC0, symSize: 0x6C }
  - { offset: 0x15D2A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr95drop_in_place$LT$core..option..IntoIter$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17he46fad7f279ea952E', symObjAddr: 0x4938, symBinAddr: 0x3F2C, symSize: 0x5C }
  - { offset: 0x15D48, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr95drop_in_place$LT$core..option..IntoIter$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17he46fad7f279ea952E', symObjAddr: 0x4938, symBinAddr: 0x3F2C, symSize: 0x5C }
  - { offset: 0x15D5D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr95drop_in_place$LT$core..option..IntoIter$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17he46fad7f279ea952E', symObjAddr: 0x4938, symBinAddr: 0x3F2C, symSize: 0x5C }
  - { offset: 0x17B43, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h025645fbfb2d8cd1E, symObjAddr: 0x4D90, symBinAddr: 0x3F9C, symSize: 0x134 }
  - { offset: 0x17D69, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h246a4f2d911688a1E, symObjAddr: 0x4EC4, symBinAddr: 0x40D0, symSize: 0xD0 }
  - { offset: 0x17E63, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h6ce4cdb71517d19eE, symObjAddr: 0x4F94, symBinAddr: 0x41A0, symSize: 0xCC }
  - { offset: 0x17F5D, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h6e4fd85017a3f5e4E, symObjAddr: 0x5060, symBinAddr: 0x426C, symSize: 0xD0 }
  - { offset: 0x18057, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17ha3c898ebd570d9a9E, symObjAddr: 0x5130, symBinAddr: 0x433C, symSize: 0xCC }
  - { offset: 0x181A7, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17hbe3f226301cd071dE, symObjAddr: 0x51FC, symBinAddr: 0x4408, symSize: 0xCC }
  - { offset: 0x182A1, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17hc3d7c76f255c304aE, symObjAddr: 0x52C8, symBinAddr: 0x44D4, symSize: 0xD0 }
  - { offset: 0x1839B, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17heb13e39171436e07E, symObjAddr: 0x5398, symBinAddr: 0x45A4, symSize: 0x11C }
  - { offset: 0x1855E, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort12sort4_stable17h29d5ecfff8d25808E, symObjAddr: 0x54B4, symBinAddr: 0x46C0, symSize: 0x1B4 }
  - { offset: 0x18701, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort12sort4_stable17he2b82e95c0946cc4E, symObjAddr: 0x5668, symBinAddr: 0x4874, symSize: 0x180 }
  - { offset: 0x18B1D, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort12sort8_stable17h40f301b851efa79fE, symObjAddr: 0x57E8, symBinAddr: 0x49F4, symSize: 0x2B8 }
  - { offset: 0x190A1, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort18small_sort_general17h8f0f2bfe6ac37bfbE, symObjAddr: 0x5AA0, symBinAddr: 0x4CAC, symSize: 0x490 }
  - { offset: 0x197A8, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort18small_sort_general17h9886ba35fa9ddf7bE, symObjAddr: 0x5F30, symBinAddr: 0x513C, symSize: 0x3E0 }
  - { offset: 0x19CBA, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h001473db42a80264E, symObjAddr: 0x6310, symBinAddr: 0x551C, symSize: 0xB0 }
  - { offset: 0x19D5B, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h1e1ea2c4d7503f74E, symObjAddr: 0x63C0, symBinAddr: 0x55CC, symSize: 0xCC }
  - { offset: 0x19E5A, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h4c238536f310567cE, symObjAddr: 0x648C, symBinAddr: 0x5698, symSize: 0xFC }
  - { offset: 0x19FF0, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h5ae8ea909444dc00E, symObjAddr: 0x6588, symBinAddr: 0x5794, symSize: 0xAC }
  - { offset: 0x1A091, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h677848d84e66ea2aE, symObjAddr: 0x6634, symBinAddr: 0x5840, symSize: 0xA8 }
  - { offset: 0x1A132, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h753c2c33d7211ceaE, symObjAddr: 0x66DC, symBinAddr: 0x58E8, symSize: 0xBC }
  - { offset: 0x1A1D3, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17hceef73df05978c22E, symObjAddr: 0x6798, symBinAddr: 0x59A4, symSize: 0x90 }
  - { offset: 0x1A274, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17hfb494d5f79707c02E, symObjAddr: 0x6828, symBinAddr: 0x5A34, symSize: 0xAC }
  - { offset: 0x1A538, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h103ff636fd5ab89dE, symObjAddr: 0x68D4, symBinAddr: 0x5AE0, symSize: 0x148 }
  - { offset: 0x1A7DB, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h1e88dc2727ad5c37E, symObjAddr: 0x6A1C, symBinAddr: 0x5C28, symSize: 0x16C }
  - { offset: 0x1AAD4, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h54d923433181cd8aE, symObjAddr: 0x6B88, symBinAddr: 0x5D94, symSize: 0x16C }
  - { offset: 0x1ADCD, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h76405073938de01eE, symObjAddr: 0x6CF4, symBinAddr: 0x5F00, symSize: 0x148 }
  - { offset: 0x1B070, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h9cb82a8482cd4f53E, symObjAddr: 0x6E3C, symBinAddr: 0x6048, symSize: 0x148 }
  - { offset: 0x1B313, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17hf9ea8a01734384bdE, symObjAddr: 0x6F84, symBinAddr: 0x6190, symSize: 0x148 }
  - { offset: 0x1B702, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h00ab1c6180ac4055E, symObjAddr: 0x70CC, symBinAddr: 0x62D8, symSize: 0x4E0 }
  - { offset: 0x1BD32, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h0aa4aacce1cc6d7cE, symObjAddr: 0x75AC, symBinAddr: 0x67B8, symSize: 0x5E8 }
  - { offset: 0x1C3AF, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h343dbae25f37733bE, symObjAddr: 0x7B94, symBinAddr: 0x6DA0, symSize: 0x538 }
  - { offset: 0x1CA78, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h4258b1bc2caa3ec0E, symObjAddr: 0x80CC, symBinAddr: 0x72D8, symSize: 0x6F0 }
  - { offset: 0x1D141, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h9cf8228521243839E, symObjAddr: 0x87BC, symBinAddr: 0x79C8, symSize: 0x5E8 }
  - { offset: 0x1D7BE, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17he5c0348fca8b4ee7E, symObjAddr: 0x8DA4, symBinAddr: 0x7FB0, symSize: 0x63C }
  - { offset: 0x1E006, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17h115e1a8136b6fa84E, symObjAddr: 0x93E0, symBinAddr: 0x85EC, symSize: 0x820 }
  - { offset: 0x1EBCA, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17h270cbea95e7bc0c9E, symObjAddr: 0x9C00, symBinAddr: 0x8E0C, symSize: 0x73C }
  - { offset: 0x1F2DA, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17h3e7b305b5c7976baE, symObjAddr: 0xA33C, symBinAddr: 0x9548, symSize: 0x6C8 }
  - { offset: 0x1FD5D, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17hc1d95435bd0f54c5E, symObjAddr: 0xAA04, symBinAddr: 0x9C10, symSize: 0x828 }
  - { offset: 0x20E12, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17hcf4fb219382f768bE, symObjAddr: 0xB22C, symBinAddr: 0xA438, symSize: 0x7FC }
  - { offset: 0x219D5, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17hfd59b4f11a472dd5E, symObjAddr: 0xBA28, symBinAddr: 0xAC34, symSize: 0x6D0 }
  - { offset: 0x2248F, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable7ipnsort17h519db9bb88c7d2c0E, symObjAddr: 0xC0F8, symBinAddr: 0xB304, symSize: 0x1E4 }
  - { offset: 0x225A2, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable7ipnsort17hc49a474c49f61d9aE, symObjAddr: 0xC2DC, symBinAddr: 0xB4E8, symSize: 0x290 }
  - { offset: 0x22846, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable8heapsort8heapsort17h6b7b52018c9f5da0E, symObjAddr: 0xC56C, symBinAddr: 0xB778, symSize: 0x10C }
  - { offset: 0x22A4B, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable8heapsort8heapsort17h719a9553e129d672E, symObjAddr: 0xC678, symBinAddr: 0xB884, symSize: 0x160 }
  - { offset: 0x22DDE, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable9quicksort9quicksort17h96e7db41672c501eE, symObjAddr: 0xC7D8, symBinAddr: 0xB9E4, symSize: 0x448 }
  - { offset: 0x23690, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable9quicksort9quicksort17ha039472c97b018d8E, symObjAddr: 0xCC20, symBinAddr: 0xBE2C, symSize: 0x5BC }
  - { offset: 0x24384, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h225d92188c8db6c3E', symObjAddr: 0x13C, symBinAddr: 0x1B78, symSize: 0x30 }
  - { offset: 0x2439E, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h22a55d7ebb819ea6E', symObjAddr: 0x16C, symBinAddr: 0x1BA8, symSize: 0x30 }
  - { offset: 0x24407, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h229631da8bdd67acE', symObjAddr: 0x464, symBinAddr: 0x1BD8, symSize: 0x1C }
  - { offset: 0x2441A, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h45300256e973f494E', symObjAddr: 0x4F8, symBinAddr: 0x1BF4, symSize: 0x30 }
  - { offset: 0x2445F, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h6cf282856d53ef8fE', symObjAddr: 0x66C, symBinAddr: 0x1C24, symSize: 0x74 }
  - { offset: 0x245A0, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h832369a0cde054d2E', symObjAddr: 0x77C, symBinAddr: 0x1C98, symSize: 0x30 }
  - { offset: 0x245E5, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h99ba184c1a5db1eaE', symObjAddr: 0x91C, symBinAddr: 0x1CC8, symSize: 0x1C }
  - { offset: 0x24685, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h15c50277390862aeE', symObjAddr: 0xCF0, symBinAddr: 0x1D68, symSize: 0x2C }
  - { offset: 0x246B3, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num52_$LT$impl$u20$core..fmt..Debug$u20$for$u20$usize$GT$3fmt17h6def11b803266407E', symObjAddr: 0xDA0, symBinAddr: 0x1D94, symSize: 0x2C }
  - { offset: 0x2471B, size: 0x8, addend: 0x0, symName: '__ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h3bfca5eae23b0d69E', symObjAddr: 0xC20, symBinAddr: 0x1CE4, symSize: 0x1C }
  - { offset: 0x24735, size: 0x8, addend: 0x0, symName: '__ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17ha43e2829162aef39E', symObjAddr: 0xC3C, symBinAddr: 0x1D00, symSize: 0x68 }
  - { offset: 0x24A79, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write10write_char17h2bd2da6422df0c04E, symObjAddr: 0xDCC, symBinAddr: 0x1DC0, symSize: 0x128 }
  - { offset: 0x24C10, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write10write_char17h375232e784da95c3E, symObjAddr: 0xEF4, symBinAddr: 0x1EE8, symSize: 0x174 }
  - { offset: 0x24E10, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write10write_char17h7453f2c039370a9dE, symObjAddr: 0x1328, symBinAddr: 0x205C, symSize: 0x180 }
  - { offset: 0x24FEA, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17h2a0fceba8367f855E, symObjAddr: 0x1620, symBinAddr: 0x21DC, symSize: 0x1C }
  - { offset: 0x25019, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17h7b0eee5bb9fc5859E, symObjAddr: 0x163C, symBinAddr: 0x21F8, symSize: 0x1C }
  - { offset: 0x25048, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17hb212afec46fd02ecE, symObjAddr: 0x1658, symBinAddr: 0x2214, symSize: 0x1C }
  - { offset: 0x25077, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17he153d8ed26875afeE, symObjAddr: 0x1690, symBinAddr: 0x2230, symSize: 0x1C }
  - { offset: 0x250D1, size: 0x8, addend: 0x0, symName: '__ZN50_$LT$$BP$mut$u20$T$u20$as$u20$core..fmt..Debug$GT$3fmt17he39465caf33bcc4eE', symObjAddr: 0xD258, symBinAddr: 0xC3E8, symSize: 0x14 }
  - { offset: 0x2511E, size: 0x8, addend: 0x0, symName: '__ZN52_$LT$$RF$mut$u20$T$u20$as$u20$core..fmt..Display$GT$3fmt17hc6db9c4561bb0640E', symObjAddr: 0xD2E0, symBinAddr: 0xC3FC, symSize: 0x1C }
  - { offset: 0x25A96, size: 0x8, addend: 0x0, symName: '__ZN55_$LT$$RF$str$u20$as$u20$core..str..pattern..Pattern$GT$15is_contained_in17h07538af93eae73f4E', symObjAddr: 0xD2FC, symBinAddr: 0xC418, symSize: 0x3BC }
  - { offset: 0x25EA8, size: 0x8, addend: 0x0, symName: __ZN4core5panic12PanicPayload6as_str17h5cdc46f655586ed2E, symObjAddr: 0x4D7C, symBinAddr: 0x3F88, symSize: 0x14 }
  - { offset: 0x26CC0, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h372dd670162a11a3E', symObjAddr: 0x1714, symBinAddr: 0x224C, symSize: 0x18 }
  - { offset: 0x26D10, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h4dbfbb5401248555E', symObjAddr: 0x1754, symBinAddr: 0x2264, symSize: 0xA4 }
  - { offset: 0x26E55, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h59664865ad50834bE', symObjAddr: 0x17F8, symBinAddr: 0x2308, symSize: 0x10 }
  - { offset: 0x26E90, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17hb8f0a3a311886dd1E', symObjAddr: 0x194C, symBinAddr: 0x2318, symSize: 0x18 }
  - { offset: 0x26EE0, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17hc37bdb1ed22c5dd7E', symObjAddr: 0x19E0, symBinAddr: 0x2330, symSize: 0x10 }
  - { offset: 0x28D1C, size: 0x8, addend: 0x0, symName: '__ZN4core4cell4once17OnceCell$LT$T$GT$8try_init17he4193c4098c349bdE', symObjAddr: 0x4A84, symBinAddr: 0x34A00, symSize: 0x154 }
  - { offset: 0x29178, size: 0x8, addend: 0x0, symName: __ZN4core9panicking13assert_failed17he664f25a4eeebf5cE, symObjAddr: 0xD234, symBinAddr: 0x34B54, symSize: 0x24 }
  - { offset: 0x294F5, size: 0x8, addend: 0x0, symName: __ZN3std9panicking11panic_count8increase17h1d24b79d4790a8a8E, symObjAddr: 0x3A9E8, symBinAddr: 0x1E568, symSize: 0x80 }
  - { offset: 0x295E9, size: 0x8, addend: 0x0, symName: __ZN3std9panicking11panic_count17is_zero_slow_path17hbb753c2ecca816a8E, symObjAddr: 0x3AB00, symBinAddr: 0x351A8, symSize: 0x2C }
  - { offset: 0x29690, size: 0x8, addend: 0x0, symName: ___rust_drop_panic, symObjAddr: 0x39FEC, symBinAddr: 0x1DE98, symSize: 0xB8 }
  - { offset: 0x29883, size: 0x8, addend: 0x0, symName: __ZN3std9panicking12default_hook17ha0b223ccc4379930E, symObjAddr: 0x3A3D0, symBinAddr: 0x1DF50, symSize: 0x1EC }
  - { offset: 0x29B21, size: 0x8, addend: 0x0, symName: '__ZN3std9panicking12default_hook28_$u7b$$u7b$closure$u7d$$u7d$17h5c3a234feebd11a5E', symObjAddr: 0x3A5BC, symBinAddr: 0x1E13C, symSize: 0x1A8 }
  - { offset: 0x29D03, size: 0x8, addend: 0x0, symName: '__ZN3std9panicking12default_hook28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17h2cb359aea37a8eccE', symObjAddr: 0x3A764, symBinAddr: 0x1E2E4, symSize: 0x284 }
  - { offset: 0x2A177, size: 0x8, addend: 0x0, symName: _rust_begin_unwind, symObjAddr: 0x3AB80, symBinAddr: 0x1E5E8, symSize: 0x20 }
  - { offset: 0x2A1DE, size: 0x8, addend: 0x0, symName: '__ZN102_$LT$std..panicking..begin_panic_handler..FormatStringPayload$u20$as$u20$core..panic..PanicPayload$GT$8take_box17hf9936339f9bb3e6eE', symObjAddr: 0x3ABA0, symBinAddr: 0x1E608, symSize: 0x110 }
  - { offset: 0x2A472, size: 0x8, addend: 0x0, symName: '__ZN102_$LT$std..panicking..begin_panic_handler..FormatStringPayload$u20$as$u20$core..panic..PanicPayload$GT$3get17h671f5c789103ccc6E', symObjAddr: 0x3ACB0, symBinAddr: 0x1E718, symSize: 0xB0 }
  - { offset: 0x2A5AB, size: 0x8, addend: 0x0, symName: '__ZN95_$LT$std..panicking..begin_panic_handler..FormatStringPayload$u20$as$u20$core..fmt..Display$GT$3fmt17h1a7e141a9fd2e841E', symObjAddr: 0x3AD60, symBinAddr: 0x1E7C8, symSize: 0x5C }
  - { offset: 0x2A6A8, size: 0x8, addend: 0x0, symName: '__ZN99_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..panic..PanicPayload$GT$8take_box17hfde794f38dfdff84E', symObjAddr: 0x3ADBC, symBinAddr: 0x1E824, symSize: 0x50 }
  - { offset: 0x2A71F, size: 0x8, addend: 0x0, symName: '__ZN99_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..panic..PanicPayload$GT$3get17h40c5dcbca42c5d43E', symObjAddr: 0x3AE0C, symBinAddr: 0x1E874, symSize: 0x18 }
  - { offset: 0x2A73A, size: 0x8, addend: 0x0, symName: '__ZN99_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..panic..PanicPayload$GT$6as_str17h36252a21ddc56c9bE', symObjAddr: 0x3AE24, symBinAddr: 0x1E88C, symSize: 0x18 }
  - { offset: 0x2A75B, size: 0x8, addend: 0x0, symName: '__ZN92_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..fmt..Display$GT$3fmt17hd84ef799566d4562E', symObjAddr: 0x3AE3C, symBinAddr: 0x1E8A4, symSize: 0x1C }
  - { offset: 0x2A777, size: 0x8, addend: 0x0, symName: '__ZN3std9panicking19begin_panic_handler28_$u7b$$u7b$closure$u7d$$u7d$17hcc8f653f753c0254E', symObjAddr: 0x3AE58, symBinAddr: 0x1E8C0, symSize: 0xC4 }
  - { offset: 0x2A8DE, size: 0x8, addend: 0x0, symName: __ZN3std9panicking14payload_as_str17h326c21158c3ccbd7E, symObjAddr: 0x3B008, symBinAddr: 0x1E984, symSize: 0xB0 }
  - { offset: 0x2A9C2, size: 0x8, addend: 0x0, symName: __ZN3std9panicking20rust_panic_with_hook17h203f96c93e7ac62dE, symObjAddr: 0x3B0B8, symBinAddr: 0x1EA34, symSize: 0x3F8 }
  - { offset: 0x2B1D5, size: 0x8, addend: 0x0, symName: _rust_panic, symObjAddr: 0x3B5E4, symBinAddr: 0x1EE2C, symSize: 0x60 }
  - { offset: 0x2B216, size: 0x8, addend: 0x0, symName: __ZN3std7process5abort17h5bdab09773b2c592E, symObjAddr: 0x35D9C, symBinAddr: 0x34EA4, symSize: 0xC }
  - { offset: 0x2B2A0, size: 0x8, addend: 0x0, symName: __ZN3std4path4Path13_strip_prefix17h55294dff41b26308E, symObjAddr: 0x33B38, symBinAddr: 0x1CED4, symSize: 0x21C }
  - { offset: 0x2B3E0, size: 0x8, addend: 0x0, symName: __ZN3std4path4Path5_join17h516bc57fe1e4225dE, symObjAddr: 0x34358, symBinAddr: 0x1D0F0, symSize: 0x1AC }
  - { offset: 0x2B973, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components15len_before_body17ha7ee2271a8a6c6d9E, symObjAddr: 0x31DBC, symBinAddr: 0x1C08C, symSize: 0x188 }
  - { offset: 0x2B98A, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components15len_before_body17ha7ee2271a8a6c6d9E, symObjAddr: 0x31DBC, symBinAddr: 0x1C08C, symSize: 0x188 }
  - { offset: 0x2B99F, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components15len_before_body17ha7ee2271a8a6c6d9E, symObjAddr: 0x31DBC, symBinAddr: 0x1C08C, symSize: 0x188 }
  - { offset: 0x2BB7B, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components7as_path17h6e25608fa8cb9557E, symObjAddr: 0x31F44, symBinAddr: 0x1C214, symSize: 0x530 }
  - { offset: 0x2C046, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components25parse_next_component_back17h5239b6f3b65d70c8E, symObjAddr: 0x32474, symBinAddr: 0x1C744, symSize: 0x120 }
  - { offset: 0x2C244, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..path..Components$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h6e8b0f281526c929E', symObjAddr: 0x3272C, symBinAddr: 0x1C864, symSize: 0x340 }
  - { offset: 0x2C590, size: 0x8, addend: 0x0, symName: '__ZN95_$LT$std..path..Components$u20$as$u20$core..iter..traits..double_ended..DoubleEndedIterator$GT$9next_back17h15500b5b5dde7decE', symObjAddr: 0x32A6C, symBinAddr: 0x1CBA4, symSize: 0x330 }
  - { offset: 0x2C73E, size: 0x8, addend: 0x0, symName: '__ZN61_$LT$std..path..Component$u20$as$u20$core..cmp..PartialEq$GT$2eq17h238f4f8a8cc29bb2E', symObjAddr: 0x3C0CC, symBinAddr: 0x1F574, symSize: 0x138 }
  - { offset: 0x2C902, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix2fs4File6open_c17h0a400570de534b0aE, symObjAddr: 0x3D220, symBinAddr: 0x1F8B0, symSize: 0x1B0 }
  - { offset: 0x2CA61, size: 0x8, addend: 0x0, symName: '__ZN91_$LT$std..sys..pal..unix..fs..ReadDir$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17he1b3299a727d70eaE', symObjAddr: 0x3CD90, symBinAddr: 0x1F6AC, symSize: 0x138 }
  - { offset: 0x2CD0B, size: 0x8, addend: 0x0, symName: '__ZN70_$LT$std..sys..pal..unix..fs..Dir$u20$as$u20$core..ops..drop..Drop$GT$4drop17h2f7cbb54b209bd1fE', symObjAddr: 0x3CEC8, symBinAddr: 0x1F7E4, symSize: 0xCC }
  - { offset: 0x2CDFD, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix2fs7readdir17h52c204b360126013E, symObjAddr: 0x3DAF4, symBinAddr: 0x1FA60, symSize: 0x1A4 }
  - { offset: 0x2D100, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix2os13env_read_lock17h1370e80127ab09adE, symObjAddr: 0x400EC, symBinAddr: 0x1FC04, symSize: 0x2EC }
  - { offset: 0x2D7AB, size: 0x8, addend: 0x0, symName: '__ZN3std3sys3pal4unix2os6getenv28_$u7b$$u7b$closure$u7d$$u7d$17h09d09dd30efbeda3E', symObjAddr: 0x403D8, symBinAddr: 0x1FEF0, symSize: 0x150 }
  - { offset: 0x2D9D7, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$5write17hf7eb28e2e3a5bd7dE', symObjAddr: 0x43178, symBinAddr: 0x20040, symSize: 0x4C }
  - { offset: 0x2DA97, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$14write_vectored17h6899437ee14ad0e1E', symObjAddr: 0x431C4, symBinAddr: 0x2008C, symSize: 0x4C }
  - { offset: 0x2DB58, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$17is_write_vectored17h9b2366713f7e1db9E', symObjAddr: 0x43210, symBinAddr: 0x200D8, symSize: 0x14 }
  - { offset: 0x2DB72, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$5flush17hd9b43cd37b405524E', symObjAddr: 0x43224, symBinAddr: 0x200EC, symSize: 0x14 }
  - { offset: 0x2DBE3, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix4sync5mutex5Mutex4init17hdf1c4f248d85eb16E, symObjAddr: 0x43238, symBinAddr: 0x20100, symSize: 0x16C }
  - { offset: 0x2DC99, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix4sync5mutex5Mutex4lock4fail17h6819c36a4f036f8cE, symObjAddr: 0x433C0, symBinAddr: 0x351D4, symSize: 0xDC }
  - { offset: 0x2DF03, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix17decode_error_kind17h7a8628d240588c97E, symObjAddr: 0x43C48, symBinAddr: 0x2026C, symSize: 0x30 }
  - { offset: 0x2DF1D, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix14abort_internal17h1f437a045e84c0acE, symObjAddr: 0x43C78, symBinAddr: 0x2029C, symSize: 0xC }
  - { offset: 0x2DF7F, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal6common14small_c_string24run_with_cstr_allocating17h0222687f57b5b8d8E, symObjAddr: 0x378C4, symBinAddr: 0x34EB0, symSize: 0xAC }
  - { offset: 0x2E0DB, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal6common14small_c_string24run_with_cstr_allocating17h89545bf18b96ebe8E, symObjAddr: 0x37BC8, symBinAddr: 0x34F5C, symSize: 0x9C }
  - { offset: 0x2E24A, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal6common14small_c_string24run_with_cstr_allocating17hf540475ca51d3e7bE, symObjAddr: 0x37DE8, symBinAddr: 0x34FF8, symSize: 0xC4 }
  - { offset: 0x2E4AF, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..os_str..bytes..Slice$u20$as$u20$core..fmt..Display$GT$3fmt17h9ac75767c8ed01b2E', symObjAddr: 0x466F4, symBinAddr: 0x20640, symSize: 0xA0 }
  - { offset: 0x2E51E, size: 0x8, addend: 0x0, symName: '__ZN3std3sys11personality3gcc14find_eh_action28_$u7b$$u7b$closure$u7d$$u7d$17heb526fe46b317879E', symObjAddr: 0x44DB8, symBinAddr: 0x202A8, symSize: 0x18 }
  - { offset: 0x2E532, size: 0x8, addend: 0x0, symName: '__ZN3std3sys11personality3gcc14find_eh_action28_$u7b$$u7b$closure$u7d$$u7d$17hdfacf1f5ab590eb6E', symObjAddr: 0x44DD0, symBinAddr: 0x202C0, symSize: 0x18 }
  - { offset: 0x2E560, size: 0x8, addend: 0x0, symName: _rust_eh_personality, symObjAddr: 0x44DE8, symBinAddr: 0x202D8, symSize: 0x368 }
  - { offset: 0x2E88D, size: 0x8, addend: 0x0, symName: __ZN3std3sys11personality5dwarf2eh19read_encoded_offset17he9cb5faabb9c188bE, symObjAddr: 0x3801C, symBinAddr: 0x1D29C, symSize: 0x150 }
  - { offset: 0x2E9EE, size: 0x8, addend: 0x0, symName: '__ZN3std3sys9backtrace10_print_fmt28_$u7b$$u7b$closure$u7d$$u7d$17h1863ed5b66499a89E', symObjAddr: 0x38458, symBinAddr: 0x1D6D8, symSize: 0x30 }
  - { offset: 0x2EA17, size: 0x8, addend: 0x0, symName: '__ZN3std3sys9backtrace10_print_fmt28_$u7b$$u7b$closure$u7d$$u7d$17h49125cd88324db11E', symObjAddr: 0x38488, symBinAddr: 0x1D708, symSize: 0x134 }
  - { offset: 0x2EB2A, size: 0x8, addend: 0x0, symName: '__ZN3std3sys9backtrace10_print_fmt28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17heb5be01c54158345E', symObjAddr: 0x385BC, symBinAddr: 0x1D83C, symSize: 0x25C }
  - { offset: 0x2ED66, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace4lock17h6e02177f0f3558baE, symObjAddr: 0x3816C, symBinAddr: 0x1D3EC, symSize: 0x70 }
  - { offset: 0x2EEAF, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace13BacktraceLock5print17h115149c0b879e5c3E, symObjAddr: 0x381DC, symBinAddr: 0x1D45C, symSize: 0x54 }
  - { offset: 0x2EEFC, size: 0x8, addend: 0x0, symName: '__ZN98_$LT$std..sys..backtrace..BacktraceLock..print..DisplayBacktrace$u20$as$u20$core..fmt..Display$GT$3fmt17h217270392019d164E', symObjAddr: 0x38230, symBinAddr: 0x1D4B0, symSize: 0x228 }
  - { offset: 0x2F480, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace26__rust_end_short_backtrace17h911de07218b69a6cE, symObjAddr: 0x38818, symBinAddr: 0x1DA98, symSize: 0xC }
  - { offset: 0x2F49A, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace15output_filename17h988a0b5399b264d6E, symObjAddr: 0x38830, symBinAddr: 0x1DAA4, symSize: 0xFC }
  - { offset: 0x2F63F, size: 0x8, addend: 0x0, symName: '__ZN3std3sys4sync8once_box16OnceBox$LT$T$GT$10initialize17h482967fb77aa636aE', symObjAddr: 0x3899C, symBinAddr: 0x350BC, symSize: 0xEC }
  - { offset: 0x2F8CE, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue6RwLock21read_unlock_contended17ha1da939695eb68a4E, symObjAddr: 0x479D0, symBinAddr: 0x352B0, symSize: 0x50 }
  - { offset: 0x2FA3D, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue6RwLock16unlock_contended17hb1bda0f6c9583d94E, symObjAddr: 0x47A20, symBinAddr: 0x35300, symSize: 0x50 }
  - { offset: 0x2FB18, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue6RwLock12unlock_queue17h1e38a16940d3a0e6E, symObjAddr: 0x47B84, symBinAddr: 0x2070C, symSize: 0x1D8 }
  - { offset: 0x30257, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue9read_lock17h02feed94edb2ee18E, symObjAddr: 0x47710, symBinAddr: 0x206E0, symSize: 0x2C }
  - { offset: 0x3034C, size: 0x8, addend: 0x0, symName: '__ZN3std3sys12thread_local6native4lazy20Storage$LT$T$C$D$GT$10initialize17h61fa4697cd48c6f6E', symObjAddr: 0x47E24, symBinAddr: 0x35350, symSize: 0xC0 }
  - { offset: 0x30540, size: 0x8, addend: 0x0, symName: __ZN3std3sys12thread_local6native4lazy7destroy17h26d4ae6c1bac34fbE, symObjAddr: 0x47EE4, symBinAddr: 0x208E4, symSize: 0x54 }
  - { offset: 0x306AD, size: 0x8, addend: 0x0, symName: __ZN3std3sys12thread_local5guard5apple6enable9run_dtors17h15022dedc81e5063E, symObjAddr: 0x4807C, symBinAddr: 0x20A7C, symSize: 0x13C }
  - { offset: 0x30C05, size: 0x8, addend: 0x0, symName: __ZN3std3sys12thread_local11destructors4list8register17h51fb0c77f8b321e6E, symObjAddr: 0x47F38, symBinAddr: 0x20938, symSize: 0x144 }
  - { offset: 0x31206, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..io..Write..write_fmt..Adapter$LT$T$GT$$u20$as$u20$core..fmt..Write$GT$9write_str17h0698cdf4cfa713e8E', symObjAddr: 0x2D538, symBinAddr: 0x1BD58, symSize: 0xD8 }
  - { offset: 0x31358, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..io..Write..write_fmt..Adapter$LT$T$GT$$u20$as$u20$core..fmt..Write$GT$9write_str17h58a4ca428f95c900E', symObjAddr: 0x2D738, symBinAddr: 0x1BE30, symSize: 0xD0 }
  - { offset: 0x3150C, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..io..Write..write_fmt..Adapter$LT$T$GT$$u20$as$u20$core..fmt..Write$GT$9write_str17h6eb5a36b1832c95eE', symObjAddr: 0x2D808, symBinAddr: 0x1BF00, symSize: 0x80 }
  - { offset: 0x31673, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_all17h87bc481c06d6b6c7E, symObjAddr: 0x2CC10, symBinAddr: 0x1B430, symSize: 0xB8 }
  - { offset: 0x317A2, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write18write_all_vectored17h0d3be626dc41da2eE, symObjAddr: 0x2CCC8, symBinAddr: 0x1B4E8, symSize: 0x264 }
  - { offset: 0x31C47, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write18write_all_vectored17h0f11e99192ac0da3E, symObjAddr: 0x2CF2C, symBinAddr: 0x1B74C, symSize: 0x2A0 }
  - { offset: 0x320E3, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_fmt17h6564db7565ec4928E, symObjAddr: 0x2D1CC, symBinAddr: 0x1B9EC, symSize: 0x124 }
  - { offset: 0x322F5, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_fmt17h6e471a53cbe47e27E, symObjAddr: 0x2D2F0, symBinAddr: 0x1BB10, symSize: 0x124 }
  - { offset: 0x32507, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_fmt17hb32eaafcfd249a19E, symObjAddr: 0x2D414, symBinAddr: 0x1BC34, symSize: 0x124 }
  - { offset: 0x32731, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$5write17hf1a6df91b3a9c331E', symObjAddr: 0x2731C, symBinAddr: 0x1B09C, symSize: 0x84 }
  - { offset: 0x32875, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$14write_vectored17h935681c1f031c204E', symObjAddr: 0x273A0, symBinAddr: 0x1B120, symSize: 0x174 }
  - { offset: 0x32AC2, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$17is_write_vectored17h4055b148f544ec6cE', symObjAddr: 0x27514, symBinAddr: 0x1B294, symSize: 0x14 }
  - { offset: 0x32ADC, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$9write_all17h8667482891c5bc01E', symObjAddr: 0x27528, symBinAddr: 0x1B2A8, symSize: 0x80 }
  - { offset: 0x32C1A, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$5flush17h1aa70c4ce0391c44E', symObjAddr: 0x275A8, symBinAddr: 0x1B328, symSize: 0x14 }
  - { offset: 0x32D5E, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$std..io..error..Error$u20$as$u20$core..fmt..Debug$GT$3fmt17h4ed4286e752207faE', symObjAddr: 0x26AE0, symBinAddr: 0x1ABF4, symSize: 0x10 }
  - { offset: 0x32D90, size: 0x8, addend: 0x0, symName: '__ZN3std2io5error83_$LT$impl$u20$core..fmt..Debug$u20$for$u20$std..io..error..repr_bitpacked..Repr$GT$3fmt17h65ae83bc7dc9681bE', symObjAddr: 0x26D88, symBinAddr: 0x1AC04, symSize: 0x294 }
  - { offset: 0x32FDB, size: 0x8, addend: 0x0, symName: '__ZN60_$LT$std..io..error..Error$u20$as$u20$core..fmt..Display$GT$3fmt17h9bd80ee0c1811a25E', symObjAddr: 0x2701C, symBinAddr: 0x1AE98, symSize: 0x204 }
  - { offset: 0x3323A, size: 0x8, addend: 0x0, symName: '__ZN62_$LT$std..io..error..ErrorKind$u20$as$u20$core..fmt..Debug$GT$3fmt17h219a5c9d2c7eda43E', symObjAddr: 0x3C098, symBinAddr: 0x1F540, symSize: 0x34 }
  - { offset: 0x33293, size: 0x8, addend: 0x0, symName: __ZN3std2io5stdio22try_set_output_capture17h3c34954bc62c0e61E, symObjAddr: 0x2BA8C, symBinAddr: 0x1B33C, symSize: 0xF4 }
  - { offset: 0x3370A, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli7Context3new17h596fdfe772dc2bb6E, symObjAddr: 0x482F4, symBinAddr: 0x20C44, symSize: 0x2410 }
  - { offset: 0x375CA, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli7Context11find_frames17h5933b96d93a6487fE, symObjAddr: 0x4A704, symBinAddr: 0x23054, symSize: 0x37C }
  - { offset: 0x37A5D, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli5macho6Object5parse17h7c99ba302784e02bE, symObjAddr: 0x4D518, symBinAddr: 0x25E38, symSize: 0xBD0 }
  - { offset: 0x3991D, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli5macho6Object7section17h7775decf80db3f1dE, symObjAddr: 0x4E0E8, symBinAddr: 0x26A08, symSize: 0x3A0 }
  - { offset: 0x39DE8, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli5macho11find_header17hcafb8c4177e72185E, symObjAddr: 0x4D344, symBinAddr: 0x25C64, symSize: 0x1D4 }
  - { offset: 0x3A4ED, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli4mmap17h52426abb42b033dbE, symObjAddr: 0x4AA80, symBinAddr: 0x233D0, symSize: 0x160 }
  - { offset: 0x3AAF2, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli7resolve17h4e500df68d74bbd0E, symObjAddr: 0x4ABE0, symBinAddr: 0x23530, symSize: 0x2734 }
  - { offset: 0x40B23, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize6Symbol4name17hfe1d12d8092fe441E, symObjAddr: 0x3B724, symBinAddr: 0x1EE8C, symSize: 0xE4 }
  - { offset: 0x40C5B, size: 0x8, addend: 0x0, symName: '__ZN79_$LT$std..backtrace_rs..symbolize..SymbolName$u20$as$u20$core..fmt..Display$GT$3fmt17hebc8fecc1f34ea4cE', symObjAddr: 0x3B948, symBinAddr: 0x1EF70, symSize: 0x100 }
  - { offset: 0x40D95, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9backtrace9libunwind5trace8trace_fn17h8dbf4a7a3c414c4fE, symObjAddr: 0x482AC, symBinAddr: 0x20BFC, symSize: 0x48 }
  - { offset: 0x40E69, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs5print17BacktraceFrameFmt21print_raw_with_column17h4b2c8f5f934c00c0E, symObjAddr: 0x3BB48, symBinAddr: 0x1F070, symSize: 0x4D0 }
  - { offset: 0x4110C, size: 0x8, addend: 0x0, symName: __ZN3std6thread7current12init_current17hb7b435bd17801980E, symObjAddr: 0x1F900, symBinAddr: 0x34CF0, symSize: 0x17C }
  - { offset: 0x414BF, size: 0x8, addend: 0x0, symName: __ZN3std6thread6Thread3new17h37ed5d6af114daceE, symObjAddr: 0x20814, symBinAddr: 0x1A510, symSize: 0x164 }
  - { offset: 0x418AA, size: 0x8, addend: 0x0, symName: '__ZN118_$LT$std..thread..thread_name_string..ThreadNameString$u20$as$u20$core..convert..From$LT$alloc..string..String$GT$$GT$4from17h83ebc56d52812e83E', symObjAddr: 0x20718, symBinAddr: 0x1A414, symSize: 0xFC }
  - { offset: 0x41A95, size: 0x8, addend: 0x0, symName: __ZN3std6thread8ThreadId3new9exhausted17h1c0c206286c216f1E, symObjAddr: 0x206E0, symBinAddr: 0x34E6C, symSize: 0x38 }
  - { offset: 0x41B3E, size: 0x8, addend: 0x0, symName: __ZN3std5panic19get_backtrace_style17hb0947c7d43e173b3E, symObjAddr: 0x31B78, symBinAddr: 0x1BF80, symSize: 0x10C }
  - { offset: 0x41B5C, size: 0x8, addend: 0x0, symName: __ZN3std5panic19get_backtrace_style17hb0947c7d43e173b3E, symObjAddr: 0x31B78, symBinAddr: 0x1BF80, symSize: 0x10C }
  - { offset: 0x41B71, size: 0x8, addend: 0x0, symName: __ZN3std5panic19get_backtrace_style17hb0947c7d43e173b3E, symObjAddr: 0x31B78, symBinAddr: 0x1BF80, symSize: 0x10C }
  - { offset: 0x41F5C, size: 0x8, addend: 0x0, symName: __ZN3std3env11current_dir17h378d2e00353b8c55E, symObjAddr: 0x21D9C, symBinAddr: 0x1A674, symSize: 0x1D0 }
  - { offset: 0x422D3, size: 0x8, addend: 0x0, symName: __ZN3std3env7_var_os17h91cf52b19c1ae9d5E, symObjAddr: 0x22698, symBinAddr: 0x1A844, symSize: 0x160 }
  - { offset: 0x42592, size: 0x8, addend: 0x0, symName: __ZN3std2fs11OpenOptions5_open17h060c4979cd359a16E, symObjAddr: 0x24BC4, symBinAddr: 0x1A9A4, symSize: 0x250 }
  - { offset: 0x4277F, size: 0x8, addend: 0x0, symName: __ZN3std5alloc24default_alloc_error_hook17h2ab9053c5916c535E, symObjAddr: 0x39CF4, symBinAddr: 0x1DBA0, symSize: 0x13C }
  - { offset: 0x429C3, size: 0x8, addend: 0x0, symName: ___rdl_alloc, symObjAddr: 0x39E30, symBinAddr: 0x1DCDC, symSize: 0x6C }
  - { offset: 0x42A3F, size: 0x8, addend: 0x0, symName: ___rdl_dealloc, symObjAddr: 0x39E9C, symBinAddr: 0x1DD48, symSize: 0x10 }
  - { offset: 0x42A6F, size: 0x8, addend: 0x0, symName: ___rdl_realloc, symObjAddr: 0x39EAC, symBinAddr: 0x1DD58, symSize: 0xB0 }
  - { offset: 0x42B41, size: 0x8, addend: 0x0, symName: ___rdl_alloc_zeroed, symObjAddr: 0x39F5C, symBinAddr: 0x1DE08, symSize: 0x90 }
  - { offset: 0x42BDF, size: 0x8, addend: 0x0, symName: __ZN3std5alloc8rust_oom17hb46c5aeb63fa3cb5E, symObjAddr: 0x481B8, symBinAddr: 0x20BB8, symSize: 0x2C }
  - { offset: 0x42C2E, size: 0x8, addend: 0x0, symName: ___rg_oom, symObjAddr: 0x481E4, symBinAddr: 0x20BE4, symSize: 0x18 }
  - { offset: 0x42D64, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit32AttributeValue$LT$R$C$Offset$GT$11udata_value17h2b61cc9b6a98fb32E', symObjAddr: 0x14DE0, symBinAddr: 0x10964, symSize: 0x90 }
  - { offset: 0x42DB9, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4unit15parse_attribute17h49f957a62d462029E, symObjAddr: 0x130AC, symBinAddr: 0xEC30, symSize: 0xF74 }
  - { offset: 0x45A9C, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4unit15skip_attributes17h31562773efeee2f1E, symObjAddr: 0x14020, symBinAddr: 0xFBA4, symSize: 0x52C }
  - { offset: 0x464B1, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit18Attribute$LT$R$GT$5value17hbc90a819c686a39aE', symObjAddr: 0x1454C, symBinAddr: 0x100D0, symSize: 0x5DC }
  - { offset: 0x46A35, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit22EntriesCursor$LT$R$GT$10next_entry17hdf28d3cec3327cb6E', symObjAddr: 0x14B28, symBinAddr: 0x106AC, symSize: 0x2B8 }
  - { offset: 0x47021, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit33DebugInfoUnitHeadersIter$LT$R$GT$4next17h34712ebaabdffee4E', symObjAddr: 0x14E70, symBinAddr: 0x109F4, symSize: 0x524 }
  - { offset: 0x48157, size: 0x8, addend: 0x0, symName: '__ZN90_$LT$gimli..read..unit..AttributeValue$LT$R$C$Offset$GT$$u20$as$u20$core..clone..Clone$GT$5clone17h20eda0c245e8425dE', symObjAddr: 0x19914, symBinAddr: 0x14FA0, symSize: 0x68 }
  - { offset: 0x48381, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader11read_offset17hb66bf69f998efb74E, symObjAddr: 0x179B8, symBinAddr: 0x1353C, symSize: 0xBC }
  - { offset: 0x485BB, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader12read_sleb12817hd93e25793436fd15E, symObjAddr: 0x17A74, symBinAddr: 0x135F8, symSize: 0x21C }
  - { offset: 0x486AA, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader12read_uleb12817ha415c724c08e32b4E, symObjAddr: 0x17C90, symBinAddr: 0x13814, symSize: 0xB0 }
  - { offset: 0x4875F, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader17read_sized_offset17hbf387c98074d1971E, symObjAddr: 0x17D40, symBinAddr: 0x138C4, symSize: 0xEC }
  - { offset: 0x48B6C, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line13parse_file_v517hcd59a31a0bc27fa9E, symObjAddr: 0x11F1C, symBinAddr: 0xDAA0, symSize: 0x2B4 }
  - { offset: 0x48CE2, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line15FileEntryFormat5parse17he68be02eb21a6f6cE, symObjAddr: 0x121D0, symBinAddr: 0xDD54, symSize: 0x370 }
  - { offset: 0x49507, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line15parse_attribute17h6dcb28bb0d8f0d55E, symObjAddr: 0x12540, symBinAddr: 0xE0C4, symSize: 0x86C }
  - { offset: 0x4AC9D, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line18parse_directory_v517h7ed9e48bb2fa518bE, symObjAddr: 0x12DAC, symBinAddr: 0xE930, symSize: 0xE8 }
  - { offset: 0x4AD2C, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4line27FileEntry$LT$R$C$Offset$GT$5parse17h0cf5e73c1698de2fE', symObjAddr: 0x12E94, symBinAddr: 0xEA18, symSize: 0x218 }
  - { offset: 0x4B3FC, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read5dwarf14Dwarf$LT$R$GT$11attr_string17hc40cc697c7ad324fE', symObjAddr: 0x17104, symBinAddr: 0x12C88, symSize: 0x1E8 }
  - { offset: 0x4BC34, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read5dwarf13Unit$LT$R$GT$3new17h9c8abef5d8c50a6dE', symObjAddr: 0x15394, symBinAddr: 0x10F18, symSize: 0x1D70 }
  - { offset: 0x4F815, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read5index18UnitIndex$LT$R$GT$5parse17haa9f95b067464b66E', symObjAddr: 0x172EC, symBinAddr: 0x12E70, symSize: 0x6CC }
  - { offset: 0x4FDE0, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read7aranges30ArangeHeader$LT$R$C$Offset$GT$5parse17h20619b15205da501E', symObjAddr: 0x17E2C, symBinAddr: 0x139B0, symSize: 0x2A8 }
  - { offset: 0x5068D, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read8rnglists20RngListIter$LT$R$GT$4next17h7e27fd598c933045E', symObjAddr: 0x180D4, symBinAddr: 0x13C58, symSize: 0xC74 }
  - { offset: 0x53582, size: 0x8, addend: 0x0, symName: '__ZN9addr2line8function17Function$LT$R$GT$14parse_children17he1d2dd4eaf8a7357E', symObjAddr: 0x1DDF8, symBinAddr: 0x193CC, symSize: 0xE88 }
  - { offset: 0x54DB3, size: 0x8, addend: 0x0, symName: __ZN9addr2line8function10name_entry17hd712c6a5749c0b0eE, symObjAddr: 0x1DA8C, symBinAddr: 0x19060, symSize: 0x36C }
  - { offset: 0x55278, size: 0x8, addend: 0x0, symName: __ZN9addr2line8function9name_attr17h41adbb1d91e81904E, symObjAddr: 0x1EC80, symBinAddr: 0x1A254, symSize: 0x1C0 }
  - { offset: 0x55494, size: 0x8, addend: 0x0, symName: '__ZN9addr2line4unit16ResUnit$LT$R$GT$25find_function_or_location17h353b18e237b7b2c9E', symObjAddr: 0x1BBAC, symBinAddr: 0x17180, symSize: 0x39C }
  - { offset: 0x559F2, size: 0x8, addend: 0x0, symName: '__ZN9addr2line4unit16ResUnit$LT$R$GT$25find_function_or_location28_$u7b$$u7b$closure$u7d$$u7d$17h6a4d5a45c96dd11cE', symObjAddr: 0x1BF48, symBinAddr: 0x1751C, symSize: 0x1490 }
  - { offset: 0x58327, size: 0x8, addend: 0x0, symName: '__ZN9addr2line16Context$LT$R$GT$9find_unit17h672f8cef0a6cef5fE', symObjAddr: 0x19A34, symBinAddr: 0x15008, symSize: 0x168 }
  - { offset: 0x584B9, size: 0x8, addend: 0x0, symName: __ZN9addr2line4line11render_file17h56c7df47359e37daE, symObjAddr: 0x19B9C, symBinAddr: 0x15170, symSize: 0x4C0 }
  - { offset: 0x58DCA, size: 0x8, addend: 0x0, symName: __ZN9addr2line4line9LazyLines6borrow17ha6d975a019e23e93E, symObjAddr: 0x1A05C, symBinAddr: 0x15630, symSize: 0x1B50 }
  - { offset: 0x5C1FF, size: 0x8, addend: 0x0, symName: '__ZN9addr2line6lookup30LoopingLookup$LT$T$C$L$C$F$GT$10new_lookup17h590d4f5c6451cf3cE', symObjAddr: 0x1D654, symBinAddr: 0x18C28, symSize: 0x438 }
  - { offset: 0x5C792, size: 0x8, addend: 0x0, symName: '__ZN9addr2line5frame18FrameIter$LT$R$GT$4next17h4512ac685f8f4647E', symObjAddr: 0x1D3D8, symBinAddr: 0x189AC, symSize: 0x27C }
  - { offset: 0x5CC22, size: 0x8, addend: 0x0, symName: __ZN6object4read7archive13ArchiveMember5parse17he23e9c7a2dcdb639E, symObjAddr: 0x19204, symBinAddr: 0x14934, symSize: 0x41C }
  - { offset: 0x5D7B7, size: 0x8, addend: 0x0, symName: __ZN6object4read7archive23parse_bsd_extended_name17hc2b3d25eef9eeec9E, symObjAddr: 0x19620, symBinAddr: 0x14D50, symSize: 0x250 }
  - { offset: 0x5E582, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_0, symObjAddr: 0x4E488, symBinAddr: 0x26DA8, symSize: 0x24 }
  - { offset: 0x5E59A, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_1, symObjAddr: 0x4E4AC, symBinAddr: 0x26DCC, symSize: 0x8 }
  - { offset: 0x5E5B2, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_2, symObjAddr: 0x4E4B4, symBinAddr: 0x26DD4, symSize: 0x10 }
  - { offset: 0x5E5CA, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_4, symObjAddr: 0x4E4CC, symBinAddr: 0x26DE4, symSize: 0x8 }
  - { offset: 0x5E5E2, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_5, symObjAddr: 0x4E4D4, symBinAddr: 0x26DEC, symSize: 0xC }
  - { offset: 0x5E617, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr49drop_in_place$LT$panic_unwind..imp..Exception$GT$17h82c2656372694058E', symObjAddr: 0x0, symBinAddr: 0x26DF8, symSize: 0x78 }
  - { offset: 0x5E63B, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr49drop_in_place$LT$panic_unwind..imp..Exception$GT$17h82c2656372694058E', symObjAddr: 0x0, symBinAddr: 0x26DF8, symSize: 0x78 }
  - { offset: 0x5E740, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr74drop_in_place$LT$alloc..boxed..Box$LT$panic_unwind..imp..Exception$GT$$GT$17h401304109b323233E', symObjAddr: 0x78, symBinAddr: 0x26E70, symSize: 0x8C }
  - { offset: 0x5E9C6, size: 0x8, addend: 0x0, symName: __ZN12panic_unwind3imp5panic17exception_cleanup17h71dcf2e4fc4aa769E, symObjAddr: 0x214, symBinAddr: 0x26FA4, symSize: 0x18 }
  - { offset: 0x5E9E1, size: 0x8, addend: 0x0, symName: ___rust_start_panic, symObjAddr: 0x16C, symBinAddr: 0x26EFC, symSize: 0xA8 }
  - { offset: 0x5EB7F, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$core..alloc..layout..LayoutError$u20$as$u20$core..fmt..Debug$GT$3fmt17h847390e718c251f1E', symObjAddr: 0x1C0, symBinAddr: 0x2F774, symSize: 0x20 }
  - { offset: 0x5EE8D, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec17capacity_overflow17hf0a187d8de84083bE, symObjAddr: 0x1E0, symBinAddr: 0x2F794, symSize: 0x34 }
  - { offset: 0x5EECD, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h207f2a977af11d07E', symObjAddr: 0x214, symBinAddr: 0x2F7C8, symSize: 0xAC }
  - { offset: 0x5F03A, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17hf907109019113e80E', symObjAddr: 0x2C0, symBinAddr: 0x354C4, symSize: 0xB0 }
  - { offset: 0x5F139, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec11finish_grow17h91993be477efd13cE, symObjAddr: 0x370, symBinAddr: 0x35574, symSize: 0x88 }
  - { offset: 0x5F1AF, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec12handle_error17h7f0cbdf2396d2d52E, symObjAddr: 0x3F8, symBinAddr: 0x355FC, symSize: 0x18 }
  - { offset: 0x5F29F, size: 0x8, addend: 0x0, symName: __ZN5alloc5alloc18handle_alloc_error17h14470fb7117d6c37E, symObjAddr: 0x410, symBinAddr: 0x35614, symSize: 0x18 }
  - { offset: 0x5F326, size: 0x8, addend: 0x0, symName: '__ZN93_$LT$alloc..collections..btree..mem..replace..PanicGuard$u20$as$u20$core..ops..drop..Drop$GT$4drop17h4dbb92e5b56c174aE', symObjAddr: 0xE10, symBinAddr: 0x2F874, symSize: 0xC }
  - { offset: 0x5F463, size: 0x8, addend: 0x0, symName: __ZN5alloc6string6String15from_utf8_lossy17h7d31b366d97043dfE, symObjAddr: 0x2F28, symBinAddr: 0x2FAFC, symSize: 0x238 }
  - { offset: 0x5F9AD, size: 0x8, addend: 0x0, symName: '__ZN98_$LT$alloc..string..String$u20$as$u20$core..convert..From$LT$alloc..borrow..Cow$LT$str$GT$$GT$$GT$4from17hde800050cd4f6a42E', symObjAddr: 0x4D48, symBinAddr: 0x2FD34, symSize: 0xBC }
  - { offset: 0x5FBD3, size: 0x8, addend: 0x0, symName: '__ZN72_$LT$$RF$str$u20$as$u20$alloc..ffi..c_str..CString..new..SpecNewImpl$GT$13spec_new_impl17h37cb9b6572c7448bE', symObjAddr: 0xEA8, symBinAddr: 0x2F880, symSize: 0x11C }
  - { offset: 0x5FE14, size: 0x8, addend: 0x0, symName: __ZN5alloc3ffi5c_str7CString19_from_vec_unchecked17h8dccb886daea0b28E, symObjAddr: 0x1110, symBinAddr: 0x2F99C, symSize: 0x160 }
  - { offset: 0x6013E, size: 0x8, addend: 0x0, symName: __ZN5alloc4sync32arcinner_layout_for_value_layout17h83c76efa9058bc15E, symObjAddr: 0x516C, symBinAddr: 0x2FDF0, symSize: 0x74 }
  - { offset: 0x60716, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index26slice_start_index_len_fail8do_panic7runtime17h9ddc6a7ecebba1e9E, symObjAddr: 0x1B12C, symBinAddr: 0x3475C, symSize: 0x50 }
  - { offset: 0x6074A, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index26slice_start_index_len_fail17hbef4cde757a23982E, symObjAddr: 0xEAAC, symBinAddr: 0x359D4, symSize: 0xC }
  - { offset: 0x60790, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index24slice_end_index_len_fail8do_panic7runtime17h385f2d9d99153a75E, symObjAddr: 0x1B17C, symBinAddr: 0x347AC, symSize: 0x50 }
  - { offset: 0x607C4, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index24slice_end_index_len_fail17ha150b75f771021fbE, symObjAddr: 0xEAB8, symBinAddr: 0x359E0, symSize: 0xC }
  - { offset: 0x6080A, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index22slice_index_order_fail8do_panic7runtime17he0b28f8ab2cd970eE, symObjAddr: 0x1B1CC, symBinAddr: 0x347FC, symSize: 0x50 }
  - { offset: 0x6083E, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index22slice_index_order_fail17h7613890af586566aE, symObjAddr: 0xEAC4, symBinAddr: 0x359EC, symSize: 0xC }
  - { offset: 0x60BC6, size: 0x8, addend: 0x0, symName: __ZN4core5slice6memchr14memchr_aligned17h6296e9c99467795dE, symObjAddr: 0xE3FC, symBinAddr: 0x32144, symSize: 0xE8 }
  - { offset: 0x60CE7, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift11sqrt_approx17hea6fce954f57e78dE, symObjAddr: 0xE604, symBinAddr: 0x3222C, symSize: 0x38 }
  - { offset: 0x60D64, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort22panic_on_ord_violation17h87158ad44664865dE, symObjAddr: 0xE63C, symBinAddr: 0x3599C, symSize: 0x38 }
  - { offset: 0x60EFC, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17hf009c24fe723987dE', symObjAddr: 0xE0B4, symBinAddr: 0x31EC4, symSize: 0x20 }
  - { offset: 0x6108A, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter12pad_integral17hc0a697f6bef9bc99E, symObjAddr: 0xB878, symBinAddr: 0x30EBC, symSize: 0x3E8 }
  - { offset: 0x61382, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter3pad17hc45fb048fd353932E, symObjAddr: 0xBCDC, symBinAddr: 0x31320, symSize: 0x370 }
  - { offset: 0x6183D, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter9write_str17h325369b1c335017bE, symObjAddr: 0xC49C, symBinAddr: 0x31690, symSize: 0x1C }
  - { offset: 0x61850, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter12debug_struct17h01892abf43f6400aE, symObjAddr: 0xC4B8, symBinAddr: 0x316AC, symSize: 0x3C }
  - { offset: 0x6188F, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter26debug_struct_field1_finish17h9a6aad483cd91d8bE, symObjAddr: 0xC4F4, symBinAddr: 0x316E8, symSize: 0xD0 }
  - { offset: 0x61966, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter26debug_struct_field2_finish17h50e0aebce8a58786E, symObjAddr: 0xC5C4, symBinAddr: 0x317B8, symSize: 0x104 }
  - { offset: 0x61A3D, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter11debug_tuple17hc10fd19a9736e89bE, symObjAddr: 0xCBD8, symBinAddr: 0x318BC, symSize: 0x50 }
  - { offset: 0x61A91, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter25debug_tuple_field2_finish17h4e3f51f71b24019cE, symObjAddr: 0xCD90, symBinAddr: 0x3190C, symSize: 0x1E0 }
  - { offset: 0x61D4A, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter10debug_list17h017ce85e0805b545E, symObjAddr: 0xDB70, symBinAddr: 0x31AEC, symSize: 0x48 }
  - { offset: 0x61DED, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders11DebugStruct5field17h2f0dca0d2df6974aE, symObjAddr: 0x84A0, symBinAddr: 0x306B4, symSize: 0x1B8 }
  - { offset: 0x61FEB, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders11DebugStruct6finish17he06e5b5d674203a3E, symObjAddr: 0x871C, symBinAddr: 0x3086C, symSize: 0x80 }
  - { offset: 0x620E8, size: 0x8, addend: 0x0, symName: '__ZN68_$LT$core..fmt..builders..PadAdapter$u20$as$u20$core..fmt..Write$GT$9write_str17hfaa313c4119f7ed4E', symObjAddr: 0x81D0, symBinAddr: 0x303E4, symSize: 0x250 }
  - { offset: 0x62396, size: 0x8, addend: 0x0, symName: '__ZN68_$LT$core..fmt..builders..PadAdapter$u20$as$u20$core..fmt..Write$GT$10write_char17hde86c020ea7d2eadE', symObjAddr: 0x8420, symBinAddr: 0x30634, symSize: 0x80 }
  - { offset: 0x62415, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders10DebugTuple5field17hd1be49f816fc5cc6E, symObjAddr: 0x879C, symBinAddr: 0x308EC, symSize: 0x14C }
  - { offset: 0x6259F, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders10DebugTuple6finish17h27cf27fbab1a8a2aE, symObjAddr: 0x89A8, symBinAddr: 0x30A38, symSize: 0xB0 }
  - { offset: 0x626E7, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders8DebugSet5entry17h30a73ad81f14a0f2E, symObjAddr: 0x8A58, symBinAddr: 0x30AE8, symSize: 0x14C }
  - { offset: 0x6288A, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders9DebugList6finish17h106b57f1a22a675eE, symObjAddr: 0x8D7C, symBinAddr: 0x30C34, symSize: 0x4C }
  - { offset: 0x62943, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u32$GT$3fmt17hbf1c5d9b6583f29fE', symObjAddr: 0x19B78, symBinAddr: 0x3438C, symSize: 0x1C }
  - { offset: 0x6296A, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u64$GT$3fmt17h339ac4429cb37bf0E', symObjAddr: 0x19CE0, symBinAddr: 0x344F4, symSize: 0x1C }
  - { offset: 0x62990, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17hf55f78073eda7290E', symObjAddr: 0x19B94, symBinAddr: 0x343A8, symSize: 0x28 }
  - { offset: 0x629F3, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp51_$LT$impl$u20$core..fmt..Display$u20$for$u20$u8$GT$3fmt17h4f4911bf2ed93697E', symObjAddr: 0x198A4, symBinAddr: 0x342DC, symSize: 0xB0 }
  - { offset: 0x62AB9, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp21_$LT$impl$u20$u32$GT$4_fmt17h2969b048172ebb80E', symObjAddr: 0x19BBC, symBinAddr: 0x343D0, symSize: 0x124 }
  - { offset: 0x62B9A, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp21_$LT$impl$u20$u64$GT$4_fmt17h99108e3731ddbd8bE', symObjAddr: 0x19D24, symBinAddr: 0x34510, symSize: 0x130 }
  - { offset: 0x62DA8, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17ha0cbda09c03c5041E', symObjAddr: 0x18C80, symBinAddr: 0x340DC, symSize: 0x80 }
  - { offset: 0x62E4E, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17heb47dcda93036a36E', symObjAddr: 0x18D00, symBinAddr: 0x3415C, symSize: 0x80 }
  - { offset: 0x62EF4, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i64$GT$3fmt17hf3c2c8f0bf48439fE', symObjAddr: 0x18E60, symBinAddr: 0x341DC, symSize: 0x80 }
  - { offset: 0x62F9A, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i64$GT$3fmt17h4be4db71703d1dabE', symObjAddr: 0x18EE0, symBinAddr: 0x3425C, symSize: 0x80 }
  - { offset: 0x63033, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num52_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i8$GT$3fmt17h7bff1931c71ac012E', symObjAddr: 0x188C0, symBinAddr: 0x33FDC, symSize: 0x80 }
  - { offset: 0x630E0, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num52_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i8$GT$3fmt17h8dba84f19f74308cE', symObjAddr: 0x18940, symBinAddr: 0x3405C, symSize: 0x80 }
  - { offset: 0x6319D, size: 0x8, addend: 0x0, symName: '__ZN57_$LT$core..fmt..Formatter$u20$as$u20$core..fmt..Write$GT$10write_char17h712a7a5b14df20b9E', symObjAddr: 0xDCC0, symBinAddr: 0x31B34, symSize: 0x1C }
  - { offset: 0x631CF, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17h3451af32630eef07E, symObjAddr: 0xB4E4, symBinAddr: 0x30C80, symSize: 0x1C }
  - { offset: 0x63236, size: 0x8, addend: 0x0, symName: '__ZN59_$LT$core..fmt..Arguments$u20$as$u20$core..fmt..Display$GT$3fmt17h5a31f19e8734f16eE', symObjAddr: 0xB658, symBinAddr: 0x30C9C, symSize: 0x18 }
  - { offset: 0x63265, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5write17he22fcab56bd3ec61E, symObjAddr: 0xB670, symBinAddr: 0x30CB4, symSize: 0x208 }
  - { offset: 0x63474, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter12pad_integral12write_prefix17h37c97ab3a7046bd2E, symObjAddr: 0xBC60, symBinAddr: 0x312A4, symSize: 0x7C }
  - { offset: 0x634B9, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h54e81ade5e5de57dE', symObjAddr: 0x1AF30, symBinAddr: 0x34640, symSize: 0x1C }
  - { offset: 0x634CC, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h91eecf8d28a7cefeE', symObjAddr: 0x1AF4C, symBinAddr: 0x3465C, symSize: 0xE4 }
  - { offset: 0x63624, size: 0x8, addend: 0x0, symName: '__ZN40_$LT$str$u20$as$u20$core..fmt..Debug$GT$3fmt17hd8879d369458d53aE', symObjAddr: 0xDD40, symBinAddr: 0x31B50, symSize: 0x374 }
  - { offset: 0x63A7B, size: 0x8, addend: 0x0, symName: '__ZN41_$LT$char$u20$as$u20$core..fmt..Debug$GT$3fmt17ha35a21095fe6b2aeE', symObjAddr: 0xE0D4, symBinAddr: 0x31EE4, symSize: 0xAC }
  - { offset: 0x63B80, size: 0x8, addend: 0x0, symName: '__ZN43_$LT$char$u20$as$u20$core..fmt..Display$GT$3fmt17h3667dcfbeb89f1b7E', symObjAddr: 0xE180, symBinAddr: 0x31F90, symSize: 0xF0 }
  - { offset: 0x63C05, size: 0x8, addend: 0x0, symName: __ZN4core3fmt17pointer_fmt_inner17ha05a056f67b250baE, symObjAddr: 0xE270, symBinAddr: 0x32080, symSize: 0xC4 }
  - { offset: 0x63CC1, size: 0x8, addend: 0x0, symName: '__ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h09a056bd82e73243E', symObjAddr: 0x1B044, symBinAddr: 0x34740, symSize: 0x1C }
  - { offset: 0x63E53, size: 0x8, addend: 0x0, symName: __ZN4core6option13unwrap_failed17h84f662fc2717d129E, symObjAddr: 0x7B40, symBinAddr: 0x35670, symSize: 0x1C }
  - { offset: 0x63E7E, size: 0x8, addend: 0x0, symName: '__ZN71_$LT$core..ops..range..Range$LT$Idx$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17h8610e207370f6ef7E', symObjAddr: 0x442C, symBinAddr: 0x2FE64, symSize: 0x1FC }
  - { offset: 0x6434D, size: 0x8, addend: 0x0, symName: __ZN4core6result13unwrap_failed17h2bf1684417afbe6bE, symObjAddr: 0x80FC, symBinAddr: 0x35940, symSize: 0x5C }
  - { offset: 0x645EC, size: 0x8, addend: 0x0, symName: __ZN4core3str7pattern11StrSearcher3new17habaad989c72fb7a8E, symObjAddr: 0x10248, symBinAddr: 0x32E7C, symSize: 0x904 }
  - { offset: 0x64890, size: 0x8, addend: 0x0, symName: __ZN4core3str5count14do_count_chars17hc555c670c9b10e3bE, symObjAddr: 0xF128, symBinAddr: 0x324A8, symSize: 0x724 }
  - { offset: 0x64BE1, size: 0x8, addend: 0x0, symName: __ZN4core3str5count23char_count_general_case17h0a6ef21a4825751aE, symObjAddr: 0xF84C, symBinAddr: 0x32BCC, symSize: 0x2B0 }
  - { offset: 0x64D3B, size: 0x8, addend: 0x0, symName: __ZN4core3str6traits23str_index_overflow_fail17h6ba0069023d715bdE, symObjAddr: 0x10214, symBinAddr: 0x359F8, symSize: 0x34 }
  - { offset: 0x64D70, size: 0x8, addend: 0x0, symName: __ZN4core3str8converts9from_utf817h5b7081f8e3c98111E, symObjAddr: 0xECA0, symBinAddr: 0x32264, symSize: 0x244 }
  - { offset: 0x64DF1, size: 0x8, addend: 0x0, symName: '__ZN87_$LT$core..str..lossy..Utf8Chunks$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hc0bb2f8797014ed2E', symObjAddr: 0x10F30, symBinAddr: 0x33780, symSize: 0x1DC }
  - { offset: 0x64FF9, size: 0x8, addend: 0x0, symName: __ZN4core3str16slice_error_fail17h8b847e853482f553E, symObjAddr: 0x111D8, symBinAddr: 0x35A2C, symSize: 0xC }
  - { offset: 0x65013, size: 0x8, addend: 0x0, symName: __ZN4core3str19slice_error_fail_rt17hd494714b9640772bE, symObjAddr: 0x11218, symBinAddr: 0x3395C, symSize: 0x390 }
  - { offset: 0x652B9, size: 0x8, addend: 0x0, symName: '__ZN4core4char7methods22_$LT$impl$u20$char$GT$16escape_debug_ext17hbfb443f01c84243eE', symObjAddr: 0x50B8, symBinAddr: 0x30084, symSize: 0x260 }
  - { offset: 0x65500, size: 0x8, addend: 0x0, symName: '__ZN63_$LT$core..cell..BorrowMutError$u20$as$u20$core..fmt..Debug$GT$3fmt17h4f632190c10a8f91E', symObjAddr: 0x4F3C, symBinAddr: 0x30060, symSize: 0x24 }
  - { offset: 0x6555D, size: 0x8, addend: 0x0, symName: __ZN4core4cell22panic_already_borrowed17ha893d1f06b8b63deE, symObjAddr: 0x4F80, symBinAddr: 0x3562C, symSize: 0x44 }
  - { offset: 0x655AC, size: 0x8, addend: 0x0, symName: __ZN4core7unicode12unicode_data15grapheme_extend11lookup_slow17hbcdf8b5a821a33ffE, symObjAddr: 0x1C978, symBinAddr: 0x34870, symSize: 0x174 }
  - { offset: 0x65793, size: 0x8, addend: 0x0, symName: __ZN4core7unicode12unicode_data2cc6lookup17he25d7ab2d2fa0f97E, symObjAddr: 0x1C954, symBinAddr: 0x3484C, symSize: 0x24 }
  - { offset: 0x657FB, size: 0x8, addend: 0x0, symName: __ZN4core7unicode9printable12is_printable17haae76351f6525626E, symObjAddr: 0x12144, symBinAddr: 0x33CEC, symSize: 0x2C0 }
  - { offset: 0x65C39, size: 0x8, addend: 0x0, symName: __ZN4core3ffi5c_str4CStr19from_bytes_with_nul17h10506affebd054b1E, symObjAddr: 0x5700, symBinAddr: 0x302E4, symSize: 0x100 }
  - { offset: 0x65D18, size: 0x8, addend: 0x0, symName: __ZN4core9panicking9panic_fmt17h6a4014bec58fba4fE, symObjAddr: 0x7C94, symBinAddr: 0x3568C, symSize: 0x20 }
  - { offset: 0x65D5B, size: 0x8, addend: 0x0, symName: __ZN4core9panicking18panic_nounwind_fmt17hc1d4bd06ab2bbbabE, symObjAddr: 0x7CB4, symBinAddr: 0x356AC, symSize: 0x3C }
  - { offset: 0x65DA0, size: 0x8, addend: 0x0, symName: __ZN4core9panicking5panic17h3ce9043ca357f318E, symObjAddr: 0x7CF0, symBinAddr: 0x356E8, symSize: 0x34 }
  - { offset: 0x65DCF, size: 0x8, addend: 0x0, symName: __ZN4core9panicking14panic_nounwind17h96b8083b1a8e2dbdE, symObjAddr: 0x7D24, symBinAddr: 0x3571C, symSize: 0x3C }
  - { offset: 0x65DFE, size: 0x8, addend: 0x0, symName: __ZN4core9panicking26panic_nounwind_nobacktrace17h984deb6b7d0bc614E, symObjAddr: 0x7D60, symBinAddr: 0x35758, symSize: 0x3C }
  - { offset: 0x65E2D, size: 0x8, addend: 0x0, symName: __ZN4core9panicking18panic_bounds_check17h1ecfa3d00f46f81cE, symObjAddr: 0x7DE4, symBinAddr: 0x35794, symSize: 0x50 }
  - { offset: 0x65E5F, size: 0x8, addend: 0x0, symName: __ZN4core9panicking19panic_cannot_unwind17ha5edf28dd2569dc8E, symObjAddr: 0x7EF8, symBinAddr: 0x357E4, symSize: 0x18 }
  - { offset: 0x65E7A, size: 0x8, addend: 0x0, symName: __ZN4core9panicking16panic_in_cleanup17hb960b8c5dea287d4E, symObjAddr: 0x7F10, symBinAddr: 0x357FC, symSize: 0x18 }
  - { offset: 0x65E94, size: 0x8, addend: 0x0, symName: __ZN4core9panicking13assert_failed17h2af7932842d934b6E, symObjAddr: 0x7F8C, symBinAddr: 0x35814, symSize: 0x24 }
  - { offset: 0x65EAF, size: 0x8, addend: 0x0, symName: __ZN4core9panicking19assert_failed_inner17hafd35c69af0230bfE, symObjAddr: 0x7FF4, symBinAddr: 0x35838, symSize: 0x108 }
  - { offset: 0x65EFC, size: 0x8, addend: 0x0, symName: __ZN4core9panicking11panic_const23panic_const_div_by_zero17h0422238fbdaad2d7E, symObjAddr: 0x179B8, symBinAddr: 0x35A38, symSize: 0x34 }
  - { offset: 0x65F2B, size: 0x8, addend: 0x0, symName: __ZN4core9panicking11panic_const23panic_const_rem_by_zero17h723f338bbd7af040E, symObjAddr: 0x179EC, symBinAddr: 0x35A6C, symSize: 0x34 }
  - { offset: 0x65FA1, size: 0x8, addend: 0x0, symName: __ZN4core5alloc6layout6Layout19is_size_align_valid17h054933e8a3b2a17cE, symObjAddr: 0x126A0, symBinAddr: 0x33FAC, symSize: 0x30 }
  - { offset: 0x65FF9, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_0, symObjAddr: 0x1D194, symBinAddr: 0x349E4, symSize: 0x10 }
...
