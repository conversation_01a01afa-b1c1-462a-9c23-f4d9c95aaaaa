---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/HedgeFund/solana_tx_utils/target/release/deps/libsolana_frozen_abi_macro-b91efcc8eb27cf74.dylib'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '__ZN100_$LT$proc_macro..bridge..client..TokenStream$u20$as$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$GT$6encode17hdca390377cf6ce3dE', symObjAddr: 0x0, symBinAddr: 0x898, symSize: 0x50 }
  - { offset: 0xEB, size: 0x8, addend: 0x0, symName: '__ZN78_$LT$std..io..error..repr_bitpacked..Repr$u20$as$u20$core..ops..drop..Drop$GT$4drop28_$u7b$$u7b$closure$u7d$$u7d$17h424ab15a24641c98E', symObjAddr: 0x3A94, symBinAddr: 0x42D4, symSize: 0x1C }
  - { offset: 0x1A0, size: 0x8, addend: 0x0, symName: '__ZN78_$LT$std..io..error..repr_bitpacked..Repr$u20$as$u20$core..ops..drop..Drop$GT$4drop17h8afffae957493be8E', symObjAddr: 0x3A5C, symBinAddr: 0x429C, symSize: 0x38 }
  - { offset: 0x1C9, size: 0x8, addend: 0x0, symName: __ZN3std2io5error14repr_bitpacked11decode_repr17h1c06d07039952b6fE, symObjAddr: 0x2178, symBinAddr: 0x2A10, symSize: 0x230 }
  - { offset: 0x46E, size: 0x8, addend: 0x0, symName: __ZN3std2io5error14repr_bitpacked14kind_from_prim17hc287fde7b16b750bE, symObjAddr: 0x23A8, symBinAddr: 0x2C40, symSize: 0x608 }
  - { offset: 0xF68, size: 0x8, addend: 0x0, symName: __ZN3std9panicking3try7do_call17h01caf2755c8e483bE, symObjAddr: 0x2B10, symBinAddr: 0x33A8, symSize: 0x50 }
  - { offset: 0x1030, size: 0x8, addend: 0x0, symName: __ZN3std9panicking3try7do_call17h3ba79b957caca129E, symObjAddr: 0x2B60, symBinAddr: 0x33F8, symSize: 0x50 }
  - { offset: 0x10F8, size: 0x8, addend: 0x0, symName: __ZN3std9panicking3try7do_call17hc5dce28662f8dcc9E, symObjAddr: 0x2BB0, symBinAddr: 0x3448, symSize: 0x50 }
  - { offset: 0x11C0, size: 0x8, addend: 0x0, symName: __ZN3std9panicking3try8do_catch17h37d16752d9714561E, symObjAddr: 0x2C00, symBinAddr: 0x3498, symSize: 0x70 }
  - { offset: 0x1255, size: 0x8, addend: 0x0, symName: __ZN3std9panicking3try8do_catch17h62e22a36315f5adcE, symObjAddr: 0x2C70, symBinAddr: 0x3508, symSize: 0x70 }
  - { offset: 0x12EA, size: 0x8, addend: 0x0, symName: __ZN3std9panicking3try8do_catch17ha2cd2894c33e78a1E, symObjAddr: 0x2CE0, symBinAddr: 0x3578, symSize: 0x70 }
  - { offset: 0x1588, size: 0x8, addend: 0x0, symName: '__ZN3std6thread5local41LocalKey$LT$core..cell..Cell$LT$T$GT$$GT$7replace28_$u7b$$u7b$closure$u7d$$u7d$17he5ece1cdb5e79be0E', symObjAddr: 0x2AE8, symBinAddr: 0x3380, symSize: 0x28 }
  - { offset: 0x2247, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr101drop_in_place$LT$std..io..error..ErrorData$LT$alloc..boxed..Box$LT$std..io..error..Custom$GT$$GT$$GT$17hfdd15722361a8abcE', symObjAddr: 0x2E48, symBinAddr: 0x36E0, symSize: 0x48 }
  - { offset: 0x2276, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr110drop_in_place$LT$$LP$proc_macro..bridge..client..TokenStream$C$proc_macro..bridge..client..TokenStream$RP$$GT$17hccdd2917e326af5cE', symObjAddr: 0x2E90, symBinAddr: 0x3728, symSize: 0x60 }
  - { offset: 0x22A5, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr118drop_in_place$LT$alloc..boxed..Box$LT$dyn$u20$core..error..Error$u2b$core..marker..Send$u2b$core..marker..Sync$GT$$GT$17h59a6df3b3f73199eE', symObjAddr: 0x2EF0, symBinAddr: 0x3788, symSize: 0x80 }
  - { offset: 0x22D4, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17hb98ced253f0a6ae3E', symObjAddr: 0x2F80, symBinAddr: 0x3808, symSize: 0x24 }
  - { offset: 0x2303, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h226e4cd32af43cddE', symObjAddr: 0x2FA4, symBinAddr: 0x382C, symSize: 0x24 }
  - { offset: 0x2332, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr43drop_in_place$LT$std..io..error..Custom$GT$17h4a43d6df2cd8031eE', symObjAddr: 0x2FC8, symBinAddr: 0x3850, symSize: 0x24 }
  - { offset: 0x2361, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr445drop_in_place$LT$proc_macro..bridge..client..run_client$LT$proc_macro..bridge..client..TokenStream$C$core..option..Option$LT$proc_macro..bridge..client..TokenStream$GT$$C$proc_macro..bridge..client..Client$LT$proc_macro..TokenStream$C$proc_macro..TokenStream$GT$..expand1$LT$solana_frozen_abi_macro..derive_abi_sample$GT$..$u7b$$u7b$closure$u7d$$u7d$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h9a8fb5ea6bbec767E', symObjAddr: 0x2FEC, symBinAddr: 0x3874, symSize: 0x24 }
  - { offset: 0x2390, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr44drop_in_place$LT$proc_macro..TokenStream$GT$17h72d6fe4f3abf81f0E', symObjAddr: 0x3010, symBinAddr: 0x3898, symSize: 0x24 }
  - { offset: 0x23BF, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr451drop_in_place$LT$proc_macro..bridge..client..run_client$LT$proc_macro..bridge..client..TokenStream$C$core..option..Option$LT$proc_macro..bridge..client..TokenStream$GT$$C$proc_macro..bridge..client..Client$LT$proc_macro..TokenStream$C$proc_macro..TokenStream$GT$..expand1$LT$solana_frozen_abi_macro..derive_abi_enum_visitor$GT$..$u7b$$u7b$closure$u7d$$u7d$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h453f5ea7d3069fabE', symObjAddr: 0x3034, symBinAddr: 0x38BC, symSize: 0x24 }
  - { offset: 0x23EE, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17h5e00b8e6a4c7b2b9E', symObjAddr: 0x3058, symBinAddr: 0x38E0, symSize: 0x5C }
  - { offset: 0x241D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr522drop_in_place$LT$proc_macro..bridge..client..run_client$LT$$LP$proc_macro..bridge..client..TokenStream$C$proc_macro..bridge..client..TokenStream$RP$$C$core..option..Option$LT$proc_macro..bridge..client..TokenStream$GT$$C$proc_macro..bridge..client..Client$LT$$LP$proc_macro..TokenStream$C$proc_macro..TokenStream$RP$$C$proc_macro..TokenStream$GT$..expand2$LT$solana_frozen_abi_macro..frozen_abi$GT$..$u7b$$u7b$closure$u7d$$u7d$..$u7b$$u7b$closure$u7d$$u7d$$GT$..$u7b$$u7b$closure$u7d$$u7d$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h46f9774940796242E', symObjAddr: 0x30B4, symBinAddr: 0x393C, symSize: 0x24 }
  - { offset: 0x244C, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr53drop_in_place$LT$alloc..raw_vec..RawVec$LT$u8$GT$$GT$17h5706e49a6f8ce2e3E', symObjAddr: 0x30D8, symBinAddr: 0x3960, symSize: 0x24 }
  - { offset: 0x247B, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr55drop_in_place$LT$proc_macro..bridge..buffer..Buffer$GT$17h525363707e0bba1dE', symObjAddr: 0x30FC, symBinAddr: 0x3984, symSize: 0x24 }
  - { offset: 0x24AA, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr55drop_in_place$LT$proc_macro..bridge..client..Bridge$GT$17hf04b80648b252a84E', symObjAddr: 0x3120, symBinAddr: 0x39A8, symSize: 0x24 }
  - { offset: 0x24D9, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr57drop_in_place$LT$std..io..error..repr_bitpacked..Repr$GT$17hda26a24664a3b25bE', symObjAddr: 0x3144, symBinAddr: 0x39CC, symSize: 0x24 }
  - { offset: 0x2508, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr58drop_in_place$LT$proc_macro..bridge..rpc..PanicMessage$GT$17h015171156d653fcdE', symObjAddr: 0x3168, symBinAddr: 0x39F0, symSize: 0x50 }
  - { offset: 0x2537, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr60drop_in_place$LT$proc_macro..bridge..client..TokenStream$GT$17h2dbb6f6d2e9d1e0fE', symObjAddr: 0x31B8, symBinAddr: 0x3A40, symSize: 0x24 }
  - { offset: 0x2566, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr68drop_in_place$LT$alloc..boxed..Box$LT$std..io..error..Custom$GT$$GT$17h3e59ba92f09ee210E', symObjAddr: 0x31DC, symBinAddr: 0x3A64, symSize: 0x60 }
  - { offset: 0x2595, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr74drop_in_place$LT$proc_macro..bridge..client..state..set..RestoreOnDrop$GT$17h7df16910fe528e9dE', symObjAddr: 0x323C, symBinAddr: 0x3AC4, symSize: 0x24 }
  - { offset: 0x25C4, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr82drop_in_place$LT$core..cell..RefCell$LT$proc_macro..bridge..client..Bridge$GT$$GT$17h4f1154f92fa035b8E', symObjAddr: 0x3260, symBinAddr: 0x3AE8, symSize: 0x24 }
  - { offset: 0x25F3, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr85drop_in_place$LT$core..cell..UnsafeCell$LT$proc_macro..bridge..client..Bridge$GT$$GT$17hb57c02e3f6c2d1dfE', symObjAddr: 0x3284, symBinAddr: 0x3B0C, symSize: 0x24 }
  - { offset: 0x2622, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr88drop_in_place$LT$core..option..Option$LT$proc_macro..bridge..client..TokenStream$GT$$GT$17h9c15b0375413621bE', symObjAddr: 0x32A8, symBinAddr: 0x3B30, symSize: 0x44 }
  - { offset: 0x3542, size: 0x8, addend: 0x0, symName: '__ZN115_$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$9call_once17h06fe1010fc5b43f5E', symObjAddr: 0x20D0, symBinAddr: 0x2968, symSize: 0x38 }
  - { offset: 0x359A, size: 0x8, addend: 0x0, symName: '__ZN115_$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$9call_once17h4310770f4b2e180cE', symObjAddr: 0x2108, symBinAddr: 0x29A0, symSize: 0x38 }
  - { offset: 0x35F2, size: 0x8, addend: 0x0, symName: '__ZN115_$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$9call_once17hcddfb97d31051a41E', symObjAddr: 0x2140, symBinAddr: 0x29D8, symSize: 0x38 }
  - { offset: 0x380E, size: 0x8, addend: 0x0, symName: '__ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$15copy_from_slice17h599a72ac03b704bdE', symObjAddr: 0x3378, symBinAddr: 0x3BB8, symSize: 0x84 }
  - { offset: 0x3A00, size: 0x8, addend: 0x0, symName: '__ZN110_$LT$core..ops..range..RangeFrom$LT$usize$GT$$u20$as$u20$core..slice..index..SliceIndex$LT$$u5b$T$u5d$$GT$$GT$5index17h5d8d5740b4d76c80E', symObjAddr: 0x205C, symBinAddr: 0x28F4, symSize: 0x74 }
  - { offset: 0x4359, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function2Fn4call17h3ac3f8be762bf949E, symObjAddr: 0x2D50, symBinAddr: 0x35E8, symSize: 0x30 }
  - { offset: 0x439D, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function2Fn4call17h5c825552f3f57d02E, symObjAddr: 0x2D80, symBinAddr: 0x3618, symSize: 0x28 }
  - { offset: 0x43E1, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function2Fn4call17h9ad1bfe557d0ae2bE, symObjAddr: 0x2DA8, symBinAddr: 0x3640, symSize: 0x28 }
  - { offset: 0x442B, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function6FnOnce9call_once17h0e71401f9a2d2c7aE, symObjAddr: 0x2DD0, symBinAddr: 0x3668, symSize: 0x4C }
  - { offset: 0x446F, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function6FnOnce9call_once17he97f2eef50099abbE, symObjAddr: 0x2E1C, symBinAddr: 0x36B4, symSize: 0x2C }
  - { offset: 0x44CC, size: 0x8, addend: 0x0, symName: __ZN4core4hint21unreachable_unchecked18precondition_check17hb3870820bc8d2d3dE, symObjAddr: 0x335C, symBinAddr: 0x3B9C, symSize: 0x1C }
  - { offset: 0x4501, size: 0x8, addend: 0x0, symName: '__ZN4core3str21_$LT$impl$u20$str$GT$5parse17h58783d6b21b89d76E', symObjAddr: 0x3334, symBinAddr: 0x3B74, symSize: 0x28 }
  - { offset: 0x4829, size: 0x8, addend: 0x0, symName: '__ZN100_$LT$proc_macro..bridge..client..TokenStream$u20$as$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$GT$6encode17hdca390377cf6ce3dE', symObjAddr: 0x0, symBinAddr: 0x898, symSize: 0x50 }
  - { offset: 0x493A, size: 0x8, addend: 0x0, symName: '__ZN103_$LT$proc_macro..bridge..client..TokenStream$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17h6cb16f430b2f8c46E', symObjAddr: 0xAC, symBinAddr: 0x944, symSize: 0x2C }
  - { offset: 0x4B1E, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client10run_client28_$u7b$$u7b$closure$u7d$$u7d$17h30b58e437c4d06e9E', symObjAddr: 0x1238, symBinAddr: 0x1AD0, symSize: 0x324 }
  - { offset: 0x4D81, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client10run_client28_$u7b$$u7b$closure$u7d$$u7d$17hdf21a656d8fee212E', symObjAddr: 0x155C, symBinAddr: 0x1DF4, symSize: 0x324 }
  - { offset: 0x4FE4, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client10run_client28_$u7b$$u7b$closure$u7d$$u7d$17heac03696b45c40f9E', symObjAddr: 0x1880, symBinAddr: 0x2118, symSize: 0x344 }
  - { offset: 0x524C, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client10run_client28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17h478a1bbc1f7e8e40E', symObjAddr: 0x1BC4, symBinAddr: 0x245C, symSize: 0x28 }
  - { offset: 0x52A6, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client10run_client28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17hb19647c917480c20E', symObjAddr: 0x1BEC, symBinAddr: 0x2484, symSize: 0x28 }
  - { offset: 0x5300, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client10run_client28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17hc3fe94fea6e80e59E', symObjAddr: 0x1C14, symBinAddr: 0x24AC, symSize: 0x24 }
  - { offset: 0x53ED, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client63Client$LT$proc_macro..TokenStream$C$proc_macro..TokenStream$GT$7expand128_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17h2dd42cf13eadb2cfE', symObjAddr: 0x1F88, symBinAddr: 0x2820, symSize: 0x30 }
  - { offset: 0x5435, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client63Client$LT$proc_macro..TokenStream$C$proc_macro..TokenStream$GT$7expand128_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17hb4f544c1b1253cf3E', symObjAddr: 0x1FB8, symBinAddr: 0x2850, symSize: 0x30 }
  - { offset: 0x547E, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client63Client$LT$proc_macro..TokenStream$C$proc_macro..TokenStream$GT$7expand128_$u7b$$u7b$closure$u7d$$u7d$17ha28cd320e379fa27E', symObjAddr: 0x1F28, symBinAddr: 0x27C0, symSize: 0x30 }
  - { offset: 0x54C7, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client63Client$LT$proc_macro..TokenStream$C$proc_macro..TokenStream$GT$7expand128_$u7b$$u7b$closure$u7d$$u7d$17hfbf2f7b5db458d27E', symObjAddr: 0x1F58, symBinAddr: 0x27F0, symSize: 0x30 }
  - { offset: 0x5538, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6client10run_client17h65c84e5963c90a92E, symObjAddr: 0xBE4, symBinAddr: 0x147C, symSize: 0x21C }
  - { offset: 0x571A, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client97Client$LT$$LP$proc_macro..TokenStream$C$proc_macro..TokenStream$RP$$C$proc_macro..TokenStream$GT$7expand228_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17hec867a2d63df885eE', symObjAddr: 0x2018, symBinAddr: 0x28B0, symSize: 0x44 }
  - { offset: 0x578D, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client97Client$LT$$LP$proc_macro..TokenStream$C$proc_macro..TokenStream$RP$$C$proc_macro..TokenStream$GT$7expand228_$u7b$$u7b$closure$u7d$$u7d$17hf1dbda44b27b5893E', symObjAddr: 0x1FE8, symBinAddr: 0x2880, symSize: 0x30 }
  - { offset: 0x57EB, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6client10run_client17h6e650b71a290bafcE, symObjAddr: 0xE00, symBinAddr: 0x1698, symSize: 0x21C }
  - { offset: 0x59AB, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6client10run_client17hf7d51446a4e8a385E, symObjAddr: 0x101C, symBinAddr: 0x18B4, symSize: 0x21C }
  - { offset: 0x5BC8, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client5state12BRIDGE_STATE29_$u7b$$u7b$constant$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17h6e2c4681703e8b70E', symObjAddr: 0x1C38, symBinAddr: 0x24D0, symSize: 0x34 }
  - { offset: 0x5C03, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6client5state3set17h23697b336e8501f1E, symObjAddr: 0x1C6C, symBinAddr: 0x2504, symSize: 0xEC }
  - { offset: 0x5CD9, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6client5state3set17hd6b4ea593e24924eE, symObjAddr: 0x1D58, symBinAddr: 0x25F0, symSize: 0xE8 }
  - { offset: 0x5DAE, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6client5state3set17hfff1e573e0c910e5E, symObjAddr: 0x1E40, symBinAddr: 0x26D8, symSize: 0xE8 }
  - { offset: 0x5EA4, size: 0x8, addend: 0x0, symName: '__ZN96_$LT$proc_macro..bridge..client..Span$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17h40e6a47062812beeE', symObjAddr: 0x3AB0, symBinAddr: 0x42F0, symSize: 0x2C }
  - { offset: 0x611C, size: 0x8, addend: 0x0, symName: '__ZN76_$LT$proc_macro..bridge..buffer..Buffer$u20$as$u20$core..ops..drop..Drop$GT$4drop17h737ff9647956c2edE', symObjAddr: 0x376C, symBinAddr: 0x3FAC, symSize: 0x8C }
  - { offset: 0x634D, size: 0x8, addend: 0x0, symName: '__ZN102_$LT$core..num..nonzero..NonZero$LT$u32$GT$$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17h4f5b27f0046f0623E', symObjAddr: 0x50, symBinAddr: 0x8E8, symSize: 0x5C }
  - { offset: 0x651F, size: 0x8, addend: 0x0, symName: '__ZN67_$LT$u32$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17hf18cb2853b0031e6E', symObjAddr: 0x34C4, symBinAddr: 0x3D04, symSize: 0x108 }
  - { offset: 0x666C, size: 0x8, addend: 0x0, symName: '__ZN68_$LT$$RF$str$u20$as$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$GT$6encode17hfdd1d94dea1dcfbcE', symObjAddr: 0x35CC, symBinAddr: 0x3E0C, symSize: 0x38 }
  - { offset: 0x66BF, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$$LP$$RP$$u20$as$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$GT$6encode17h1d38de0a60975166E', symObjAddr: 0x3604, symBinAddr: 0x3E44, symSize: 0x14 }
  - { offset: 0x6706, size: 0x8, addend: 0x0, symName: '__ZN77_$LT$$LP$A$C$B$RP$$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17h03cf9228f69839f3E', symObjAddr: 0x37F8, symBinAddr: 0x4038, symSize: 0x80 }
  - { offset: 0x675B, size: 0x8, addend: 0x0, symName: '__ZN77_$LT$$LP$A$C$B$RP$$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17h06fcf3b086ea1358E', symObjAddr: 0x3878, symBinAddr: 0x40B8, symSize: 0x90 }
  - { offset: 0x67B0, size: 0x8, addend: 0x0, symName: '__ZN77_$LT$$LP$A$C$B$RP$$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17hfcbe4a3ddf674a5cE', symObjAddr: 0x3908, symBinAddr: 0x4148, symSize: 0x84 }
  - { offset: 0x683D, size: 0x8, addend: 0x0, symName: '__ZN77_$LT$$RF$$u5b$u8$u5d$$u20$as$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$GT$6encode17h026ddcdbc474a735E', symObjAddr: 0x398C, symBinAddr: 0x41CC, symSize: 0xD0 }
  - { offset: 0x693F, size: 0x8, addend: 0x0, symName: '__ZN98_$LT$proc_macro..bridge..rpc..PanicMessage$u20$as$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$GT$6encode17h3907f39ad466ed59E', symObjAddr: 0x3ADC, symBinAddr: 0x431C, symSize: 0x128 }
  - { offset: 0x6B06, size: 0x8, addend: 0x0, symName: '__ZN107_$LT$proc_macro..bridge..ExpnGlobals$LT$Span$GT$$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17hd400db0db7ec3365E', symObjAddr: 0xD8, symBinAddr: 0x970, symSize: 0xB0 }
  - { offset: 0x6B58, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge100_$LT$impl$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$u20$for$u20$core..option..Option$LT$T$GT$$GT$6encode17h4967918565e30d8eE', symObjAddr: 0x188, symBinAddr: 0xA20, symSize: 0xCC }
  - { offset: 0x6BFE, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge100_$LT$impl$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$u20$for$u20$core..option..Option$LT$T$GT$$GT$6encode17heb82bed0debb013cE', symObjAddr: 0x254, symBinAddr: 0xAEC, symSize: 0xD8 }
  - { offset: 0x6CAA, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge104_$LT$impl$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$u20$for$u20$core..result..Result$LT$T$C$E$GT$$GT$6encode17h277d5b424aea4ff9E', symObjAddr: 0x32C, symBinAddr: 0xBC4, symSize: 0x12C }
  - { offset: 0x6D6F, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge104_$LT$impl$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$u20$for$u20$core..result..Result$LT$T$C$E$GT$$GT$6encode17h39bd57bf32532ec2E', symObjAddr: 0x458, symBinAddr: 0xCF0, symSize: 0x108 }
  - { offset: 0x6E3E, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge14selfless_reify31reify_to_extern_c_fn_hrt_bridge7wrapper17h0bd6518979125f9dE, symObjAddr: 0x560, symBinAddr: 0xDF8, symSize: 0x5C }
  - { offset: 0x6E9A, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge14selfless_reify31reify_to_extern_c_fn_hrt_bridge7wrapper17h113c8e169dd15aa5E, symObjAddr: 0x5BC, symBinAddr: 0xE54, symSize: 0x5C }
  - { offset: 0x6EF6, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge14selfless_reify31reify_to_extern_c_fn_hrt_bridge7wrapper17hc8b600fdad2dbf11E, symObjAddr: 0x618, symBinAddr: 0xEB0, symSize: 0x5C }
  - { offset: 0x6FA5, size: 0x8, addend: 0x0, symName: '__ZN57_$LT$proc_macro..LexError$u20$as$u20$core..fmt..Debug$GT$3fmt17heda16d4a39b289a9E', symObjAddr: 0x33FC, symBinAddr: 0x3C3C, symSize: 0x40 }
  - { offset: 0x6FF6, size: 0x8, addend: 0x0, symName: ___rustc_proc_macro_decls_3c94d57438b2e6a2__, symObjAddr: 0x46C8, symBinAddr: 0x501A8, symSize: 0x0 }
  - { offset: 0x7004, size: 0x8, addend: 0x0, symName: __ZN23solana_frozen_abi_macro10frozen_abi17h76b62a426ab8149fE, symObjAddr: 0x3C04, symBinAddr: 0x4444, symSize: 0x34 }
  - { offset: 0x703E, size: 0x8, addend: 0x0, symName: __ZN23solana_frozen_abi_macro17derive_abi_sample17h85daab71652683d9E, symObjAddr: 0x3C38, symBinAddr: 0x4478, symSize: 0xFC }
  - { offset: 0x709D, size: 0x8, addend: 0x0, symName: __ZN23solana_frozen_abi_macro23derive_abi_enum_visitor17hd3261dc8bf7b0e48E, symObjAddr: 0x3D34, symBinAddr: 0x4574, symSize: 0xFC }
  - { offset: 0x7351, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6buffer6Buffer17extend_from_array17h13956cfc4fbc4c73E, symObjAddr: 0x674, symBinAddr: 0xF0C, symSize: 0x16C }
  - { offset: 0x7517, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6buffer6Buffer17extend_from_array17h7e81e58084a8cabcE, symObjAddr: 0x7E0, symBinAddr: 0x1078, symSize: 0x16C }
  - { offset: 0x76C3, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6buffer6Buffer17extend_from_slice17h023fa4903109fffcE, symObjAddr: 0x94C, symBinAddr: 0x11E4, symSize: 0x164 }
  - { offset: 0x787F, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6buffer6Buffer4push17h2f128d4b4b4b9b83E, symObjAddr: 0xAB0, symBinAddr: 0x1348, symSize: 0x134 }
  - { offset: 0x7B24, size: 0x8, addend: 0x0, symName: '__ZN63_$LT$alloc..alloc..Global$u20$as$u20$core..alloc..Allocator$GT$10deallocate17h3c4f5b0a38c7c35aE', symObjAddr: 0x343C, symBinAddr: 0x3C7C, symSize: 0x88 }
  - { offset: 0x7CF2, size: 0x8, addend: 0x0, symName: '__ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hab9741fac8f63c4aE', symObjAddr: 0x3618, symBinAddr: 0x3E58, symSize: 0xB4 }
  - { offset: 0x7E29, size: 0x8, addend: 0x0, symName: '__ZN72_$LT$alloc..boxed..Box$LT$T$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$4drop17hecf7247ac6643a5fE', symObjAddr: 0x36CC, symBinAddr: 0x3F0C, symSize: 0xA0 }
  - { offset: 0x85AB, size: 0x8, addend: 0x0, symName: '__ZN3std6thread5local17LocalKey$LT$T$GT$4with17hed9b931f205bacc5E', symObjAddr: 0x29B0, symBinAddr: 0x3248, symSize: 0x54 }
  - { offset: 0x8664, size: 0x8, addend: 0x0, symName: '__ZN3std6thread5local17LocalKey$LT$T$GT$8try_with17hb5e82fb3bea41297E', symObjAddr: 0x2A04, symBinAddr: 0x329C, symSize: 0xE4 }
  - { offset: 0x8E66, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h00e91ac68e4f9259E', symObjAddr: 0x0, symBinAddr: 0x46D8, symSize: 0x30 }
  - { offset: 0x8E82, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h00e91ac68e4f9259E', symObjAddr: 0x0, symBinAddr: 0x46D8, symSize: 0x30 }
  - { offset: 0x8E9B, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17he96b59fd084ca801E', symObjAddr: 0x30, symBinAddr: 0x4708, symSize: 0x30 }
  - { offset: 0x8EB4, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17hebbb805b587cd9efE', symObjAddr: 0x60, symBinAddr: 0x4738, symSize: 0x30 }
  - { offset: 0x910F, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr131drop_in_place$LT$proc_macro..bridge..client..maybe_install_panic_hook..$u7b$$u7b$closure$u7d$$u7d$..$u7b$$u7b$closure$u7d$$u7d$$GT$17h0e3979d570eb173bE', symObjAddr: 0x8C4, symBinAddr: 0x4A94, symSize: 0x6C }
  - { offset: 0x92CA, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr146drop_in_place$LT$std..sys..thread_local..native..lazy..State$LT$core..cell..RefCell$LT$proc_macro..bridge..symbol..Interner$GT$$C$$LP$$RP$$GT$$GT$17h0b9a70164f63af52E', symObjAddr: 0x930, symBinAddr: 0x4B00, symSize: 0xD0 }
  - { offset: 0x9648, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr39drop_in_place$LT$std..path..PathBuf$GT$17h82ac2e015bc59d4eE', symObjAddr: 0xF14, symBinAddr: 0x4BD0, symSize: 0x28 }
  - { offset: 0x96F0, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17hc636f1771f84a95eE', symObjAddr: 0xFB0, symBinAddr: 0x4BF8, symSize: 0x2C }
  - { offset: 0x9786, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr91drop_in_place$LT$alloc..boxed..Box$LT$dyn$u20$core..any..Any$u2b$core..marker..Send$GT$$GT$17h3d11ff82bce02370E', symObjAddr: 0x1344, symBinAddr: 0x4C24, symSize: 0x78 }
  - { offset: 0x9B5B, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h5336ab964fe941f7E', symObjAddr: 0x4DC, symBinAddr: 0x4918, symSize: 0xD4 }
  - { offset: 0x9CB6, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17heb332f3d8c854584E', symObjAddr: 0x5B0, symBinAddr: 0x49EC, symSize: 0xA8 }
  - { offset: 0x9EF9, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h5dc7f32a2fde3835E', symObjAddr: 0x2D4, symBinAddr: 0x4858, symSize: 0x64 }
  - { offset: 0x9F21, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17ha21392b589eb0d7aE', symObjAddr: 0x354, symBinAddr: 0x48BC, symSize: 0x30 }
  - { offset: 0x9FA3, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num52_$LT$impl$u20$core..fmt..Debug$u20$for$u20$usize$GT$3fmt17h6def11b803266407E', symObjAddr: 0x494, symBinAddr: 0x48EC, symSize: 0x2C }
  - { offset: 0xA194, size: 0x8, addend: 0x0, symName: '__ZN64_$LT$core..str..error..Utf8Error$u20$as$u20$core..fmt..Debug$GT$3fmt17ha1b6df96ab5da701E', symObjAddr: 0x1C58, symBinAddr: 0x4C9C, symSize: 0x70 }
  - { offset: 0xA363, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6symbol6Symbol14invalidate_all17h48e54b34105a7737E, symObjAddr: 0x444C, symBinAddr: 0x5504, symSize: 0x188 }
  - { offset: 0xA8A4, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client24maybe_install_panic_hook28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17h908313cbb667be53E', symObjAddr: 0x3410, symBinAddr: 0x5198, symSize: 0x50 }
  - { offset: 0xA8FF, size: 0x8, addend: 0x0, symName: '__ZN95_$LT$proc_macro..bridge..client..state..set..RestoreOnDrop$u20$as$u20$core..ops..drop..Drop$GT$4drop17h3802d75c6d5bb03dE', symObjAddr: 0x2CD8, symBinAddr: 0x4DB4, symSize: 0x28 }
  - { offset: 0xAA0D, size: 0x8, addend: 0x0, symName: '__ZN81_$LT$proc_macro..bridge..client..TokenStream$u20$as$u20$core..ops..drop..Drop$GT$4drop17hd4fee2277e6da64eE', symObjAddr: 0x15370, symBinAddr: 0x5994, symSize: 0x2C }
  - { offset: 0xAAAB, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge6client6Bridge4with28_$u7b$$u7b$closure$u7d$$u7d$17h1e83f68ca6ee9f7eE', symObjAddr: 0x2D00, symBinAddr: 0x4DDC, symSize: 0x354 }
  - { offset: 0xB26D, size: 0x8, addend: 0x0, symName: __ZN10proc_macro6bridge6client24maybe_install_panic_hook17he267c413f1c002ecE, symObjAddr: 0x33A8, symBinAddr: 0x5130, symSize: 0x68 }
  - { offset: 0xB303, size: 0x8, addend: 0x0, symName: '__ZN107_$LT$proc_macro..bridge..buffer..Buffer$u20$as$u20$core..convert..From$LT$alloc..vec..Vec$LT$u8$GT$$GT$$GT$4from7reserve17h3c09f5b161bb8d4fE', symObjAddr: 0x24A0, symBinAddr: 0x4D0C, symSize: 0x80 }
  - { offset: 0xB3C4, size: 0x8, addend: 0x0, symName: '__ZN107_$LT$proc_macro..bridge..buffer..Buffer$u20$as$u20$core..convert..From$LT$alloc..vec..Vec$LT$u8$GT$$GT$$GT$4from4drop17h9061ca228ccd3141E', symObjAddr: 0x2520, symBinAddr: 0x4D8C, symSize: 0x28 }
  - { offset: 0xB5CD, size: 0x8, addend: 0x0, symName: '__ZN71_$LT$$RF$str$u20$as$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$GT$6decode17he4f27fbf937c31bdE', symObjAddr: 0x3460, symBinAddr: 0x51E8, symSize: 0xB4 }
  - { offset: 0xB766, size: 0x8, addend: 0x0, symName: '__ZN155_$LT$proc_macro..bridge..rpc..PanicMessage$u20$as$u20$core..convert..From$LT$alloc..boxed..Box$LT$dyn$u20$core..any..Any$u2b$core..marker..Send$GT$$GT$$GT$4from17h9b544d125d221d73E', symObjAddr: 0x3514, symBinAddr: 0x529C, symSize: 0x180 }
  - { offset: 0xB959, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge3rpc165_$LT$impl$u20$core..convert..From$LT$proc_macro..bridge..rpc..PanicMessage$GT$$u20$for$u20$alloc..boxed..Box$LT$dyn$u20$core..any..Any$u2b$core..marker..Send$GT$$GT$4from17h42c767f426a2a841E', symObjAddr: 0x3694, symBinAddr: 0x541C, symSize: 0xE8 }
  - { offset: 0xBBC3, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge107_$LT$impl$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$u20$for$u20$core..result..Result$LT$T$C$E$GT$$GT$6decode17h2d78b3b1b9c74403E', symObjAddr: 0x17F7C, symBinAddr: 0x66F8, symSize: 0xFC }
  - { offset: 0xBD55, size: 0x8, addend: 0x0, symName: '__ZN10proc_macro6bridge103_$LT$impl$u20$proc_macro..bridge..rpc..DecodeMut$LT$S$GT$$u20$for$u20$core..option..Option$LT$T$GT$$GT$6decode17h0b8d4c397c7457e5E', symObjAddr: 0x17D7C, symBinAddr: 0x65FC, symSize: 0xFC }
  - { offset: 0xBF9D, size: 0x8, addend: 0x0, symName: '__ZN97_$LT$proc_macro..bridge..api_tags..Method$u20$as$u20$proc_macro..bridge..rpc..Encode$LT$S$GT$$GT$6encode17h08c2666ca52e29e7E', symObjAddr: 0x16B68, symBinAddr: 0x59C0, symSize: 0xC3C }
  - { offset: 0xCB72, size: 0x8, addend: 0x0, symName: '__ZN70_$LT$proc_macro..TokenStream$u20$as$u20$core..str..traits..FromStr$GT$8from_str17hd7301b477336bebbE', symObjAddr: 0x6CFC, symBinAddr: 0x568C, symSize: 0x308 }
  - { offset: 0xD2A7, size: 0x8, addend: 0x0, symName: '__ZN3std3sys12thread_local6native4lazy20Storage$LT$T$C$D$GT$10initialize17hab7900152ed63120E', symObjAddr: 0x90, symBinAddr: 0x3A4E0, symSize: 0x144 }
  - { offset: 0xD434, size: 0x8, addend: 0x0, symName: __ZN3std3sys12thread_local6native4lazy7destroy17h98bd2e6c531b7de3E, symObjAddr: 0x1D4, symBinAddr: 0x4768, symSize: 0x48 }
  - { offset: 0xD544, size: 0x8, addend: 0x0, symName: '__ZN3std4sync6poison4once4Once9call_once28_$u7b$$u7b$closure$u7d$$u7d$17h4894a5d1747f7aeaE', symObjAddr: 0x21C, symBinAddr: 0x47B0, symSize: 0xA8 }
  - { offset: 0xD93A, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec11finish_grow17h2340b3229cc3b623E, symObjAddr: 0x18B4, symBinAddr: 0x3A624, symSize: 0x88 }
  - { offset: 0xD9C6, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17h53d2f48ffd624bf0E', symObjAddr: 0x1B8C, symBinAddr: 0x3A6AC, symSize: 0xB0 }
  - { offset: 0xE1A0, size: 0x8, addend: 0x0, symName: '__ZN5alloc11collections5btree3map25IntoIter$LT$K$C$V$C$A$GT$10dying_next17h1d88f957430bad1dE', symObjAddr: 0xECA8, symBinAddr: 0x11810, symSize: 0x224 }
  - { offset: 0xE587, size: 0x8, addend: 0x0, symName: '__ZN5alloc11collections5btree3map25IntoIter$LT$K$C$V$C$A$GT$10dying_next17hfaa5a005d7e39d8eE', symObjAddr: 0xF0F0, symBinAddr: 0x11A34, symSize: 0x224 }
  - { offset: 0xF18F, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h095dc4f4c91ff6b9E', symObjAddr: 0x114B8, symBinAddr: 0x12044, symSize: 0xD0 }
  - { offset: 0xF2E3, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h3a19a4ab2b92359dE', symObjAddr: 0x11658, symBinAddr: 0x12114, symSize: 0xC0 }
  - { offset: 0xF437, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h40071feec6bbd372E', symObjAddr: 0x11718, symBinAddr: 0x121D4, symSize: 0xD0 }
  - { offset: 0xF58B, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h4a39c02819ac1667E', symObjAddr: 0x117E8, symBinAddr: 0x122A4, symSize: 0xD0 }
  - { offset: 0xF6DF, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h4a92fc1fe476e477E', symObjAddr: 0x118B8, symBinAddr: 0x12374, symSize: 0xC0 }
  - { offset: 0xF833, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h4fe34a41a9b5eb13E', symObjAddr: 0x11978, symBinAddr: 0x12434, symSize: 0xC0 }
  - { offset: 0xF987, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h51ebdaf7922ccb0fE', symObjAddr: 0x11A38, symBinAddr: 0x124F4, symSize: 0xD0 }
  - { offset: 0xFADB, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h5fa1addd75b9c473E', symObjAddr: 0x11B08, symBinAddr: 0x125C4, symSize: 0xC0 }
  - { offset: 0xFC2F, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h86dfd012ac0923eeE', symObjAddr: 0x11BC8, symBinAddr: 0x12684, symSize: 0xCC }
  - { offset: 0xFD83, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h94e52b7d38af74feE', symObjAddr: 0x11C94, symBinAddr: 0x12750, symSize: 0xCC }
  - { offset: 0xFED7, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17hbaffb1ea2421dae5E', symObjAddr: 0x11D60, symBinAddr: 0x1281C, symSize: 0xCC }
  - { offset: 0x105B4, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec11finish_grow17hd37f20dcfa74548bE, symObjAddr: 0x11430, symBinAddr: 0x3A8D4, symSize: 0x88 }
  - { offset: 0x1064C, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17h59c9ec03f2a10329E', symObjAddr: 0x11E2C, symBinAddr: 0x3A95C, symSize: 0xF0 }
  - { offset: 0x116DA, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h382cff577ce4ab40E', symObjAddr: 0x10FD0, symBinAddr: 0x11C58, symSize: 0x100 }
  - { offset: 0x11A09, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h5608352b196d8cb3E', symObjAddr: 0x110D0, symBinAddr: 0x11D58, symSize: 0xA0 }
  - { offset: 0x11D2C, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h5ede859a095ca78aE', symObjAddr: 0x111E4, symBinAddr: 0x11DF8, symSize: 0x78 }
  - { offset: 0x11EF7, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h7cf1d5f987884bcfE', symObjAddr: 0x1125C, symBinAddr: 0x11E70, symSize: 0xC0 }
  - { offset: 0x1221B, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h90e2e762cfbfaa1eE', symObjAddr: 0x1131C, symBinAddr: 0x11F30, symSize: 0x44 }
  - { offset: 0x122F7, size: 0x8, addend: 0x0, symName: '__ZN5alloc4sync16Arc$LT$T$C$A$GT$9drop_slow17h9659d3e2cd59fce8E', symObjAddr: 0x11360, symBinAddr: 0x11F74, symSize: 0xD0 }
  - { offset: 0x12828, size: 0x8, addend: 0x0, symName: '__ZN64_$LT$alloc..ffi..c_str..NulError$u20$as$u20$core..fmt..Debug$GT$3fmt17h18ce3d950e35fa91E', symObjAddr: 0x18D64, symBinAddr: 0x19730, symSize: 0x4C }
  - { offset: 0x12BCB, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$alloc..string..String$u20$as$u20$core..fmt..Debug$GT$3fmt17hdbacb71d50eff926E', symObjAddr: 0xD6B8, symBinAddr: 0x1161C, symSize: 0x1C }
  - { offset: 0x12C85, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$alloc..string..String$u20$as$u20$core..fmt..Write$GT$10write_char17h67c485e816708459E', symObjAddr: 0xD6D4, symBinAddr: 0x11638, symSize: 0x158 }
  - { offset: 0x12EE8, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$alloc..string..String$u20$as$u20$core..fmt..Write$GT$9write_str17h4bc2793cacc1d696E', symObjAddr: 0xD82C, symBinAddr: 0x11790, symSize: 0x80 }
  - { offset: 0x13040, size: 0x8, addend: 0x0, symName: '__ZN60_$LT$alloc..string..String$u20$as$u20$core..fmt..Display$GT$3fmt17ha0099383a97e68c2E', symObjAddr: 0x18D48, symBinAddr: 0x19714, symSize: 0x1C }
  - { offset: 0x1420F, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr107drop_in_place$LT$core..pin..Pin$LT$alloc..boxed..Box$LT$std..sys..pal..unix..sync..mutex..Mutex$GT$$GT$$GT$17hc07c140671e79445E', symObjAddr: 0x1CB0, symBinAddr: 0x7028, symSize: 0x2C }
  - { offset: 0x14300, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr108drop_in_place$LT$alloc..collections..btree..map..BTreeMap$LT$u64$C$gimli..read..abbrev..Abbreviation$GT$$GT$17haa74a0211d9ed923E', symObjAddr: 0x1CDC, symBinAddr: 0x7054, symSize: 0xB0 }
  - { offset: 0x144D0, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr109drop_in_place$LT$alloc..sync..Arc$LT$std..sync..poison..mutex..Mutex$LT$alloc..vec..Vec$LT$u8$GT$$GT$$GT$$GT$17h61b1cac8a1fede90E', symObjAddr: 0x1D8C, symBinAddr: 0x7104, symSize: 0x30 }
  - { offset: 0x14564, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr118drop_in_place$LT$std..io..Write..write_fmt..Adapter$LT$std..io..cursor..Cursor$LT$$RF$mut$u20$$u5b$u8$u5d$$GT$$GT$$GT$17h5c63d0c0bee45058E', symObjAddr: 0x1E54, symBinAddr: 0x7134, symSize: 0x20 }
  - { offset: 0x1459A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr123drop_in_place$LT$addr2line..Context$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17hc6d5be859abd218dE', symObjAddr: 0x1E74, symBinAddr: 0x7154, symSize: 0x70 }
  - { offset: 0x146ED, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr129drop_in_place$LT$addr2line..unit..ResUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h3200663a94d9b140E', symObjAddr: 0x202C, symBinAddr: 0x71C4, symSize: 0xE4 }
  - { offset: 0x1490A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr129drop_in_place$LT$addr2line..unit..SupUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17hea048140b03f01afE', symObjAddr: 0x2110, symBinAddr: 0x72A8, symSize: 0x54 }
  - { offset: 0x149E1, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr130drop_in_place$LT$addr2line..unit..ResUnits$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17he56c193886414dbaE', symObjAddr: 0x2164, symBinAddr: 0x72FC, symSize: 0xD0 }
  - { offset: 0x14B85, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr130drop_in_place$LT$addr2line..unit..SupUnits$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h74d4d2bc946c0774E', symObjAddr: 0x2234, symBinAddr: 0x73CC, symSize: 0xE4 }
  - { offset: 0x14D41, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr130drop_in_place$LT$gimli..read..dwarf..Dwarf$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h733659cc5e33a4cbE', symObjAddr: 0x2318, symBinAddr: 0x74B0, symSize: 0x58 }
  - { offset: 0x14E0A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr131drop_in_place$LT$$u5b$core..option..Option$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$$u5d$$GT$17h1b55ae3732cae68aE', symObjAddr: 0x2370, symBinAddr: 0x7508, symSize: 0xCC }
  - { offset: 0x14EB3, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr131drop_in_place$LT$alloc..boxed..Box$LT$dyn$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$u2b$Output$u20$$u3d$$u20$$LP$$RP$$GT$$GT$17h4c6a3b22dc3f9555E', symObjAddr: 0x243C, symBinAddr: 0x75D4, symSize: 0x78 }
  - { offset: 0x14F9D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr137drop_in_place$LT$gimli..read..dwarf..Unit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$usize$GT$$GT$17h90351d6bab11b82cE', symObjAddr: 0x2500, symBinAddr: 0x764C, symSize: 0x54 }
  - { offset: 0x15092, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x76A0, symSize: 0x64 }
  - { offset: 0x150A9, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x76A0, symSize: 0x64 }
  - { offset: 0x150BE, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x76A0, symSize: 0x64 }
  - { offset: 0x150D3, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr138drop_in_place$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$17h0f9458c797bd90e9E', symObjAddr: 0x2554, symBinAddr: 0x76A0, symSize: 0x64 }
  - { offset: 0x15213, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr144drop_in_place$LT$alloc..vec..Vec$LT$core..option..Option$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$$GT$$GT$17hd5b41028903956eeE', symObjAddr: 0x25B8, symBinAddr: 0x7704, symSize: 0x78 }
  - { offset: 0x15379, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr152drop_in_place$LT$alloc..vec..Vec$LT$addr2line..unit..ResUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17hdd35552c1680b2ebE', symObjAddr: 0x26FC, symBinAddr: 0x777C, symSize: 0xC4 }
  - { offset: 0x154E5, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr152drop_in_place$LT$alloc..vec..Vec$LT$addr2line..unit..SupUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17h6431a2f92897bef0E', symObjAddr: 0x27C0, symBinAddr: 0x7840, symSize: 0xF4 }
  - { offset: 0x156FB, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr156drop_in_place$LT$alloc..boxed..Box$LT$$u5b$core..option..Option$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$$u5d$$GT$$GT$17h61627ecd782bd92dE', symObjAddr: 0x28B4, symBinAddr: 0x7934, symSize: 0xF8 }
  - { offset: 0x15822, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr159drop_in_place$LT$alloc..sync..ArcInner$LT$gimli..read..dwarf..Dwarf$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17h71c073e2ea863d36E', symObjAddr: 0x29AC, symBinAddr: 0x7A2C, symSize: 0x58 }
  - { offset: 0x158FB, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr161drop_in_place$LT$alloc..vec..Vec$LT$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$17h938f97b5a308fc3cE', symObjAddr: 0x2A04, symBinAddr: 0x7A84, symSize: 0xAC }
  - { offset: 0x15B36, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr172drop_in_place$LT$core..result..Result$LT$addr2line..Context$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$C$gimli..read..Error$GT$$GT$17h8205db00afecd676E', symObjAddr: 0x2B5C, symBinAddr: 0x7B30, symSize: 0x80 }
  - { offset: 0x15BCA, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr173drop_in_place$LT$alloc..boxed..Box$LT$$u5b$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$u5d$$GT$$GT$17h141b901e37828fa3E', symObjAddr: 0x2BDC, symBinAddr: 0x7BB0, symSize: 0x9C }
  - { offset: 0x15BE1, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr173drop_in_place$LT$alloc..boxed..Box$LT$$u5b$addr2line..function..LazyFunction$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$u5d$$GT$$GT$17h141b901e37828fa3E', symObjAddr: 0x2BDC, symBinAddr: 0x7BB0, symSize: 0x9C }
  - { offset: 0x15E1D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr181drop_in_place$LT$core..option..Option$LT$gimli..read..line..IncompleteLineProgram$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$usize$GT$$GT$$GT$17h0ce7bfce5e4abb6bE', symObjAddr: 0x2CE8, symBinAddr: 0x7C4C, symSize: 0x98 }
  - { offset: 0x160D0, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr181drop_in_place$LT$core..result..Result$LT$addr2line..frame..FrameIter$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$C$gimli..read..Error$GT$$GT$17hc32763af75cf70e4E', symObjAddr: 0x2D80, symBinAddr: 0x7CE4, symSize: 0x44 }
  - { offset: 0x1620A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr184drop_in_place$LT$core..result..Result$LT$addr2line..function..Functions$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$C$gimli..read..Error$GT$$GT$17hae974801a783d12aE', symObjAddr: 0x2DC4, symBinAddr: 0x7D28, symSize: 0xD8 }
  - { offset: 0x16BC0, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr223drop_in_place$LT$std..sys..thread_local..native..lazy..State$LT$core..cell..Cell$LT$core..option..Option$LT$alloc..sync..Arc$LT$std..sync..poison..mutex..Mutex$LT$alloc..vec..Vec$LT$u8$GT$$GT$$GT$$GT$$GT$$C$$LP$$RP$$GT$$GT$17h8e1dbbdc8aa6e2b2E', symObjAddr: 0x2F10, symBinAddr: 0x7E00, symSize: 0x40 }
  - { offset: 0x16CA8, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr231drop_in_place$LT$core..result..Result$LT$core..option..Option$LT$alloc..boxed..Box$LT$addr2line..unit..DwoUnit$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$GT$$GT$$GT$$C$gimli..read..Error$GT$$GT$17h48abe14940fae1d7E', symObjAddr: 0x2F50, symBinAddr: 0x7E40, symSize: 0xD4 }
  - { offset: 0x16E8E, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr275drop_in_place$LT$gimli..read..line..LineRows$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$gimli..read..line..IncompleteLineProgram$LT$gimli..read..endian_slice..EndianSlice$LT$gimli..endianity..LittleEndian$GT$$C$usize$GT$$C$usize$GT$$GT$17h1fb49ddc3575d792E', symObjAddr: 0x304C, symBinAddr: 0x7F14, symSize: 0x8C }
  - { offset: 0x17100, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr280drop_in_place$LT$$LT$alloc..collections..btree..map..IntoIter$LT$K$C$V$C$A$GT$$u20$as$u20$core..ops..drop..Drop$GT$..drop..DropGuard$LT$u64$C$core..result..Result$LT$alloc..sync..Arc$LT$gimli..read..abbrev..Abbreviations$GT$$C$gimli..read..Error$GT$$C$alloc..alloc..Global$GT$$GT$17h412edb4cf40f508aE', symObjAddr: 0x30D8, symBinAddr: 0x7FA0, symSize: 0x84 }
  - { offset: 0x1729F, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr41drop_in_place$LT$std..panicking..Hook$GT$17h5156331f1f3483fdE', symObjAddr: 0x32B4, symBinAddr: 0x8024, symSize: 0x7C }
  - { offset: 0x17397, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr42drop_in_place$LT$alloc..string..String$GT$17h49e49416d7fc736bE', symObjAddr: 0x3330, symBinAddr: 0x80A0, symSize: 0x28 }
  - { offset: 0x1746C, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr42drop_in_place$LT$std..io..error..Error$GT$17h2076e24185186a1eE', symObjAddr: 0x3358, symBinAddr: 0x80C8, symSize: 0xAC }
  - { offset: 0x17680, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr44drop_in_place$LT$object..read..ObjectMap$GT$17hefa7b8970618855fE', symObjAddr: 0x3404, symBinAddr: 0x8174, symSize: 0x58 }
  - { offset: 0x177AE, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$alloc..vec..Vec$LT$u8$GT$$GT$17he05f3434a0e55321E', symObjAddr: 0x350C, symBinAddr: 0x81CC, symSize: 0x28 }
  - { offset: 0x17839, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$std..io..stdio..StdinLock$GT$17h7f23f0068af34be6E', symObjAddr: 0x3534, symBinAddr: 0x81F4, symSize: 0x54 }
  - { offset: 0x17850, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$std..io..stdio..StdinLock$GT$17h7f23f0068af34be6E', symObjAddr: 0x3534, symBinAddr: 0x81F4, symSize: 0x54 }
  - { offset: 0x17865, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr46drop_in_place$LT$std..io..stdio..StdinLock$GT$17h7f23f0068af34be6E', symObjAddr: 0x3534, symBinAddr: 0x81F4, symSize: 0x54 }
  - { offset: 0x17946, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr48drop_in_place$LT$alloc..ffi..c_str..NulError$GT$17hf807e8c3d992613aE', symObjAddr: 0x35E0, symBinAddr: 0x8248, symSize: 0x28 }
  - { offset: 0x17A01, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr55drop_in_place$LT$gimli..read..abbrev..Abbreviations$GT$17h5028fd7f23a66a5dE', symObjAddr: 0x38D4, symBinAddr: 0x8270, symSize: 0x84 }
  - { offset: 0x17BB8, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr60drop_in_place$LT$gimli..read..abbrev..AbbreviationsCache$GT$17h109635aebb38ad94E', symObjAddr: 0x39E8, symBinAddr: 0x82F4, symSize: 0xCC }
  - { offset: 0x17D77, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr62drop_in_place$LT$std..sys..sync..rwlock..queue..PanicGuard$GT$17ha6ff6fed8f58b75aE', symObjAddr: 0x3AB4, symBinAddr: 0x83C0, symSize: 0x44 }
  - { offset: 0x17DBD, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr64drop_in_place$LT$std..sys..pal..unix..sync..mutex..AttrGuard$GT$17hfae1c338ebc17455E', symObjAddr: 0x3AF8, symBinAddr: 0x8404, symSize: 0x48 }
  - { offset: 0x17DF8, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr65drop_in_place$LT$alloc..vec..Vec$LT$alloc..string..String$GT$$GT$17h0e8d3320a68b6c20E', symObjAddr: 0x3B40, symBinAddr: 0x844C, symSize: 0x80 }
  - { offset: 0x17F8F, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr65drop_in_place$LT$std..backtrace_rs..symbolize..gimli..Context$GT$17he1a9681aa2b789c5E', symObjAddr: 0x3BC0, symBinAddr: 0x84CC, symSize: 0xE8 }
  - { offset: 0x18220, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr65drop_in_place$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$17h1ad26fdf3d4d883aE', symObjAddr: 0x3CA8, symBinAddr: 0x85B4, symSize: 0x50 }
  - { offset: 0x1828A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr66drop_in_place$LT$std..backtrace_rs..backtrace..libunwind..Bomb$GT$17hcf55bbc6e2408f91E', symObjAddr: 0x3D50, symBinAddr: 0x8604, symSize: 0x40 }
  - { offset: 0x1832F, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr70drop_in_place$LT$std..backtrace_rs..symbolize..gimli..stash..Stash$GT$17h823b9ed5294710a1E', symObjAddr: 0x3E8C, symBinAddr: 0x8644, symSize: 0xB8 }
  - { offset: 0x185D2, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr71drop_in_place$LT$std..backtrace_rs..symbolize..gimli..macho..Object$GT$17h7d766a5d50e6fcdbE', symObjAddr: 0x3F44, symBinAddr: 0x86FC, symSize: 0xD8 }
  - { offset: 0x188A3, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr71drop_in_place$LT$std..panicking..rust_panic_without_hook..RewrapBox$GT$17hb555acb8faca8b70E', symObjAddr: 0x401C, symBinAddr: 0x87D4, symSize: 0x6C }
  - { offset: 0x1899B, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr72drop_in_place$LT$core..option..Option$LT$object..read..ObjectMap$GT$$GT$17hc34cda9cf8984625E', symObjAddr: 0x4088, symBinAddr: 0x8840, symSize: 0x64 }
  - { offset: 0x18B13, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr73drop_in_place$LT$alloc..vec..Vec$LT$addr2line..line..LineSequence$GT$$GT$17h0feaef6467c5bb06E', symObjAddr: 0x40EC, symBinAddr: 0x88A4, symSize: 0x84 }
  - { offset: 0x18C6A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr77drop_in_place$LT$alloc..boxed..Box$LT$$u5b$alloc..string..String$u5d$$GT$$GT$17h8657c1556d878703E', symObjAddr: 0x41C8, symBinAddr: 0x8928, symSize: 0x70 }
  - { offset: 0x18C81, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr77drop_in_place$LT$alloc..boxed..Box$LT$$u5b$alloc..string..String$u5d$$GT$$GT$17h8657c1556d878703E', symObjAddr: 0x41C8, symBinAddr: 0x8928, symSize: 0x70 }
  - { offset: 0x18D8A, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr77drop_in_place$LT$std..panicking..begin_panic_handler..FormatStringPayload$GT$17h1141e34cc3e1cd8cE', symObjAddr: 0x4238, symBinAddr: 0x8998, symSize: 0x34 }
  - { offset: 0x18E45, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr79drop_in_place$LT$std..sync..poison..rwlock..RwLockReadGuard$LT$$LP$$RP$$GT$$GT$17h3c7acdfaefb6864dE', symObjAddr: 0x435C, symBinAddr: 0x89CC, symSize: 0x68 }
  - { offset: 0x18EDE, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr81drop_in_place$LT$$LP$usize$C$std..backtrace_rs..symbolize..gimli..Mapping$RP$$GT$17h0fc60d1e178e2af3E', symObjAddr: 0x4458, symBinAddr: 0x8A34, symSize: 0x54 }
  - { offset: 0x18F5E, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr81drop_in_place$LT$core..result..Result$LT$$LP$$RP$$C$std..io..error..Error$GT$$GT$17h32d66e7d6b4290f9E', symObjAddr: 0x44AC, symBinAddr: 0x8A88, symSize: 0x20 }
  - { offset: 0x18F7E, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr84drop_in_place$LT$alloc..sync..ArcInner$LT$gimli..read..abbrev..Abbreviations$GT$$GT$17hc1742a6ee5f4f5c4E', symObjAddr: 0x455C, symBinAddr: 0x8AA8, symSize: 0x84 }
  - { offset: 0x19145, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr87drop_in_place$LT$alloc..sync..ArcInner$LT$std..sys..pal..unix..fs..InnerReadDir$GT$$GT$17h22f7f5fc9c3aa8bdE', symObjAddr: 0x45E0, symBinAddr: 0x8B2C, symSize: 0x60 }
  - { offset: 0x19328, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr88drop_in_place$LT$alloc..vec..Vec$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17heda6be67225cff8cE', symObjAddr: 0x4674, symBinAddr: 0x8B8C, symSize: 0x98 }
  - { offset: 0x19547, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr92drop_in_place$LT$core..result..Result$LT$addr2line..line..Lines$C$gimli..read..Error$GT$$GT$17h75873180c89d56d0E', symObjAddr: 0x47E8, symBinAddr: 0x8C24, symSize: 0xE4 }
  - { offset: 0x1978B, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr93drop_in_place$LT$core..option..Option$LT$std..backtrace_rs..symbolize..gimli..Mapping$GT$$GT$17h8ea9f8655928f4fcE', symObjAddr: 0x48CC, symBinAddr: 0x8D08, symSize: 0x6C }
  - { offset: 0x19825, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr95drop_in_place$LT$core..option..IntoIter$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17he46fad7f279ea952E', symObjAddr: 0x4938, symBinAddr: 0x8D74, symSize: 0x5C }
  - { offset: 0x19843, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr95drop_in_place$LT$core..option..IntoIter$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17he46fad7f279ea952E', symObjAddr: 0x4938, symBinAddr: 0x8D74, symSize: 0x5C }
  - { offset: 0x19858, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr95drop_in_place$LT$core..option..IntoIter$LT$std..backtrace_rs..symbolize..gimli..Library$GT$$GT$17he46fad7f279ea952E', symObjAddr: 0x4938, symBinAddr: 0x8D74, symSize: 0x5C }
  - { offset: 0x1B6F3, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h025645fbfb2d8cd1E, symObjAddr: 0x4D90, symBinAddr: 0x8DE4, symSize: 0x134 }
  - { offset: 0x1B916, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h246a4f2d911688a1E, symObjAddr: 0x4EC4, symBinAddr: 0x8F18, symSize: 0xD0 }
  - { offset: 0x1BA0D, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h6ce4cdb71517d19eE, symObjAddr: 0x4F94, symBinAddr: 0x8FE8, symSize: 0xCC }
  - { offset: 0x1BB04, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17h6e4fd85017a3f5e4E, symObjAddr: 0x5060, symBinAddr: 0x90B4, symSize: 0xD0 }
  - { offset: 0x1BBFB, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17ha3c898ebd570d9a9E, symObjAddr: 0x5130, symBinAddr: 0x9184, symSize: 0xCC }
  - { offset: 0x1BD48, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17hbe3f226301cd071dE, symObjAddr: 0x51FC, symBinAddr: 0x9250, symSize: 0xCC }
  - { offset: 0x1BE3F, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17hc3d7c76f255c304aE, symObjAddr: 0x52C8, symBinAddr: 0x931C, symSize: 0xD0 }
  - { offset: 0x1BF36, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared5pivot11median3_rec17heb13e39171436e07E, symObjAddr: 0x5398, symBinAddr: 0x93EC, symSize: 0x11C }
  - { offset: 0x1C0F7, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort12sort4_stable17h29d5ecfff8d25808E, symObjAddr: 0x54B4, symBinAddr: 0x9508, symSize: 0x1B4 }
  - { offset: 0x1C29A, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort12sort4_stable17he2b82e95c0946cc4E, symObjAddr: 0x5668, symBinAddr: 0x96BC, symSize: 0x180 }
  - { offset: 0x1C6B6, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort12sort8_stable17h40f301b851efa79fE, symObjAddr: 0x57E8, symBinAddr: 0x983C, symSize: 0x2B8 }
  - { offset: 0x1CC38, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort18small_sort_general17h8f0f2bfe6ac37bfbE, symObjAddr: 0x5AA0, symBinAddr: 0x9AF4, symSize: 0x490 }
  - { offset: 0x1D33E, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort18small_sort_general17h9886ba35fa9ddf7bE, symObjAddr: 0x5F30, symBinAddr: 0x9F84, symSize: 0x3E0 }
  - { offset: 0x1D84C, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h001473db42a80264E, symObjAddr: 0x6310, symBinAddr: 0xA364, symSize: 0xB0 }
  - { offset: 0x1D8EC, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h1e1ea2c4d7503f74E, symObjAddr: 0x63C0, symBinAddr: 0xA414, symSize: 0xCC }
  - { offset: 0x1D9EA, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h4c238536f310567cE, symObjAddr: 0x648C, symBinAddr: 0xA4E0, symSize: 0xFC }
  - { offset: 0x1DB7F, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h5ae8ea909444dc00E, symObjAddr: 0x6588, symBinAddr: 0xA5DC, symSize: 0xAC }
  - { offset: 0x1DC1F, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h677848d84e66ea2aE, symObjAddr: 0x6634, symBinAddr: 0xA688, symSize: 0xA8 }
  - { offset: 0x1DCBF, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17h753c2c33d7211ceaE, symObjAddr: 0x66DC, symBinAddr: 0xA730, symSize: 0xBC }
  - { offset: 0x1DD5F, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17hceef73df05978c22E, symObjAddr: 0x6798, symBinAddr: 0xA7EC, symSize: 0x90 }
  - { offset: 0x1DDFF, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort25insertion_sort_shift_left17hfb494d5f79707c02E, symObjAddr: 0x6828, symBinAddr: 0xA87C, symSize: 0xAC }
  - { offset: 0x1E0C2, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h103ff636fd5ab89dE, symObjAddr: 0x68D4, symBinAddr: 0xA928, symSize: 0x148 }
  - { offset: 0x1E364, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h1e88dc2727ad5c37E, symObjAddr: 0x6A1C, symBinAddr: 0xAA70, symSize: 0x16C }
  - { offset: 0x1E65C, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h54d923433181cd8aE, symObjAddr: 0x6B88, symBinAddr: 0xABDC, symSize: 0x16C }
  - { offset: 0x1E954, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h76405073938de01eE, symObjAddr: 0x6CF4, symBinAddr: 0xAD48, symSize: 0x148 }
  - { offset: 0x1EBF6, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17h9cb82a8482cd4f53E, symObjAddr: 0x6E3C, symBinAddr: 0xAE90, symSize: 0x148 }
  - { offset: 0x1EE98, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable14driftsort_main17hf9ea8a01734384bdE, symObjAddr: 0x6F84, symBinAddr: 0xAFD8, symSize: 0x148 }
  - { offset: 0x1F286, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h00ab1c6180ac4055E, symObjAddr: 0x70CC, symBinAddr: 0xB120, symSize: 0x4E0 }
  - { offset: 0x1F8B1, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h0aa4aacce1cc6d7cE, symObjAddr: 0x75AC, symBinAddr: 0xB600, symSize: 0x5E8 }
  - { offset: 0x1FF2B, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h343dbae25f37733bE, symObjAddr: 0x7B94, symBinAddr: 0xBBE8, symSize: 0x538 }
  - { offset: 0x205F1, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h4258b1bc2caa3ec0E, symObjAddr: 0x80CC, symBinAddr: 0xC120, symSize: 0x6F0 }
  - { offset: 0x20CB6, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17h9cf8228521243839E, symObjAddr: 0x87BC, symBinAddr: 0xC810, symSize: 0x5E8 }
  - { offset: 0x21330, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift4sort17he5c0348fca8b4ee7E, symObjAddr: 0x8DA4, symBinAddr: 0xCDF8, symSize: 0x63C }
  - { offset: 0x21B75, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17h115e1a8136b6fa84E, symObjAddr: 0x93E0, symBinAddr: 0xD434, symSize: 0x820 }
  - { offset: 0x22733, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17h270cbea95e7bc0c9E, symObjAddr: 0x9C00, symBinAddr: 0xDC54, symSize: 0x73C }
  - { offset: 0x22E38, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17h3e7b305b5c7976baE, symObjAddr: 0xA33C, symBinAddr: 0xE390, symSize: 0x6C8 }
  - { offset: 0x238B2, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17hc1d95435bd0f54c5E, symObjAddr: 0xAA04, symBinAddr: 0xEA58, symSize: 0x828 }
  - { offset: 0x2495A, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17hcf4fb219382f768bE, symObjAddr: 0xB22C, symBinAddr: 0xF280, symSize: 0x7FC }
  - { offset: 0x25516, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable9quicksort9quicksort17hfd59b4f11a472dd5E, symObjAddr: 0xBA28, symBinAddr: 0xFA7C, symSize: 0x6D0 }
  - { offset: 0x25FC7, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable7ipnsort17h519db9bb88c7d2c0E, symObjAddr: 0xC0F8, symBinAddr: 0x1014C, symSize: 0x1E4 }
  - { offset: 0x260D8, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable7ipnsort17hc49a474c49f61d9aE, symObjAddr: 0xC2DC, symBinAddr: 0x10330, symSize: 0x290 }
  - { offset: 0x2637B, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable8heapsort8heapsort17h6b7b52018c9f5da0E, symObjAddr: 0xC56C, symBinAddr: 0x105C0, symSize: 0x10C }
  - { offset: 0x2657C, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable8heapsort8heapsort17h719a9553e129d672E, symObjAddr: 0xC678, symBinAddr: 0x106CC, symSize: 0x160 }
  - { offset: 0x2690D, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable9quicksort9quicksort17h96e7db41672c501eE, symObjAddr: 0xC7D8, symBinAddr: 0x1082C, symSize: 0x448 }
  - { offset: 0x271BC, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort8unstable9quicksort9quicksort17ha039472c97b018d8E, symObjAddr: 0xCC20, symBinAddr: 0x10C74, symSize: 0x5BC }
  - { offset: 0x27EAD, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h225d92188c8db6c3E', symObjAddr: 0x13C, symBinAddr: 0x67F4, symSize: 0x30 }
  - { offset: 0x27EC6, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h22a55d7ebb819ea6E', symObjAddr: 0x16C, symBinAddr: 0x6824, symSize: 0x30 }
  - { offset: 0x27EDF, size: 0x8, addend: 0x0, symName: '__ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h8d516569098f5f2bE', symObjAddr: 0x19C, symBinAddr: 0x6854, symSize: 0x30 }
  - { offset: 0x27F47, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h229631da8bdd67acE', symObjAddr: 0x464, symBinAddr: 0x6884, symSize: 0x1C }
  - { offset: 0x27F5A, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h45300256e973f494E', symObjAddr: 0x4F8, symBinAddr: 0x68A0, symSize: 0x30 }
  - { offset: 0x27F9F, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h6cf282856d53ef8fE', symObjAddr: 0x66C, symBinAddr: 0x68D0, symSize: 0x74 }
  - { offset: 0x280E0, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h832369a0cde054d2E', symObjAddr: 0x77C, symBinAddr: 0x6944, symSize: 0x30 }
  - { offset: 0x28125, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h99ba184c1a5db1eaE', symObjAddr: 0x91C, symBinAddr: 0x6974, symSize: 0x1C }
  - { offset: 0x281C5, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num50_$LT$impl$u20$core..fmt..Debug$u20$for$u20$i32$GT$3fmt17h15c50277390862aeE', symObjAddr: 0xCF0, symBinAddr: 0x6A14, symSize: 0x2C }
  - { offset: 0x281F3, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num52_$LT$impl$u20$core..fmt..Debug$u20$for$u20$usize$GT$3fmt17h6def11b803266407E', symObjAddr: 0xDA0, symBinAddr: 0x6A40, symSize: 0x2C }
  - { offset: 0x2825A, size: 0x8, addend: 0x0, symName: '__ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h3bfca5eae23b0d69E', symObjAddr: 0xC20, symBinAddr: 0x6990, symSize: 0x1C }
  - { offset: 0x28274, size: 0x8, addend: 0x0, symName: '__ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17ha43e2829162aef39E', symObjAddr: 0xC3C, symBinAddr: 0x69AC, symSize: 0x68 }
  - { offset: 0x285F2, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write10write_char17h2bd2da6422df0c04E, symObjAddr: 0xDCC, symBinAddr: 0x6A6C, symSize: 0x128 }
  - { offset: 0x28788, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write10write_char17h375232e784da95c3E, symObjAddr: 0xEF4, symBinAddr: 0x6B94, symSize: 0x174 }
  - { offset: 0x28987, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write10write_char17h7453f2c039370a9dE, symObjAddr: 0x1328, symBinAddr: 0x6D08, symSize: 0x180 }
  - { offset: 0x28B60, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17h2a0fceba8367f855E, symObjAddr: 0x1620, symBinAddr: 0x6E88, symSize: 0x1C }
  - { offset: 0x28B8E, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17h7b0eee5bb9fc5859E, symObjAddr: 0x163C, symBinAddr: 0x6EA4, symSize: 0x1C }
  - { offset: 0x28BBC, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17hb212afec46fd02ecE, symObjAddr: 0x1658, symBinAddr: 0x6EC0, symSize: 0x1C }
  - { offset: 0x28BEA, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17he153d8ed26875afeE, symObjAddr: 0x1690, symBinAddr: 0x6EDC, symSize: 0x1C }
  - { offset: 0x28C44, size: 0x8, addend: 0x0, symName: '__ZN50_$LT$$BP$mut$u20$T$u20$as$u20$core..fmt..Debug$GT$3fmt17he39465caf33bcc4eE', symObjAddr: 0xD258, symBinAddr: 0x11230, symSize: 0x14 }
  - { offset: 0x28C90, size: 0x8, addend: 0x0, symName: '__ZN52_$LT$$RF$mut$u20$T$u20$as$u20$core..fmt..Display$GT$3fmt17hc6db9c4561bb0640E', symObjAddr: 0xD2E0, symBinAddr: 0x11244, symSize: 0x1C }
  - { offset: 0x29608, size: 0x8, addend: 0x0, symName: '__ZN55_$LT$$RF$str$u20$as$u20$core..str..pattern..Pattern$GT$15is_contained_in17h07538af93eae73f4E', symObjAddr: 0xD2FC, symBinAddr: 0x11260, symSize: 0x3BC }
  - { offset: 0x29A11, size: 0x8, addend: 0x0, symName: __ZN4core5panic12PanicPayload6as_str17h5cdc46f655586ed2E, symObjAddr: 0x4D7C, symBinAddr: 0x8DD0, symSize: 0x14 }
  - { offset: 0x2A7E6, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function2Fn4call17ha8b0a4f638d49efbE, symObjAddr: 0x16AC, symBinAddr: 0x6EF8, symSize: 0x14 }
  - { offset: 0x2A811, size: 0x8, addend: 0x0, symName: __ZN4core3ops8function5FnMut8call_mut17hf559869434bbd04eE, symObjAddr: 0x16C0, symBinAddr: 0x6F0C, symSize: 0x14 }
  - { offset: 0x2A854, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h372dd670162a11a3E', symObjAddr: 0x1714, symBinAddr: 0x6F20, symSize: 0x18 }
  - { offset: 0x2A8A3, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h4dbfbb5401248555E', symObjAddr: 0x1754, symBinAddr: 0x6F38, symSize: 0xA4 }
  - { offset: 0x2A9E7, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h59664865ad50834bE', symObjAddr: 0x17F8, symBinAddr: 0x6FDC, symSize: 0x10 }
  - { offset: 0x2AA21, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17h5ee9fbff98e3f576E', symObjAddr: 0x1938, symBinAddr: 0x6FEC, symSize: 0x14 }
  - { offset: 0x2AA5B, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17hb8f0a3a311886dd1E', symObjAddr: 0x194C, symBinAddr: 0x7000, symSize: 0x18 }
  - { offset: 0x2AAAA, size: 0x8, addend: 0x0, symName: '__ZN4core3ops8function6FnOnce40call_once$u7b$$u7b$vtable.shim$u7d$$u7d$17hc37bdb1ed22c5dd7E', symObjAddr: 0x19E0, symBinAddr: 0x7018, symSize: 0x10 }
  - { offset: 0x2CB01, size: 0x8, addend: 0x0, symName: '__ZN4core4cell4once17OnceCell$LT$T$GT$8try_init17he4193c4098c349bdE', symObjAddr: 0x4A84, symBinAddr: 0x3A75C, symSize: 0x154 }
  - { offset: 0x2CF7C, size: 0x8, addend: 0x0, symName: __ZN4core9panicking13assert_failed17he664f25a4eeebf5cE, symObjAddr: 0xD234, symBinAddr: 0x3A8B0, symSize: 0x24 }
  - { offset: 0x2D332, size: 0x8, addend: 0x0, symName: __ZN3std9panicking11panic_count8increase17h1d24b79d4790a8a8E, symObjAddr: 0x3A9E8, symBinAddr: 0x239C0, symSize: 0x80 }
  - { offset: 0x2D425, size: 0x8, addend: 0x0, symName: __ZN3std9panicking11panic_count17is_zero_slow_path17hbb753c2ecca816a8E, symObjAddr: 0x3AB00, symBinAddr: 0x3AF48, symSize: 0x2C }
  - { offset: 0x2D4BB, size: 0x8, addend: 0x0, symName: __ZN3std9panicking3try7cleanup17hc102fb7a29c56c95E, symObjAddr: 0x3AB2C, symBinAddr: 0x3AF74, symSize: 0x54 }
  - { offset: 0x2D5D9, size: 0x8, addend: 0x0, symName: ___rust_drop_panic, symObjAddr: 0x39FEC, symBinAddr: 0x22FC4, symSize: 0xB8 }
  - { offset: 0x2D7CA, size: 0x8, addend: 0x0, symName: ___rust_foreign_exception, symObjAddr: 0x3A0A4, symBinAddr: 0x2307C, symSize: 0xB8 }
  - { offset: 0x2D9C8, size: 0x8, addend: 0x0, symName: __ZN3std9panicking8set_hook17hb09b3ca39306cb36E, symObjAddr: 0x3A15C, symBinAddr: 0x23134, symSize: 0x17C }
  - { offset: 0x2DCB1, size: 0x8, addend: 0x0, symName: __ZN3std9panicking9take_hook17he609bce382566349E, symObjAddr: 0x3A2D8, symBinAddr: 0x232B0, symSize: 0xF8 }
  - { offset: 0x2DEA6, size: 0x8, addend: 0x0, symName: __ZN3std9panicking12default_hook17ha0b223ccc4379930E, symObjAddr: 0x3A3D0, symBinAddr: 0x233A8, symSize: 0x1EC }
  - { offset: 0x2E143, size: 0x8, addend: 0x0, symName: '__ZN3std9panicking12default_hook28_$u7b$$u7b$closure$u7d$$u7d$17h5c3a234feebd11a5E', symObjAddr: 0x3A5BC, symBinAddr: 0x23594, symSize: 0x1A8 }
  - { offset: 0x2E324, size: 0x8, addend: 0x0, symName: '__ZN3std9panicking12default_hook28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17h2cb359aea37a8eccE', symObjAddr: 0x3A764, symBinAddr: 0x2373C, symSize: 0x284 }
  - { offset: 0x2E794, size: 0x8, addend: 0x0, symName: _rust_begin_unwind, symObjAddr: 0x3AB80, symBinAddr: 0x23A40, symSize: 0x20 }
  - { offset: 0x2E7FA, size: 0x8, addend: 0x0, symName: '__ZN102_$LT$std..panicking..begin_panic_handler..FormatStringPayload$u20$as$u20$core..panic..PanicPayload$GT$8take_box17hf9936339f9bb3e6eE', symObjAddr: 0x3ABA0, symBinAddr: 0x23A60, symSize: 0x110 }
  - { offset: 0x2EA8C, size: 0x8, addend: 0x0, symName: '__ZN102_$LT$std..panicking..begin_panic_handler..FormatStringPayload$u20$as$u20$core..panic..PanicPayload$GT$3get17h671f5c789103ccc6E', symObjAddr: 0x3ACB0, symBinAddr: 0x23B70, symSize: 0xB0 }
  - { offset: 0x2EBC3, size: 0x8, addend: 0x0, symName: '__ZN95_$LT$std..panicking..begin_panic_handler..FormatStringPayload$u20$as$u20$core..fmt..Display$GT$3fmt17h1a7e141a9fd2e841E', symObjAddr: 0x3AD60, symBinAddr: 0x23C20, symSize: 0x5C }
  - { offset: 0x2ECBE, size: 0x8, addend: 0x0, symName: '__ZN99_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..panic..PanicPayload$GT$8take_box17hfde794f38dfdff84E', symObjAddr: 0x3ADBC, symBinAddr: 0x23C7C, symSize: 0x50 }
  - { offset: 0x2ED34, size: 0x8, addend: 0x0, symName: '__ZN99_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..panic..PanicPayload$GT$3get17h40c5dcbca42c5d43E', symObjAddr: 0x3AE0C, symBinAddr: 0x23CCC, symSize: 0x18 }
  - { offset: 0x2ED4E, size: 0x8, addend: 0x0, symName: '__ZN99_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..panic..PanicPayload$GT$6as_str17h36252a21ddc56c9bE', symObjAddr: 0x3AE24, symBinAddr: 0x23CE4, symSize: 0x18 }
  - { offset: 0x2ED6E, size: 0x8, addend: 0x0, symName: '__ZN92_$LT$std..panicking..begin_panic_handler..StaticStrPayload$u20$as$u20$core..fmt..Display$GT$3fmt17hd84ef799566d4562E', symObjAddr: 0x3AE3C, symBinAddr: 0x23CFC, symSize: 0x1C }
  - { offset: 0x2ED89, size: 0x8, addend: 0x0, symName: '__ZN3std9panicking19begin_panic_handler28_$u7b$$u7b$closure$u7d$$u7d$17hcc8f653f753c0254E', symObjAddr: 0x3AE58, symBinAddr: 0x23D18, symSize: 0xC4 }
  - { offset: 0x2EEEE, size: 0x8, addend: 0x0, symName: __ZN3std9panicking14payload_as_str17h326c21158c3ccbd7E, symObjAddr: 0x3B008, symBinAddr: 0x23DDC, symSize: 0xB0 }
  - { offset: 0x2EFD1, size: 0x8, addend: 0x0, symName: __ZN3std9panicking20rust_panic_with_hook17h203f96c93e7ac62dE, symObjAddr: 0x3B0B8, symBinAddr: 0x23E8C, symSize: 0x3F8 }
  - { offset: 0x2F7DB, size: 0x8, addend: 0x0, symName: __ZN3std9panicking23rust_panic_without_hook17h0dc46d7b855229cfE, symObjAddr: 0x3B4B0, symBinAddr: 0x24284, symSize: 0xD4 }
  - { offset: 0x2F9E9, size: 0x8, addend: 0x0, symName: '__ZN96_$LT$std..panicking..rust_panic_without_hook..RewrapBox$u20$as$u20$core..panic..PanicPayload$GT$8take_box17h8a1901fef59ea3f3E', symObjAddr: 0x3B584, symBinAddr: 0x24358, symSize: 0x28 }
  - { offset: 0x2FA44, size: 0x8, addend: 0x0, symName: '__ZN96_$LT$std..panicking..rust_panic_without_hook..RewrapBox$u20$as$u20$core..panic..PanicPayload$GT$3get17h6712453ec18db7eaE', symObjAddr: 0x3B5AC, symBinAddr: 0x24380, symSize: 0x18 }
  - { offset: 0x2FA64, size: 0x8, addend: 0x0, symName: '__ZN89_$LT$std..panicking..rust_panic_without_hook..RewrapBox$u20$as$u20$core..fmt..Display$GT$3fmt17hd60effbb72ca2d48E', symObjAddr: 0x3B5C4, symBinAddr: 0x24398, symSize: 0x20 }
  - { offset: 0x2FA80, size: 0x8, addend: 0x0, symName: _rust_panic, symObjAddr: 0x3B5E4, symBinAddr: 0x243B8, symSize: 0x60 }
  - { offset: 0x2FABF, size: 0x8, addend: 0x0, symName: __ZN3std7process5abort17h5bdab09773b2c592E, symObjAddr: 0x35D9C, symBinAddr: 0x3AC44, symSize: 0xC }
  - { offset: 0x2FB45, size: 0x8, addend: 0x0, symName: __ZN3std4path4Path13_strip_prefix17h55294dff41b26308E, symObjAddr: 0x33B38, symBinAddr: 0x21D60, symSize: 0x21C }
  - { offset: 0x2FC83, size: 0x8, addend: 0x0, symName: __ZN3std4path4Path5_join17h516bc57fe1e4225dE, symObjAddr: 0x34358, symBinAddr: 0x21F7C, symSize: 0x1AC }
  - { offset: 0x30216, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components15len_before_body17ha7ee2271a8a6c6d9E, symObjAddr: 0x31DBC, symBinAddr: 0x20F18, symSize: 0x188 }
  - { offset: 0x3022D, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components15len_before_body17ha7ee2271a8a6c6d9E, symObjAddr: 0x31DBC, symBinAddr: 0x20F18, symSize: 0x188 }
  - { offset: 0x30242, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components15len_before_body17ha7ee2271a8a6c6d9E, symObjAddr: 0x31DBC, symBinAddr: 0x20F18, symSize: 0x188 }
  - { offset: 0x3041C, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components7as_path17h6e25608fa8cb9557E, symObjAddr: 0x31F44, symBinAddr: 0x210A0, symSize: 0x530 }
  - { offset: 0x308E5, size: 0x8, addend: 0x0, symName: __ZN3std4path10Components25parse_next_component_back17h5239b6f3b65d70c8E, symObjAddr: 0x32474, symBinAddr: 0x215D0, symSize: 0x120 }
  - { offset: 0x30AE0, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..path..Components$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17h6e8b0f281526c929E', symObjAddr: 0x3272C, symBinAddr: 0x216F0, symSize: 0x340 }
  - { offset: 0x30E29, size: 0x8, addend: 0x0, symName: '__ZN95_$LT$std..path..Components$u20$as$u20$core..iter..traits..double_ended..DoubleEndedIterator$GT$9next_back17h15500b5b5dde7decE', symObjAddr: 0x32A6C, symBinAddr: 0x21A30, symSize: 0x330 }
  - { offset: 0x30FD2, size: 0x8, addend: 0x0, symName: '__ZN61_$LT$std..path..Component$u20$as$u20$core..cmp..PartialEq$GT$2eq17h238f4f8a8cc29bb2E', symObjAddr: 0x3C0CC, symBinAddr: 0x24B00, symSize: 0x138 }
  - { offset: 0x31190, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix2fs4File6open_c17h0a400570de534b0aE, symObjAddr: 0x3D220, symBinAddr: 0x24E3C, symSize: 0x1B0 }
  - { offset: 0x312ED, size: 0x8, addend: 0x0, symName: '__ZN91_$LT$std..sys..pal..unix..fs..ReadDir$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17he1b3299a727d70eaE', symObjAddr: 0x3CD90, symBinAddr: 0x24C38, symSize: 0x138 }
  - { offset: 0x31593, size: 0x8, addend: 0x0, symName: '__ZN70_$LT$std..sys..pal..unix..fs..Dir$u20$as$u20$core..ops..drop..Drop$GT$4drop17h2f7cbb54b209bd1fE', symObjAddr: 0x3CEC8, symBinAddr: 0x24D70, symSize: 0xCC }
  - { offset: 0x31684, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix2fs7readdir17h52c204b360126013E, symObjAddr: 0x3DAF4, symBinAddr: 0x24FEC, symSize: 0x1A4 }
  - { offset: 0x31984, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix2os13env_read_lock17h1370e80127ab09adE, symObjAddr: 0x400EC, symBinAddr: 0x25190, symSize: 0x2EC }
  - { offset: 0x3202B, size: 0x8, addend: 0x0, symName: '__ZN3std3sys3pal4unix2os6getenv28_$u7b$$u7b$closure$u7d$$u7d$17h09d09dd30efbeda3E', symObjAddr: 0x403D8, symBinAddr: 0x2547C, symSize: 0x150 }
  - { offset: 0x32255, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$5write17hf7eb28e2e3a5bd7dE', symObjAddr: 0x43178, symBinAddr: 0x255CC, symSize: 0x4C }
  - { offset: 0x32315, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$14write_vectored17h6899437ee14ad0e1E', symObjAddr: 0x431C4, symBinAddr: 0x25618, symSize: 0x4C }
  - { offset: 0x323D5, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$17is_write_vectored17h9b2366713f7e1db9E', symObjAddr: 0x43210, symBinAddr: 0x25664, symSize: 0x14 }
  - { offset: 0x323EE, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..pal..unix..stdio..Stderr$u20$as$u20$std..io..Write$GT$5flush17hd9b43cd37b405524E', symObjAddr: 0x43224, symBinAddr: 0x25678, symSize: 0x14 }
  - { offset: 0x3245A, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix4sync5mutex5Mutex4init17hdf1c4f248d85eb16E, symObjAddr: 0x43238, symBinAddr: 0x2568C, symSize: 0x16C }
  - { offset: 0x3250B, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix4sync5mutex5Mutex4lock4fail17h6819c36a4f036f8cE, symObjAddr: 0x433C0, symBinAddr: 0x3AFC8, symSize: 0xDC }
  - { offset: 0x32773, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix17decode_error_kind17h7a8628d240588c97E, symObjAddr: 0x43C48, symBinAddr: 0x257F8, symSize: 0x30 }
  - { offset: 0x3278C, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal4unix14abort_internal17h1f437a045e84c0acE, symObjAddr: 0x43C78, symBinAddr: 0x25828, symSize: 0xC }
  - { offset: 0x327ED, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal6common14small_c_string24run_with_cstr_allocating17h0222687f57b5b8d8E, symObjAddr: 0x378C4, symBinAddr: 0x3AC50, symSize: 0xAC }
  - { offset: 0x32948, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal6common14small_c_string24run_with_cstr_allocating17h89545bf18b96ebe8E, symObjAddr: 0x37BC8, symBinAddr: 0x3ACFC, symSize: 0x9C }
  - { offset: 0x32AB6, size: 0x8, addend: 0x0, symName: __ZN3std3sys3pal6common14small_c_string24run_with_cstr_allocating17hf540475ca51d3e7bE, symObjAddr: 0x37DE8, symBinAddr: 0x3AD98, symSize: 0xC4 }
  - { offset: 0x32D16, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$std..sys..os_str..bytes..Slice$u20$as$u20$core..fmt..Display$GT$3fmt17h9ac75767c8ed01b2E', symObjAddr: 0x466F4, symBinAddr: 0x25BCC, symSize: 0xA0 }
  - { offset: 0x32D84, size: 0x8, addend: 0x0, symName: '__ZN3std3sys11personality3gcc14find_eh_action28_$u7b$$u7b$closure$u7d$$u7d$17heb526fe46b317879E', symObjAddr: 0x44DB8, symBinAddr: 0x25834, symSize: 0x18 }
  - { offset: 0x32D97, size: 0x8, addend: 0x0, symName: '__ZN3std3sys11personality3gcc14find_eh_action28_$u7b$$u7b$closure$u7d$$u7d$17hdfacf1f5ab590eb6E', symObjAddr: 0x44DD0, symBinAddr: 0x2584C, symSize: 0x18 }
  - { offset: 0x32DC4, size: 0x8, addend: 0x0, symName: _rust_eh_personality, symObjAddr: 0x44DE8, symBinAddr: 0x25864, symSize: 0x368 }
  - { offset: 0x330EE, size: 0x8, addend: 0x0, symName: __ZN3std3sys11personality5dwarf2eh19read_encoded_offset17he9cb5faabb9c188bE, symObjAddr: 0x3801C, symBinAddr: 0x223C8, symSize: 0x150 }
  - { offset: 0x3324B, size: 0x8, addend: 0x0, symName: '__ZN3std3sys9backtrace10_print_fmt28_$u7b$$u7b$closure$u7d$$u7d$17h1863ed5b66499a89E', symObjAddr: 0x38458, symBinAddr: 0x22804, symSize: 0x30 }
  - { offset: 0x33273, size: 0x8, addend: 0x0, symName: '__ZN3std3sys9backtrace10_print_fmt28_$u7b$$u7b$closure$u7d$$u7d$17h49125cd88324db11E', symObjAddr: 0x38488, symBinAddr: 0x22834, symSize: 0x134 }
  - { offset: 0x33385, size: 0x8, addend: 0x0, symName: '__ZN3std3sys9backtrace10_print_fmt28_$u7b$$u7b$closure$u7d$$u7d$28_$u7b$$u7b$closure$u7d$$u7d$17heb5be01c54158345E', symObjAddr: 0x385BC, symBinAddr: 0x22968, symSize: 0x25C }
  - { offset: 0x335BF, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace4lock17h6e02177f0f3558baE, symObjAddr: 0x3816C, symBinAddr: 0x22518, symSize: 0x70 }
  - { offset: 0x33706, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace13BacktraceLock5print17h115149c0b879e5c3E, symObjAddr: 0x381DC, symBinAddr: 0x22588, symSize: 0x54 }
  - { offset: 0x33751, size: 0x8, addend: 0x0, symName: '__ZN98_$LT$std..sys..backtrace..BacktraceLock..print..DisplayBacktrace$u20$as$u20$core..fmt..Display$GT$3fmt17h217270392019d164E', symObjAddr: 0x38230, symBinAddr: 0x225DC, symSize: 0x228 }
  - { offset: 0x33CD2, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace26__rust_end_short_backtrace17h911de07218b69a6cE, symObjAddr: 0x38818, symBinAddr: 0x22BC4, symSize: 0xC }
  - { offset: 0x33CEB, size: 0x8, addend: 0x0, symName: __ZN3std3sys9backtrace15output_filename17h988a0b5399b264d6E, symObjAddr: 0x38830, symBinAddr: 0x22BD0, symSize: 0xFC }
  - { offset: 0x33E8E, size: 0x8, addend: 0x0, symName: '__ZN3std3sys4sync8once_box16OnceBox$LT$T$GT$10initialize17h482967fb77aa636aE', symObjAddr: 0x3899C, symBinAddr: 0x3AE5C, symSize: 0xEC }
  - { offset: 0x34140, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue6RwLock21read_unlock_contended17ha1da939695eb68a4E, symObjAddr: 0x479D0, symBinAddr: 0x3B204, symSize: 0x50 }
  - { offset: 0x342AE, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue6RwLock16unlock_contended17hb1bda0f6c9583d94E, symObjAddr: 0x47A20, symBinAddr: 0x3B254, symSize: 0x50 }
  - { offset: 0x34389, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue6RwLock12unlock_queue17h1e38a16940d3a0e6E, symObjAddr: 0x47B84, symBinAddr: 0x25F18, symSize: 0x1D8 }
  - { offset: 0x34AD3, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync6rwlock5queue9read_lock17h02feed94edb2ee18E, symObjAddr: 0x47710, symBinAddr: 0x25EEC, symSize: 0x2C }
  - { offset: 0x34B71, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync4once5queue4Once4call17hc1cbcad9bb87275cE, symObjAddr: 0x47310, symBinAddr: 0x3B0A4, symSize: 0x160 }
  - { offset: 0x34C90, size: 0x8, addend: 0x0, symName: __ZN3std3sys4sync4once5queue4wait17h58ee39f231bfccb9E, symObjAddr: 0x47470, symBinAddr: 0x25C6C, symSize: 0x198 }
  - { offset: 0x3520A, size: 0x8, addend: 0x0, symName: '__ZN82_$LT$std..sys..sync..once..queue..WaiterQueue$u20$as$u20$core..ops..drop..Drop$GT$4drop17h45f863b67fd27f71E', symObjAddr: 0x47608, symBinAddr: 0x25E04, symSize: 0xE8 }
  - { offset: 0x35452, size: 0x8, addend: 0x0, symName: '__ZN3std3sys12thread_local6native4lazy20Storage$LT$T$C$D$GT$10initialize17h61fa4697cd48c6f6E', symObjAddr: 0x47E24, symBinAddr: 0x3B2A4, symSize: 0xC0 }
  - { offset: 0x35645, size: 0x8, addend: 0x0, symName: __ZN3std3sys12thread_local6native4lazy7destroy17h26d4ae6c1bac34fbE, symObjAddr: 0x47EE4, symBinAddr: 0x260F0, symSize: 0x54 }
  - { offset: 0x357B1, size: 0x8, addend: 0x0, symName: __ZN3std3sys12thread_local5guard5apple6enable9run_dtors17h15022dedc81e5063E, symObjAddr: 0x4807C, symBinAddr: 0x26288, symSize: 0x13C }
  - { offset: 0x35D03, size: 0x8, addend: 0x0, symName: __ZN3std3sys12thread_local11destructors4list8register17h51fb0c77f8b321e6E, symObjAddr: 0x47F38, symBinAddr: 0x26144, symSize: 0x144 }
  - { offset: 0x362FB, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..io..Write..write_fmt..Adapter$LT$T$GT$$u20$as$u20$core..fmt..Write$GT$9write_str17h0698cdf4cfa713e8E', symObjAddr: 0x2D538, symBinAddr: 0x20BD8, symSize: 0xD8 }
  - { offset: 0x3644D, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..io..Write..write_fmt..Adapter$LT$T$GT$$u20$as$u20$core..fmt..Write$GT$9write_str17h58a4ca428f95c900E', symObjAddr: 0x2D738, symBinAddr: 0x20CB0, symSize: 0xD0 }
  - { offset: 0x36601, size: 0x8, addend: 0x0, symName: '__ZN80_$LT$std..io..Write..write_fmt..Adapter$LT$T$GT$$u20$as$u20$core..fmt..Write$GT$9write_str17h6eb5a36b1832c95eE', symObjAddr: 0x2D808, symBinAddr: 0x20D80, symSize: 0x80 }
  - { offset: 0x36768, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_all17h87bc481c06d6b6c7E, symObjAddr: 0x2CC10, symBinAddr: 0x202B0, symSize: 0xB8 }
  - { offset: 0x36897, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write18write_all_vectored17h0d3be626dc41da2eE, symObjAddr: 0x2CCC8, symBinAddr: 0x20368, symSize: 0x264 }
  - { offset: 0x36D3C, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write18write_all_vectored17h0f11e99192ac0da3E, symObjAddr: 0x2CF2C, symBinAddr: 0x205CC, symSize: 0x2A0 }
  - { offset: 0x371D7, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_fmt17h6564db7565ec4928E, symObjAddr: 0x2D1CC, symBinAddr: 0x2086C, symSize: 0x124 }
  - { offset: 0x373E9, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_fmt17h6e471a53cbe47e27E, symObjAddr: 0x2D2F0, symBinAddr: 0x20990, symSize: 0x124 }
  - { offset: 0x375FB, size: 0x8, addend: 0x0, symName: __ZN3std2io5Write9write_fmt17hb32eaafcfd249a19E, symObjAddr: 0x2D414, symBinAddr: 0x20AB4, symSize: 0x124 }
  - { offset: 0x37825, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$5write17hf1a6df91b3a9c331E', symObjAddr: 0x2731C, symBinAddr: 0x1FF1C, symSize: 0x84 }
  - { offset: 0x37969, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$14write_vectored17h935681c1f031c204E', symObjAddr: 0x273A0, symBinAddr: 0x1FFA0, symSize: 0x174 }
  - { offset: 0x37BB4, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$17is_write_vectored17h4055b148f544ec6cE', symObjAddr: 0x27514, symBinAddr: 0x20114, symSize: 0x14 }
  - { offset: 0x37BCE, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$9write_all17h8667482891c5bc01E', symObjAddr: 0x27528, symBinAddr: 0x20128, symSize: 0x80 }
  - { offset: 0x37D0B, size: 0x8, addend: 0x0, symName: '__ZN3std2io5impls74_$LT$impl$u20$std..io..Write$u20$for$u20$alloc..vec..Vec$LT$u8$C$A$GT$$GT$5flush17h1aa70c4ce0391c44E', symObjAddr: 0x275A8, symBinAddr: 0x201A8, symSize: 0x14 }
  - { offset: 0x37E4E, size: 0x8, addend: 0x0, symName: '__ZN58_$LT$std..io..error..Error$u20$as$u20$core..fmt..Debug$GT$3fmt17h4ed4286e752207faE', symObjAddr: 0x26AE0, symBinAddr: 0x1FA74, symSize: 0x10 }
  - { offset: 0x37E7F, size: 0x8, addend: 0x0, symName: '__ZN3std2io5error83_$LT$impl$u20$core..fmt..Debug$u20$for$u20$std..io..error..repr_bitpacked..Repr$GT$3fmt17h65ae83bc7dc9681bE', symObjAddr: 0x26D88, symBinAddr: 0x1FA84, symSize: 0x294 }
  - { offset: 0x380C9, size: 0x8, addend: 0x0, symName: '__ZN60_$LT$std..io..error..Error$u20$as$u20$core..fmt..Display$GT$3fmt17h9bd80ee0c1811a25E', symObjAddr: 0x2701C, symBinAddr: 0x1FD18, symSize: 0x204 }
  - { offset: 0x38327, size: 0x8, addend: 0x0, symName: '__ZN62_$LT$std..io..error..ErrorKind$u20$as$u20$core..fmt..Debug$GT$3fmt17h219a5c9d2c7eda43E', symObjAddr: 0x3C098, symBinAddr: 0x24ACC, symSize: 0x34 }
  - { offset: 0x3837F, size: 0x8, addend: 0x0, symName: __ZN3std2io5stdio22try_set_output_capture17h3c34954bc62c0e61E, symObjAddr: 0x2BA8C, symBinAddr: 0x201BC, symSize: 0xF4 }
  - { offset: 0x386EA, size: 0x8, addend: 0x0, symName: '__ZN3std4sync6poison6rwlock15RwLock$LT$T$GT$5write17h6b68ca5ab0a1427eE', symObjAddr: 0x36758, symBinAddr: 0x22128, symSize: 0x2A0 }
  - { offset: 0x38E86, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli7Context3new17h596fdfe772dc2bb6E, symObjAddr: 0x482F4, symBinAddr: 0x26450, symSize: 0x2410 }
  - { offset: 0x3CD2F, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli7Context11find_frames17h5933b96d93a6487fE, symObjAddr: 0x4A704, symBinAddr: 0x28860, symSize: 0x37C }
  - { offset: 0x3D1BC, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli5macho6Object5parse17h7c99ba302784e02bE, symObjAddr: 0x4D518, symBinAddr: 0x2B644, symSize: 0xBD0 }
  - { offset: 0x3F01A, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli5macho6Object7section17h7775decf80db3f1dE, symObjAddr: 0x4E0E8, symBinAddr: 0x2C214, symSize: 0x3A0 }
  - { offset: 0x3F4C6, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli5macho11find_header17hcafb8c4177e72185E, symObjAddr: 0x4D344, symBinAddr: 0x2B470, symSize: 0x1D4 }
  - { offset: 0x3FBA6, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli4mmap17h52426abb42b033dbE, symObjAddr: 0x4AA80, symBinAddr: 0x28BDC, symSize: 0x160 }
  - { offset: 0x4019C, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize5gimli7resolve17h4e500df68d74bbd0E, symObjAddr: 0x4ABE0, symBinAddr: 0x28D3C, symSize: 0x2734 }
  - { offset: 0x460E5, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9symbolize6Symbol4name17hfe1d12d8092fe441E, symObjAddr: 0x3B724, symBinAddr: 0x24418, symSize: 0xE4 }
  - { offset: 0x4621B, size: 0x8, addend: 0x0, symName: '__ZN79_$LT$std..backtrace_rs..symbolize..SymbolName$u20$as$u20$core..fmt..Display$GT$3fmt17hebc8fecc1f34ea4cE', symObjAddr: 0x3B948, symBinAddr: 0x244FC, symSize: 0x100 }
  - { offset: 0x46353, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs9backtrace9libunwind5trace8trace_fn17h8dbf4a7a3c414c4fE, symObjAddr: 0x482AC, symBinAddr: 0x26408, symSize: 0x48 }
  - { offset: 0x46426, size: 0x8, addend: 0x0, symName: __ZN3std12backtrace_rs5print17BacktraceFrameFmt21print_raw_with_column17h4b2c8f5f934c00c0E, symObjAddr: 0x3BB48, symBinAddr: 0x245FC, symSize: 0x4D0 }
  - { offset: 0x466C1, size: 0x8, addend: 0x0, symName: __ZN3std6thread7current12init_current17hb7b435bd17801980E, symObjAddr: 0x1F900, symBinAddr: 0x3AA4C, symSize: 0x17C }
  - { offset: 0x46A6D, size: 0x8, addend: 0x0, symName: __ZN3std6thread6Thread3new17h37ed5d6af114daceE, symObjAddr: 0x20814, symBinAddr: 0x1F390, symSize: 0x164 }
  - { offset: 0x46E4B, size: 0x8, addend: 0x0, symName: '__ZN68_$LT$std..thread..local..AccessError$u20$as$u20$core..fmt..Debug$GT$3fmt17hadafb6691b43fd9bE', symObjAddr: 0x20080, symBinAddr: 0x1F25C, symSize: 0x38 }
  - { offset: 0x46E66, size: 0x8, addend: 0x0, symName: __ZN3std6thread5local18panic_access_error17h9c565d65be312c10E, symObjAddr: 0x200D8, symBinAddr: 0x3ABC8, symSize: 0x44 }
  - { offset: 0x46ED9, size: 0x8, addend: 0x0, symName: '__ZN118_$LT$std..thread..thread_name_string..ThreadNameString$u20$as$u20$core..convert..From$LT$alloc..string..String$GT$$GT$4from17h83ebc56d52812e83E', symObjAddr: 0x20718, symBinAddr: 0x1F294, symSize: 0xFC }
  - { offset: 0x470C3, size: 0x8, addend: 0x0, symName: __ZN3std6thread8ThreadId3new9exhausted17h1c0c206286c216f1E, symObjAddr: 0x206E0, symBinAddr: 0x3AC0C, symSize: 0x38 }
  - { offset: 0x47165, size: 0x8, addend: 0x0, symName: __ZN3std5panic13resume_unwind17hff7a84dcda5b0744E, symObjAddr: 0x31B30, symBinAddr: 0x20E00, symSize: 0xC }
  - { offset: 0x4719F, size: 0x8, addend: 0x0, symName: __ZN3std5panic19get_backtrace_style17hb0947c7d43e173b3E, symObjAddr: 0x31B78, symBinAddr: 0x20E0C, symSize: 0x10C }
  - { offset: 0x471BD, size: 0x8, addend: 0x0, symName: __ZN3std5panic19get_backtrace_style17hb0947c7d43e173b3E, symObjAddr: 0x31B78, symBinAddr: 0x20E0C, symSize: 0x10C }
  - { offset: 0x471D2, size: 0x8, addend: 0x0, symName: __ZN3std5panic19get_backtrace_style17hb0947c7d43e173b3E, symObjAddr: 0x31B78, symBinAddr: 0x20E0C, symSize: 0x10C }
  - { offset: 0x475BB, size: 0x8, addend: 0x0, symName: __ZN3std3env11current_dir17h378d2e00353b8c55E, symObjAddr: 0x21D9C, symBinAddr: 0x1F4F4, symSize: 0x1D0 }
  - { offset: 0x47931, size: 0x8, addend: 0x0, symName: __ZN3std3env7_var_os17h91cf52b19c1ae9d5E, symObjAddr: 0x22698, symBinAddr: 0x1F6C4, symSize: 0x160 }
  - { offset: 0x47BEF, size: 0x8, addend: 0x0, symName: __ZN3std2fs11OpenOptions5_open17h060c4979cd359a16E, symObjAddr: 0x24BC4, symBinAddr: 0x1F824, symSize: 0x250 }
  - { offset: 0x47DD8, size: 0x8, addend: 0x0, symName: __ZN3std5alloc24default_alloc_error_hook17h2ab9053c5916c535E, symObjAddr: 0x39CF4, symBinAddr: 0x22CCC, symSize: 0x13C }
  - { offset: 0x4801B, size: 0x8, addend: 0x0, symName: ___rdl_alloc, symObjAddr: 0x39E30, symBinAddr: 0x22E08, symSize: 0x6C }
  - { offset: 0x48096, size: 0x8, addend: 0x0, symName: ___rdl_dealloc, symObjAddr: 0x39E9C, symBinAddr: 0x22E74, symSize: 0x10 }
  - { offset: 0x480C6, size: 0x8, addend: 0x0, symName: ___rdl_realloc, symObjAddr: 0x39EAC, symBinAddr: 0x22E84, symSize: 0xB0 }
  - { offset: 0x48197, size: 0x8, addend: 0x0, symName: ___rdl_alloc_zeroed, symObjAddr: 0x39F5C, symBinAddr: 0x22F34, symSize: 0x90 }
  - { offset: 0x48234, size: 0x8, addend: 0x0, symName: __ZN3std5alloc8rust_oom17hb46c5aeb63fa3cb5E, symObjAddr: 0x481B8, symBinAddr: 0x263C4, symSize: 0x2C }
  - { offset: 0x48282, size: 0x8, addend: 0x0, symName: ___rg_oom, symObjAddr: 0x481E4, symBinAddr: 0x263F0, symSize: 0x18 }
  - { offset: 0x483B7, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit32AttributeValue$LT$R$C$Offset$GT$11udata_value17h2b61cc9b6a98fb32E', symObjAddr: 0x14DE0, symBinAddr: 0x157AC, symSize: 0x90 }
  - { offset: 0x4840C, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4unit15parse_attribute17h49f957a62d462029E, symObjAddr: 0x130AC, symBinAddr: 0x13A78, symSize: 0xF74 }
  - { offset: 0x4B0E8, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4unit15skip_attributes17h31562773efeee2f1E, symObjAddr: 0x14020, symBinAddr: 0x149EC, symSize: 0x52C }
  - { offset: 0x4BAF9, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit18Attribute$LT$R$GT$5value17hbc90a819c686a39aE', symObjAddr: 0x1454C, symBinAddr: 0x14F18, symSize: 0x5DC }
  - { offset: 0x4C072, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit22EntriesCursor$LT$R$GT$10next_entry17hdf28d3cec3327cb6E', symObjAddr: 0x14B28, symBinAddr: 0x154F4, symSize: 0x2B8 }
  - { offset: 0x4C65D, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4unit33DebugInfoUnitHeadersIter$LT$R$GT$4next17h34712ebaabdffee4E', symObjAddr: 0x14E70, symBinAddr: 0x1583C, symSize: 0x524 }
  - { offset: 0x4D78F, size: 0x8, addend: 0x0, symName: '__ZN90_$LT$gimli..read..unit..AttributeValue$LT$R$C$Offset$GT$$u20$as$u20$core..clone..Clone$GT$5clone17h20eda0c245e8425dE', symObjAddr: 0x19914, symBinAddr: 0x19DE8, symSize: 0x68 }
  - { offset: 0x4D9B9, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader11read_offset17hb66bf69f998efb74E, symObjAddr: 0x179B8, symBinAddr: 0x18384, symSize: 0xBC }
  - { offset: 0x4DBF3, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader12read_sleb12817hd93e25793436fd15E, symObjAddr: 0x17A74, symBinAddr: 0x18440, symSize: 0x21C }
  - { offset: 0x4DCDF, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader12read_uleb12817ha415c724c08e32b4E, symObjAddr: 0x17C90, symBinAddr: 0x1865C, symSize: 0xB0 }
  - { offset: 0x4DD93, size: 0x8, addend: 0x0, symName: __ZN5gimli4read6reader6Reader17read_sized_offset17hbf387c98074d1971E, symObjAddr: 0x17D40, symBinAddr: 0x1870C, symSize: 0xEC }
  - { offset: 0x4E1A0, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line13parse_file_v517hcd59a31a0bc27fa9E, symObjAddr: 0x11F1C, symBinAddr: 0x128E8, symSize: 0x2B4 }
  - { offset: 0x4E316, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line15FileEntryFormat5parse17he68be02eb21a6f6cE, symObjAddr: 0x121D0, symBinAddr: 0x12B9C, symSize: 0x370 }
  - { offset: 0x4EB38, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line15parse_attribute17h6dcb28bb0d8f0d55E, symObjAddr: 0x12540, symBinAddr: 0x12F0C, symSize: 0x86C }
  - { offset: 0x502CB, size: 0x8, addend: 0x0, symName: __ZN5gimli4read4line18parse_directory_v517h7ed9e48bb2fa518bE, symObjAddr: 0x12DAC, symBinAddr: 0x13778, symSize: 0xE8 }
  - { offset: 0x5035A, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read4line27FileEntry$LT$R$C$Offset$GT$5parse17h0cf5e73c1698de2fE', symObjAddr: 0x12E94, symBinAddr: 0x13860, symSize: 0x218 }
  - { offset: 0x50A29, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read5dwarf14Dwarf$LT$R$GT$11attr_string17hc40cc697c7ad324fE', symObjAddr: 0x17104, symBinAddr: 0x17AD0, symSize: 0x1E8 }
  - { offset: 0x51260, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read5dwarf13Unit$LT$R$GT$3new17h9c8abef5d8c50a6dE', symObjAddr: 0x15394, symBinAddr: 0x15D60, symSize: 0x1D70 }
  - { offset: 0x54E38, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read5index18UnitIndex$LT$R$GT$5parse17haa9f95b067464b66E', symObjAddr: 0x172EC, symBinAddr: 0x17CB8, symSize: 0x6CC }
  - { offset: 0x553F5, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read7aranges30ArangeHeader$LT$R$C$Offset$GT$5parse17h20619b15205da501E', symObjAddr: 0x17E2C, symBinAddr: 0x187F8, symSize: 0x2A8 }
  - { offset: 0x55C99, size: 0x8, addend: 0x0, symName: '__ZN5gimli4read8rnglists20RngListIter$LT$R$GT$4next17h7e27fd598c933045E', symObjAddr: 0x180D4, symBinAddr: 0x18AA0, symSize: 0xC74 }
  - { offset: 0x58B88, size: 0x8, addend: 0x0, symName: '__ZN9addr2line8function17Function$LT$R$GT$14parse_children17he1d2dd4eaf8a7357E', symObjAddr: 0x1DDF8, symBinAddr: 0x1E214, symSize: 0xE88 }
  - { offset: 0x5A3B7, size: 0x8, addend: 0x0, symName: __ZN9addr2line8function10name_entry17hd712c6a5749c0b0eE, symObjAddr: 0x1DA8C, symBinAddr: 0x1DEA8, symSize: 0x36C }
  - { offset: 0x5A87C, size: 0x8, addend: 0x0, symName: __ZN9addr2line8function9name_attr17h41adbb1d91e81904E, symObjAddr: 0x1EC80, symBinAddr: 0x1F09C, symSize: 0x1C0 }
  - { offset: 0x5AA96, size: 0x8, addend: 0x0, symName: '__ZN9addr2line4unit16ResUnit$LT$R$GT$25find_function_or_location17h353b18e237b7b2c9E', symObjAddr: 0x1BBAC, symBinAddr: 0x1BFC8, symSize: 0x39C }
  - { offset: 0x5AFED, size: 0x8, addend: 0x0, symName: '__ZN9addr2line4unit16ResUnit$LT$R$GT$25find_function_or_location28_$u7b$$u7b$closure$u7d$$u7d$17h6a4d5a45c96dd11cE', symObjAddr: 0x1BF48, symBinAddr: 0x1C364, symSize: 0x1490 }
  - { offset: 0x5D912, size: 0x8, addend: 0x0, symName: '__ZN9addr2line16Context$LT$R$GT$9find_unit17h672f8cef0a6cef5fE', symObjAddr: 0x19A34, symBinAddr: 0x19E50, symSize: 0x168 }
  - { offset: 0x5DAA2, size: 0x8, addend: 0x0, symName: __ZN9addr2line4line11render_file17h56c7df47359e37daE, symObjAddr: 0x19B9C, symBinAddr: 0x19FB8, symSize: 0x4C0 }
  - { offset: 0x5E3AB, size: 0x8, addend: 0x0, symName: __ZN9addr2line4line9LazyLines6borrow17ha6d975a019e23e93E, symObjAddr: 0x1A05C, symBinAddr: 0x1A478, symSize: 0x1B50 }
  - { offset: 0x617D1, size: 0x8, addend: 0x0, symName: '__ZN9addr2line6lookup30LoopingLookup$LT$T$C$L$C$F$GT$10new_lookup17h590d4f5c6451cf3cE', symObjAddr: 0x1D654, symBinAddr: 0x1DA70, symSize: 0x438 }
  - { offset: 0x61D62, size: 0x8, addend: 0x0, symName: '__ZN9addr2line5frame18FrameIter$LT$R$GT$4next17h4512ac685f8f4647E', symObjAddr: 0x1D3D8, symBinAddr: 0x1D7F4, symSize: 0x27C }
  - { offset: 0x621EE, size: 0x8, addend: 0x0, symName: __ZN6object4read7archive13ArchiveMember5parse17he23e9c7a2dcdb639E, symObjAddr: 0x19204, symBinAddr: 0x1977C, symSize: 0x41C }
  - { offset: 0x62D72, size: 0x8, addend: 0x0, symName: __ZN6object4read7archive23parse_bsd_extended_name17hc2b3d25eef9eeec9E, symObjAddr: 0x19620, symBinAddr: 0x19B98, symSize: 0x250 }
  - { offset: 0x63B08, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_0, symObjAddr: 0x4E488, symBinAddr: 0x2C5B4, symSize: 0x24 }
  - { offset: 0x63B20, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_1, symObjAddr: 0x4E4AC, symBinAddr: 0x2C5D8, symSize: 0x8 }
  - { offset: 0x63B38, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_2, symObjAddr: 0x4E4B4, symBinAddr: 0x2C5E0, symSize: 0x10 }
  - { offset: 0x63B50, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_4, symObjAddr: 0x4E4CC, symBinAddr: 0x2C5F0, symSize: 0x8 }
  - { offset: 0x63B68, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_5, symObjAddr: 0x4E4D4, symBinAddr: 0x2C5F8, symSize: 0xC }
  - { offset: 0x63B9D, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr49drop_in_place$LT$panic_unwind..imp..Exception$GT$17h82c2656372694058E', symObjAddr: 0x0, symBinAddr: 0x2C604, symSize: 0x78 }
  - { offset: 0x63BC1, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr49drop_in_place$LT$panic_unwind..imp..Exception$GT$17h82c2656372694058E', symObjAddr: 0x0, symBinAddr: 0x2C604, symSize: 0x78 }
  - { offset: 0x63CC6, size: 0x8, addend: 0x0, symName: '__ZN4core3ptr74drop_in_place$LT$alloc..boxed..Box$LT$panic_unwind..imp..Exception$GT$$GT$17h401304109b323233E', symObjAddr: 0x78, symBinAddr: 0x2C67C, symSize: 0x8C }
  - { offset: 0x63F8A, size: 0x8, addend: 0x0, symName: __ZN12panic_unwind3imp5panic17exception_cleanup17h71dcf2e4fc4aa769E, symObjAddr: 0x214, symBinAddr: 0x2C818, symSize: 0x18 }
  - { offset: 0x63FA6, size: 0x8, addend: 0x0, symName: ___rust_panic_cleanup, symObjAddr: 0x104, symBinAddr: 0x2C708, symSize: 0x68 }
  - { offset: 0x64048, size: 0x8, addend: 0x0, symName: ___rust_start_panic, symObjAddr: 0x16C, symBinAddr: 0x2C770, symSize: 0xA8 }
  - { offset: 0x641E5, size: 0x8, addend: 0x0, symName: '__ZN69_$LT$core..alloc..layout..LayoutError$u20$as$u20$core..fmt..Debug$GT$3fmt17h847390e718c251f1E', symObjAddr: 0x1C0, symBinAddr: 0x34FE8, symSize: 0x20 }
  - { offset: 0x644F2, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec17capacity_overflow17hf0a187d8de84083bE, symObjAddr: 0x1E0, symBinAddr: 0x35008, symSize: 0x34 }
  - { offset: 0x64532, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec19RawVec$LT$T$C$A$GT$8grow_one17h207f2a977af11d07E', symObjAddr: 0x214, symBinAddr: 0x3503C, symSize: 0xAC }
  - { offset: 0x6469E, size: 0x8, addend: 0x0, symName: '__ZN5alloc7raw_vec20RawVecInner$LT$A$GT$7reserve21do_reserve_and_handle17hf907109019113e80E', symObjAddr: 0x2C0, symBinAddr: 0x3B418, symSize: 0xB0 }
  - { offset: 0x6479D, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec11finish_grow17h91993be477efd13cE, symObjAddr: 0x370, symBinAddr: 0x3B4C8, symSize: 0x88 }
  - { offset: 0x64812, size: 0x8, addend: 0x0, symName: __ZN5alloc7raw_vec12handle_error17h7f0cbdf2396d2d52E, symObjAddr: 0x3F8, symBinAddr: 0x3B550, symSize: 0x18 }
  - { offset: 0x64901, size: 0x8, addend: 0x0, symName: __ZN5alloc5alloc18handle_alloc_error17h14470fb7117d6c37E, symObjAddr: 0x410, symBinAddr: 0x3B568, symSize: 0x18 }
  - { offset: 0x64987, size: 0x8, addend: 0x0, symName: '__ZN93_$LT$alloc..collections..btree..mem..replace..PanicGuard$u20$as$u20$core..ops..drop..Drop$GT$4drop17h4dbb92e5b56c174aE', symObjAddr: 0xE10, symBinAddr: 0x350E8, symSize: 0xC }
  - { offset: 0x64AC3, size: 0x8, addend: 0x0, symName: __ZN5alloc6string6String15from_utf8_lossy17h7d31b366d97043dfE, symObjAddr: 0x2F28, symBinAddr: 0x35370, symSize: 0x238 }
  - { offset: 0x6500C, size: 0x8, addend: 0x0, symName: '__ZN98_$LT$alloc..string..String$u20$as$u20$core..convert..From$LT$alloc..borrow..Cow$LT$str$GT$$GT$$GT$4from17hde800050cd4f6a42E', symObjAddr: 0x4D48, symBinAddr: 0x355A8, symSize: 0xBC }
  - { offset: 0x65231, size: 0x8, addend: 0x0, symName: '__ZN72_$LT$$RF$str$u20$as$u20$alloc..ffi..c_str..CString..new..SpecNewImpl$GT$13spec_new_impl17h37cb9b6572c7448bE', symObjAddr: 0xEA8, symBinAddr: 0x350F4, symSize: 0x11C }
  - { offset: 0x65471, size: 0x8, addend: 0x0, symName: __ZN5alloc3ffi5c_str7CString19_from_vec_unchecked17h8dccb886daea0b28E, symObjAddr: 0x1110, symBinAddr: 0x35210, symSize: 0x160 }
  - { offset: 0x6579A, size: 0x8, addend: 0x0, symName: __ZN5alloc4sync32arcinner_layout_for_value_layout17h83c76efa9058bc15E, symObjAddr: 0x516C, symBinAddr: 0x35664, symSize: 0x74 }
  - { offset: 0x65D71, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index26slice_start_index_len_fail8do_panic7runtime17h9ddc6a7ecebba1e9E, symObjAddr: 0x1B12C, symBinAddr: 0x3A1F4, symSize: 0x50 }
  - { offset: 0x65DA4, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index26slice_start_index_len_fail17hbef4cde757a23982E, symObjAddr: 0xEAAC, symBinAddr: 0x3B994, symSize: 0xC }
  - { offset: 0x65DE9, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index24slice_end_index_len_fail8do_panic7runtime17h385f2d9d99153a75E, symObjAddr: 0x1B17C, symBinAddr: 0x3A244, symSize: 0x50 }
  - { offset: 0x65E1C, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index24slice_end_index_len_fail17ha150b75f771021fbE, symObjAddr: 0xEAB8, symBinAddr: 0x3B9A0, symSize: 0xC }
  - { offset: 0x65E61, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index22slice_index_order_fail8do_panic7runtime17he0b28f8ab2cd970eE, symObjAddr: 0x1B1CC, symBinAddr: 0x3A294, symSize: 0x50 }
  - { offset: 0x65E94, size: 0x8, addend: 0x0, symName: __ZN4core5slice5index22slice_index_order_fail17h7613890af586566aE, symObjAddr: 0xEAC4, symBinAddr: 0x3B9AC, symSize: 0xC }
  - { offset: 0x660F2, size: 0x8, addend: 0x0, symName: '__ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$15copy_from_slice17len_mismatch_fail8do_panic7runtime17h32155aa7baf5bd1dE', symObjAddr: 0x1B21C, symBinAddr: 0x3A2E4, symSize: 0x54 }
  - { offset: 0x66124, size: 0x8, addend: 0x0, symName: '__ZN4core5slice29_$LT$impl$u20$$u5b$T$u5d$$GT$15copy_from_slice17len_mismatch_fail17h9236ad3844e86423E', symObjAddr: 0xEC10, symBinAddr: 0x3B9B8, symSize: 0x18 }
  - { offset: 0x66299, size: 0x8, addend: 0x0, symName: __ZN4core5slice6memchr14memchr_aligned17h6296e9c99467795dE, symObjAddr: 0xE3FC, symBinAddr: 0x37B20, symSize: 0xE8 }
  - { offset: 0x663B9, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6stable5drift11sqrt_approx17hea6fce954f57e78dE, symObjAddr: 0xE604, symBinAddr: 0x37C08, symSize: 0x38 }
  - { offset: 0x66435, size: 0x8, addend: 0x0, symName: __ZN4core5slice4sort6shared9smallsort22panic_on_ord_violation17h87158ad44664865dE, symObjAddr: 0xE63C, symBinAddr: 0x3B95C, symSize: 0x38 }
  - { offset: 0x665B3, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$str$u20$as$u20$core..fmt..Display$GT$3fmt17hf009c24fe723987dE', symObjAddr: 0xE0B4, symBinAddr: 0x378A0, symSize: 0x20 }
  - { offset: 0x66758, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter12pad_integral17hc0a697f6bef9bc99E, symObjAddr: 0xB878, symBinAddr: 0x36730, symSize: 0x3E8 }
  - { offset: 0x66A4B, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter3pad17hc45fb048fd353932E, symObjAddr: 0xBCDC, symBinAddr: 0x36B94, symSize: 0x370 }
  - { offset: 0x66F01, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter9write_str17h325369b1c335017bE, symObjAddr: 0xC49C, symBinAddr: 0x36F04, symSize: 0x1C }
  - { offset: 0x66F14, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter12debug_struct17h01892abf43f6400aE, symObjAddr: 0xC4B8, symBinAddr: 0x36F20, symSize: 0x3C }
  - { offset: 0x66F52, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter26debug_struct_field1_finish17h9a6aad483cd91d8bE, symObjAddr: 0xC4F4, symBinAddr: 0x36F5C, symSize: 0xD0 }
  - { offset: 0x67027, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter26debug_struct_field2_finish17h50e0aebce8a58786E, symObjAddr: 0xC5C4, symBinAddr: 0x3702C, symSize: 0x104 }
  - { offset: 0x670FC, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter11debug_tuple17hc10fd19a9736e89bE, symObjAddr: 0xCBD8, symBinAddr: 0x37130, symSize: 0x50 }
  - { offset: 0x6714F, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter25debug_tuple_field1_finish17ha7de20751beabbe9E, symObjAddr: 0xCC28, symBinAddr: 0x37180, symSize: 0x168 }
  - { offset: 0x67350, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter25debug_tuple_field2_finish17h4e3f51f71b24019cE, symObjAddr: 0xCD90, symBinAddr: 0x372E8, symSize: 0x1E0 }
  - { offset: 0x67607, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter10debug_list17h017ce85e0805b545E, symObjAddr: 0xDB70, symBinAddr: 0x374C8, symSize: 0x48 }
  - { offset: 0x676A7, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders11DebugStruct5field17h2f0dca0d2df6974aE, symObjAddr: 0x84A0, symBinAddr: 0x35F28, symSize: 0x1B8 }
  - { offset: 0x678A5, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders11DebugStruct6finish17he06e5b5d674203a3E, symObjAddr: 0x871C, symBinAddr: 0x360E0, symSize: 0x80 }
  - { offset: 0x6799F, size: 0x8, addend: 0x0, symName: '__ZN68_$LT$core..fmt..builders..PadAdapter$u20$as$u20$core..fmt..Write$GT$9write_str17hfaa313c4119f7ed4E', symObjAddr: 0x81D0, symBinAddr: 0x35C58, symSize: 0x250 }
  - { offset: 0x67C49, size: 0x8, addend: 0x0, symName: '__ZN68_$LT$core..fmt..builders..PadAdapter$u20$as$u20$core..fmt..Write$GT$10write_char17hde86c020ea7d2eadE', symObjAddr: 0x8420, symBinAddr: 0x35EA8, symSize: 0x80 }
  - { offset: 0x67CC7, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders10DebugTuple5field17hd1be49f816fc5cc6E, symObjAddr: 0x879C, symBinAddr: 0x36160, symSize: 0x14C }
  - { offset: 0x67E51, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders10DebugTuple6finish17h27cf27fbab1a8a2aE, symObjAddr: 0x89A8, symBinAddr: 0x362AC, symSize: 0xB0 }
  - { offset: 0x67F96, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders8DebugSet5entry17h30a73ad81f14a0f2E, symObjAddr: 0x8A58, symBinAddr: 0x3635C, symSize: 0x14C }
  - { offset: 0x68139, size: 0x8, addend: 0x0, symName: __ZN4core3fmt8builders9DebugList6finish17h106b57f1a22a675eE, symObjAddr: 0x8D7C, symBinAddr: 0x364A8, symSize: 0x4C }
  - { offset: 0x681EE, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u32$GT$3fmt17hbf1c5d9b6583f29fE', symObjAddr: 0x19B78, symBinAddr: 0x39D38, symSize: 0x1C }
  - { offset: 0x68213, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$u64$GT$3fmt17h339ac4429cb37bf0E', symObjAddr: 0x19CE0, symBinAddr: 0x39EA0, symSize: 0x1C }
  - { offset: 0x68238, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp52_$LT$impl$u20$core..fmt..Display$u20$for$u20$i32$GT$3fmt17hf55f78073eda7290E', symObjAddr: 0x19B94, symBinAddr: 0x39D54, symSize: 0x28 }
  - { offset: 0x6829A, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp51_$LT$impl$u20$core..fmt..Display$u20$for$u20$u8$GT$3fmt17h4f4911bf2ed93697E', symObjAddr: 0x198A4, symBinAddr: 0x39C88, symSize: 0xB0 }
  - { offset: 0x6835F, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp21_$LT$impl$u20$u32$GT$4_fmt17h2969b048172ebb80E', symObjAddr: 0x19BBC, symBinAddr: 0x39D7C, symSize: 0x124 }
  - { offset: 0x6843F, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num3imp21_$LT$impl$u20$u64$GT$4_fmt17h99108e3731ddbd8bE', symObjAddr: 0x19D24, symBinAddr: 0x39EBC, symSize: 0x130 }
  - { offset: 0x68646, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i32$GT$3fmt17ha0cbda09c03c5041E', symObjAddr: 0x18C80, symBinAddr: 0x39A88, symSize: 0x80 }
  - { offset: 0x686E9, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i32$GT$3fmt17heb47dcda93036a36E', symObjAddr: 0x18D00, symBinAddr: 0x39B08, symSize: 0x80 }
  - { offset: 0x6878C, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i64$GT$3fmt17hf3c2c8f0bf48439fE', symObjAddr: 0x18E60, symBinAddr: 0x39B88, symSize: 0x80 }
  - { offset: 0x6882F, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num53_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i64$GT$3fmt17h4be4db71703d1dabE', symObjAddr: 0x18EE0, symBinAddr: 0x39C08, symSize: 0x80 }
  - { offset: 0x688C6, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num52_$LT$impl$u20$core..fmt..LowerHex$u20$for$u20$i8$GT$3fmt17h7bff1931c71ac012E', symObjAddr: 0x188C0, symBinAddr: 0x39988, symSize: 0x80 }
  - { offset: 0x68971, size: 0x8, addend: 0x0, symName: '__ZN4core3fmt3num52_$LT$impl$u20$core..fmt..UpperHex$u20$for$u20$i8$GT$3fmt17h8dba84f19f74308cE', symObjAddr: 0x18940, symBinAddr: 0x39A08, symSize: 0x80 }
  - { offset: 0x68A2A, size: 0x8, addend: 0x0, symName: '__ZN57_$LT$core..fmt..Formatter$u20$as$u20$core..fmt..Write$GT$10write_char17h712a7a5b14df20b9E', symObjAddr: 0xDCC0, symBinAddr: 0x37510, symSize: 0x1C }
  - { offset: 0x68A5B, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5Write9write_fmt17h3451af32630eef07E, symObjAddr: 0xB4E4, symBinAddr: 0x364F4, symSize: 0x1C }
  - { offset: 0x68AC0, size: 0x8, addend: 0x0, symName: '__ZN59_$LT$core..fmt..Arguments$u20$as$u20$core..fmt..Display$GT$3fmt17h5a31f19e8734f16eE', symObjAddr: 0xB658, symBinAddr: 0x36510, symSize: 0x18 }
  - { offset: 0x68AEE, size: 0x8, addend: 0x0, symName: __ZN4core3fmt5write17he22fcab56bd3ec61E, symObjAddr: 0xB670, symBinAddr: 0x36528, symSize: 0x208 }
  - { offset: 0x68CFD, size: 0x8, addend: 0x0, symName: __ZN4core3fmt9Formatter12pad_integral12write_prefix17h37c97ab3a7046bd2E, symObjAddr: 0xBC60, symBinAddr: 0x36B18, symSize: 0x7C }
  - { offset: 0x68D41, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h3133fac5ab5169ddE', symObjAddr: 0x1AE44, symBinAddr: 0x39FEC, symSize: 0xEC }
  - { offset: 0x68E6E, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h54e81ade5e5de57dE', symObjAddr: 0x1AF30, symBinAddr: 0x3A0D8, symSize: 0x1C }
  - { offset: 0x68E81, size: 0x8, addend: 0x0, symName: '__ZN42_$LT$$RF$T$u20$as$u20$core..fmt..Debug$GT$3fmt17h91eecf8d28a7cefeE', symObjAddr: 0x1AF4C, symBinAddr: 0x3A0F4, symSize: 0xE4 }
  - { offset: 0x68FD4, size: 0x8, addend: 0x0, symName: '__ZN40_$LT$str$u20$as$u20$core..fmt..Debug$GT$3fmt17hd8879d369458d53aE', symObjAddr: 0xDD40, symBinAddr: 0x3752C, symSize: 0x374 }
  - { offset: 0x69429, size: 0x8, addend: 0x0, symName: '__ZN41_$LT$char$u20$as$u20$core..fmt..Debug$GT$3fmt17ha35a21095fe6b2aeE', symObjAddr: 0xE0D4, symBinAddr: 0x378C0, symSize: 0xAC }
  - { offset: 0x6952C, size: 0x8, addend: 0x0, symName: '__ZN43_$LT$char$u20$as$u20$core..fmt..Display$GT$3fmt17h3667dcfbeb89f1b7E', symObjAddr: 0xE180, symBinAddr: 0x3796C, symSize: 0xF0 }
  - { offset: 0x695B1, size: 0x8, addend: 0x0, symName: __ZN4core3fmt17pointer_fmt_inner17ha05a056f67b250baE, symObjAddr: 0xE270, symBinAddr: 0x37A5C, symSize: 0xC4 }
  - { offset: 0x6966A, size: 0x8, addend: 0x0, symName: '__ZN44_$LT$$RF$T$u20$as$u20$core..fmt..Display$GT$3fmt17h09a056bd82e73243E', symObjAddr: 0x1B044, symBinAddr: 0x3A1D8, symSize: 0x1C }
  - { offset: 0x697FB, size: 0x8, addend: 0x0, symName: __ZN4core6option13unwrap_failed17h84f662fc2717d129E, symObjAddr: 0x7B40, symBinAddr: 0x3B5C4, symSize: 0x1C }
  - { offset: 0x69815, size: 0x8, addend: 0x0, symName: __ZN4core6option13expect_failed17h064f2cf84916882aE, symObjAddr: 0x7B5C, symBinAddr: 0x3B5E0, symSize: 0x48 }
  - { offset: 0x6986C, size: 0x8, addend: 0x0, symName: '__ZN71_$LT$core..ops..range..Range$LT$Idx$GT$$u20$as$u20$core..fmt..Debug$GT$3fmt17h8610e207370f6ef7E', symObjAddr: 0x442C, symBinAddr: 0x356D8, symSize: 0x1FC }
  - { offset: 0x69D31, size: 0x8, addend: 0x0, symName: __ZN4core6result13unwrap_failed17h2bf1684417afbe6bE, symObjAddr: 0x80FC, symBinAddr: 0x3B900, symSize: 0x5C }
  - { offset: 0x69FCF, size: 0x8, addend: 0x0, symName: __ZN4core3str7pattern11StrSearcher3new17habaad989c72fb7a8E, symObjAddr: 0x10248, symBinAddr: 0x38858, symSize: 0x904 }
  - { offset: 0x6A26D, size: 0x8, addend: 0x0, symName: __ZN4core3str5count14do_count_chars17hc555c670c9b10e3bE, symObjAddr: 0xF128, symBinAddr: 0x37E84, symSize: 0x724 }
  - { offset: 0x6A5B6, size: 0x8, addend: 0x0, symName: __ZN4core3str5count23char_count_general_case17h0a6ef21a4825751aE, symObjAddr: 0xF84C, symBinAddr: 0x385A8, symSize: 0x2B0 }
  - { offset: 0x6A70F, size: 0x8, addend: 0x0, symName: __ZN4core3str6traits23str_index_overflow_fail17h6ba0069023d715bdE, symObjAddr: 0x10214, symBinAddr: 0x3B9D0, symSize: 0x34 }
  - { offset: 0x6A743, size: 0x8, addend: 0x0, symName: __ZN4core3str8converts9from_utf817h5b7081f8e3c98111E, symObjAddr: 0xECA0, symBinAddr: 0x37C40, symSize: 0x244 }
  - { offset: 0x6A7C3, size: 0x8, addend: 0x0, symName: '__ZN87_$LT$core..str..lossy..Utf8Chunks$u20$as$u20$core..iter..traits..iterator..Iterator$GT$4next17hc0bb2f8797014ed2E', symObjAddr: 0x10F30, symBinAddr: 0x3915C, symSize: 0x1DC }
  - { offset: 0x6A9C6, size: 0x8, addend: 0x0, symName: __ZN4core3str16slice_error_fail17h8b847e853482f553E, symObjAddr: 0x111D8, symBinAddr: 0x3BA04, symSize: 0xC }
  - { offset: 0x6A9E0, size: 0x8, addend: 0x0, symName: __ZN4core3str19slice_error_fail_rt17hd494714b9640772bE, symObjAddr: 0x11218, symBinAddr: 0x39338, symSize: 0x390 }
  - { offset: 0x6AC82, size: 0x8, addend: 0x0, symName: '__ZN4core4char7methods22_$LT$impl$u20$char$GT$16escape_debug_ext17hbfb443f01c84243eE', symObjAddr: 0x50B8, symBinAddr: 0x358F8, symSize: 0x260 }
  - { offset: 0x6AEC5, size: 0x8, addend: 0x0, symName: '__ZN63_$LT$core..cell..BorrowMutError$u20$as$u20$core..fmt..Debug$GT$3fmt17h4f632190c10a8f91E', symObjAddr: 0x4F3C, symBinAddr: 0x358D4, symSize: 0x24 }
  - { offset: 0x6AF21, size: 0x8, addend: 0x0, symName: __ZN4core4cell22panic_already_borrowed17ha893d1f06b8b63deE, symObjAddr: 0x4F80, symBinAddr: 0x3B580, symSize: 0x44 }
  - { offset: 0x6AF6F, size: 0x8, addend: 0x0, symName: __ZN4core7unicode12unicode_data15grapheme_extend11lookup_slow17hbcdf8b5a821a33ffE, symObjAddr: 0x1C978, symBinAddr: 0x3A35C, symSize: 0x174 }
  - { offset: 0x6B153, size: 0x8, addend: 0x0, symName: __ZN4core7unicode12unicode_data2cc6lookup17he25d7ab2d2fa0f97E, symObjAddr: 0x1C954, symBinAddr: 0x3A338, symSize: 0x24 }
  - { offset: 0x6B1BA, size: 0x8, addend: 0x0, symName: __ZN4core7unicode9printable12is_printable17haae76351f6525626E, symObjAddr: 0x12144, symBinAddr: 0x396C8, symSize: 0x2C0 }
  - { offset: 0x6B5F6, size: 0x8, addend: 0x0, symName: __ZN4core3ffi5c_str4CStr19from_bytes_with_nul17h10506affebd054b1E, symObjAddr: 0x5700, symBinAddr: 0x35B58, symSize: 0x100 }
  - { offset: 0x6B6E3, size: 0x8, addend: 0x0, symName: __ZN4core9panicking9panic_fmt17h6a4014bec58fba4fE, symObjAddr: 0x7C94, symBinAddr: 0x3B628, symSize: 0x20 }
  - { offset: 0x6B726, size: 0x8, addend: 0x0, symName: __ZN4core9panicking18panic_nounwind_fmt17hc1d4bd06ab2bbbabE, symObjAddr: 0x7CB4, symBinAddr: 0x3B648, symSize: 0x3C }
  - { offset: 0x6B76B, size: 0x8, addend: 0x0, symName: __ZN4core9panicking5panic17h3ce9043ca357f318E, symObjAddr: 0x7CF0, symBinAddr: 0x3B684, symSize: 0x34 }
  - { offset: 0x6B79A, size: 0x8, addend: 0x0, symName: __ZN4core9panicking14panic_nounwind17h96b8083b1a8e2dbdE, symObjAddr: 0x7D24, symBinAddr: 0x3B6B8, symSize: 0x3C }
  - { offset: 0x6B7C9, size: 0x8, addend: 0x0, symName: __ZN4core9panicking26panic_nounwind_nobacktrace17h984deb6b7d0bc614E, symObjAddr: 0x7D60, symBinAddr: 0x3B6F4, symSize: 0x3C }
  - { offset: 0x6B7F7, size: 0x8, addend: 0x0, symName: __ZN4core9panicking18panic_bounds_check17h1ecfa3d00f46f81cE, symObjAddr: 0x7DE4, symBinAddr: 0x3B730, symSize: 0x50 }
  - { offset: 0x6B828, size: 0x8, addend: 0x0, symName: __ZN4core9panicking19panic_cannot_unwind17ha5edf28dd2569dc8E, symObjAddr: 0x7EF8, symBinAddr: 0x3B780, symSize: 0x18 }
  - { offset: 0x6B842, size: 0x8, addend: 0x0, symName: __ZN4core9panicking16panic_in_cleanup17hb960b8c5dea287d4E, symObjAddr: 0x7F10, symBinAddr: 0x3B798, symSize: 0x18 }
  - { offset: 0x6B85C, size: 0x8, addend: 0x0, symName: __ZN4core9panicking13assert_failed17h2af7932842d934b6E, symObjAddr: 0x7F8C, symBinAddr: 0x3B7B0, symSize: 0x24 }
  - { offset: 0x6B876, size: 0x8, addend: 0x0, symName: __ZN4core9panicking13assert_failed17hc7c9e0269eebc316E, symObjAddr: 0x7FB0, symBinAddr: 0x3B7D4, symSize: 0x24 }
  - { offset: 0x6B890, size: 0x8, addend: 0x0, symName: __ZN4core9panicking19assert_failed_inner17hafd35c69af0230bfE, symObjAddr: 0x7FF4, symBinAddr: 0x3B7F8, symSize: 0x108 }
  - { offset: 0x6B8DD, size: 0x8, addend: 0x0, symName: __ZN4core9panicking11panic_const23panic_const_div_by_zero17h0422238fbdaad2d7E, symObjAddr: 0x179B8, symBinAddr: 0x3BA10, symSize: 0x34 }
  - { offset: 0x6B90C, size: 0x8, addend: 0x0, symName: __ZN4core9panicking11panic_const23panic_const_rem_by_zero17h723f338bbd7af040E, symObjAddr: 0x179EC, symBinAddr: 0x3BA44, symSize: 0x34 }
  - { offset: 0x6B980, size: 0x8, addend: 0x0, symName: _OUTLINED_FUNCTION_0, symObjAddr: 0x1D194, symBinAddr: 0x3A4D0, symSize: 0x10 }
...
