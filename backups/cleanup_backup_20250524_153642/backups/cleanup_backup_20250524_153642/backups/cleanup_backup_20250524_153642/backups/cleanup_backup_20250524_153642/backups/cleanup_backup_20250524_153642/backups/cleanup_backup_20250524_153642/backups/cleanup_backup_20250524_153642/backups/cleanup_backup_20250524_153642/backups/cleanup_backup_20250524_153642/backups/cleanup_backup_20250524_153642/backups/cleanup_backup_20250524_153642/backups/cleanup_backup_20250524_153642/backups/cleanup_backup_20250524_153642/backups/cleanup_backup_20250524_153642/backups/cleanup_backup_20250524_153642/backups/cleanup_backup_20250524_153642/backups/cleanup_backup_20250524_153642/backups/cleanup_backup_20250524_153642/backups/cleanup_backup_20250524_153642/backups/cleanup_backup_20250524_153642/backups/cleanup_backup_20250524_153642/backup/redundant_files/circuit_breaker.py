#!/usr/bin/env python3
"""
Circuit Breaker Module for Q5 Trading System

This module provides circuit breaker functionality to prevent cascading failures
by temporarily disabling operations that are failing repeatedly.
"""

import time
import logging
import asyncio
import functools
from enum import Enum
from typing import Dict, Any, Callable, Optional, TypeVar, Awaitable, Union, cast

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('circuit_breaker')

# Type variables for function signatures
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])
AsyncF = TypeVar('AsyncF', bound=Callable[..., Awaitable[Any]])

class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = 'CLOSED'  # Normal operation, requests pass through
    OPEN = 'OPEN'      # Circuit is open, requests fail fast
    HALF_OPEN = 'HALF_OPEN'  # Testing if service is back to normal


class CircuitBreaker:
    """
    Circuit breaker implementation to prevent cascading failures.
    """
    
    def __init__(self,
                 name: str,
                 failure_threshold: int = 5,
                 reset_timeout: float = 60.0,
                 half_open_max_calls: int = 1,
                 exclude_exceptions: list = None):
        """
        Initialize the CircuitBreaker.
        
        Args:
            name: Name of the circuit breaker
            failure_threshold: Number of failures before opening circuit
            reset_timeout: Time in seconds before attempting reset
            half_open_max_calls: Maximum calls allowed in half-open state
            exclude_exceptions: List of exception types to ignore
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.half_open_max_calls = half_open_max_calls
        self.exclude_exceptions = exclude_exceptions or []
        
        # State
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
        self.half_open_calls = 0
        
        # Metrics
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.rejected_calls = 0
        
        logger.info(f"Circuit breaker '{name}' initialized with threshold={failure_threshold}, "
                   f"reset_timeout={reset_timeout}s")
    
    def _should_allow_request(self) -> bool:
        """
        Determine if a request should be allowed based on circuit state.
        
        Returns:
            True if request should be allowed, False otherwise
        """
        current_time = time.time()
        
        if self.state == CircuitState.CLOSED:
            return True
        
        if self.state == CircuitState.OPEN:
            # Check if reset timeout has elapsed
            if current_time - self.last_failure_time >= self.reset_timeout:
                logger.info(f"Circuit '{self.name}' transitioning from OPEN to HALF_OPEN")
                self.state = CircuitState.HALF_OPEN
                self.half_open_calls = 0
                return True
            return False
        
        if self.state == CircuitState.HALF_OPEN:
            # Allow limited calls in half-open state
            if self.half_open_calls < self.half_open_max_calls:
                self.half_open_calls += 1
                return True
            return False
        
        return True
    
    def _on_success(self) -> None:
        """Handle successful call."""
        self.total_calls += 1
        self.successful_calls += 1
        
        if self.state == CircuitState.HALF_OPEN:
            logger.info(f"Circuit '{self.name}' transitioning from HALF_OPEN to CLOSED")
            self.state = CircuitState.CLOSED
            self.failure_count = 0
    
    def _on_failure(self, exception: Exception) -> None:
        """
        Handle failed call.
        
        Args:
            exception: Exception that caused the failure
        """
        self.total_calls += 1
        self.failed_calls += 1
        
        # Check if exception should be excluded
        if any(isinstance(exception, exc_type) for exc_type in self.exclude_exceptions):
            logger.debug(f"Circuit '{self.name}' ignoring excluded exception: {str(exception)}")
            return
        
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == CircuitState.CLOSED and self.failure_count >= self.failure_threshold:
            logger.warning(f"Circuit '{self.name}' transitioning from CLOSED to OPEN "
                          f"after {self.failure_count} failures")
            self.state = CircuitState.OPEN
        
        if self.state == CircuitState.HALF_OPEN:
            logger.warning(f"Circuit '{self.name}' transitioning from HALF_OPEN to OPEN "
                          f"after failure: {str(exception)}")
            self.state = CircuitState.OPEN
    
    def _on_rejected(self) -> None:
        """Handle rejected call."""
        self.total_calls += 1
        self.rejected_calls += 1
    
    def call(self, func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
        """
        Call a function with circuit breaker protection.
        
        Args:
            func: Function to call
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Result of the function call
            
        Raises:
            CircuitBreakerError: If circuit is open
            Exception: Any exception raised by the function
        """
        if not self._should_allow_request():
            self._on_rejected()
            raise CircuitBreakerError(f"Circuit '{self.name}' is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure(e)
            raise
    
    async def call_async(self, func: Callable[..., Awaitable[T]], *args: Any, **kwargs: Any) -> T:
        """
        Call an async function with circuit breaker protection.
        
        Args:
            func: Async function to call
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Result of the async function call
            
        Raises:
            CircuitBreakerError: If circuit is open
            Exception: Any exception raised by the function
        """
        if not self._should_allow_request():
            self._on_rejected()
            raise CircuitBreakerError(f"Circuit '{self.name}' is OPEN")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure(e)
            raise
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get circuit breaker metrics.
        
        Returns:
            Dict containing metrics
        """
        return {
            'name': self.name,
            'state': self.state.value,
            'failure_count': self.failure_count,
            'total_calls': self.total_calls,
            'successful_calls': self.successful_calls,
            'failed_calls': self.failed_calls,
            'rejected_calls': self.rejected_calls,
            'last_failure_time': self.last_failure_time,
            'failure_threshold': self.failure_threshold,
            'reset_timeout': self.reset_timeout
        }


class CircuitBreakerError(Exception):
    """Exception raised when a circuit breaker is open."""
    pass


def circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    reset_timeout: float = 60.0,
    exclude_exceptions: list = None
) -> Callable[[F], F]:
    """
    Decorator for applying circuit breaker to functions.
    
    Args:
        name: Name of the circuit breaker
        failure_threshold: Number of failures before opening circuit
        reset_timeout: Time in seconds before attempting reset
        exclude_exceptions: List of exception types to ignore
        
    Returns:
        Decorated function
    """
    circuit = CircuitBreaker(
        name=name,
        failure_threshold=failure_threshold,
        reset_timeout=reset_timeout,
        exclude_exceptions=exclude_exceptions or []
    )
    
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            return circuit.call(func, *args, **kwargs)
        return cast(F, wrapper)
    
    return decorator


def async_circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    reset_timeout: float = 60.0,
    exclude_exceptions: list = None
) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator for applying circuit breaker to async functions.
    
    Args:
        name: Name of the circuit breaker
        failure_threshold: Number of failures before opening circuit
        reset_timeout: Time in seconds before attempting reset
        exclude_exceptions: List of exception types to ignore
        
    Returns:
        Decorated async function
    """
    circuit = CircuitBreaker(
        name=name,
        failure_threshold=failure_threshold,
        reset_timeout=reset_timeout,
        exclude_exceptions=exclude_exceptions or []
    )
    
    def decorator(func: AsyncF) -> AsyncF:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            return await circuit.call_async(func, *args, **kwargs)
        return cast(AsyncF, wrapper)
    
    return decorator
