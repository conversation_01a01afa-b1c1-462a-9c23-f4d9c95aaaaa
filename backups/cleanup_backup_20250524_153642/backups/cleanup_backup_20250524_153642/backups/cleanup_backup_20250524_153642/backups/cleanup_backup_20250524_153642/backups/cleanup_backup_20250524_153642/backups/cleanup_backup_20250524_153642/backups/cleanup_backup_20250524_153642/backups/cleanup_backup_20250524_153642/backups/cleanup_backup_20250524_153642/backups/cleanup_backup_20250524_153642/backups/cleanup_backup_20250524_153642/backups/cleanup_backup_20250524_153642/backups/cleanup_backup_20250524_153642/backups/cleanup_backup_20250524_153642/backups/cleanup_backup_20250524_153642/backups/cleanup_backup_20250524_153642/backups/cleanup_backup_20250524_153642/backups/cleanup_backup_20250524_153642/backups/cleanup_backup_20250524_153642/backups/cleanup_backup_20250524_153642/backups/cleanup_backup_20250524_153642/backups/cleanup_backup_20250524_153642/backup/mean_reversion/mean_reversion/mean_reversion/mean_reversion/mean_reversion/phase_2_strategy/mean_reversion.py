"""
Enhanced Mean Reversion Strategy for Synergy7 Trading System.

This module implements an enhanced mean reversion strategy with adaptive parameters
based on market conditions and improved risk management.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class MeanReversionStrategy:
    """
    Enhanced Mean Reversion Strategy.
    
    This strategy identifies overbought and oversold conditions using Bollinger Bands
    and other statistical measures, with adaptive parameters based on market volatility.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the strategy with configuration parameters.
        
        Args:
            **kwargs: Strategy parameters
        """
        self.name = "enhanced_mean_reversion"
        self.version = "2.0.0"
        
        # Extract core strategy parameters
        self.lookback_period = kwargs.get("lookback_period", 20)
        self.std_dev_multiplier = kwargs.get("std_dev_multiplier", 2.0)
        self.mean_period = kwargs.get("mean_period", 50)
        self.symbol = kwargs.get("symbol", "SOL/USD")
        
        # Risk parameters
        self.stop_loss = kwargs.get("stop_loss", 0.05)
        self.take_profit = kwargs.get("take_profit", 0.15)
        self.trailing_stop = kwargs.get("trailing_stop", 0.02)
        self.min_confidence = kwargs.get("min_confidence", 0.6)
        
        # Adaptive parameters
        self.use_adaptive_params = kwargs.get("use_adaptive_params", True)
        self.volatility_lookback = kwargs.get("volatility_lookback", 50)
        self.volatility_threshold_low = kwargs.get("volatility_threshold_low", 0.01)
        self.volatility_threshold_high = kwargs.get("volatility_threshold_high", 0.03)
        
        # Parameter sets for different volatility regimes
        self.low_volatility_params = {
            "std_dev_multiplier": kwargs.get("low_vol_std_dev", 1.5),
            "lookback_period": kwargs.get("low_vol_lookback", 15),
            "mean_period": kwargs.get("low_vol_mean_period", 30)
        }
        
        self.medium_volatility_params = {
            "std_dev_multiplier": kwargs.get("med_vol_std_dev", 2.0),
            "lookback_period": kwargs.get("med_vol_lookback", 20),
            "mean_period": kwargs.get("med_vol_mean_period", 50)
        }
        
        self.high_volatility_params = {
            "std_dev_multiplier": kwargs.get("high_vol_std_dev", 2.5),
            "lookback_period": kwargs.get("high_vol_lookback", 30),
            "mean_period": kwargs.get("high_vol_mean_period", 80)
        }
        
        # Position sizing parameters
        self.position_sizing_method = kwargs.get("position_sizing_method", "risk_based")
        self.risk_per_trade = kwargs.get("risk_per_trade", 0.01)  # 1% risk per trade
        self.atr_multiplier = kwargs.get("atr_multiplier", 1.5)
        self.atr_period = kwargs.get("atr_period", 14)
        
        logger.info(f"Initialized {self.name} strategy with lookback_period={self.lookback_period}, "
                   f"std_dev_multiplier={self.std_dev_multiplier}, mean_period={self.mean_period}")
    
    def detect_market_regime(self, df: pd.DataFrame) -> str:
        """
        Detect the current market regime based on volatility and trend.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            str: Market regime ('low_volatility', 'medium_volatility', 'high_volatility')
        """
        # Calculate historical volatility
        returns = df['close'].pct_change().dropna()
        volatility = returns.rolling(self.volatility_lookback).std().iloc[-1] * np.sqrt(252)
        
        # Determine volatility regime
        if volatility < self.volatility_threshold_low:
            return "low_volatility"
        elif volatility > self.volatility_threshold_high:
            return "high_volatility"
        else:
            return "medium_volatility"
    
    def get_adaptive_parameters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get adaptive parameters based on market conditions.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            Dict[str, Any]: Adaptive parameters
        """
        if not self.use_adaptive_params:
            return {
                "lookback_period": self.lookback_period,
                "std_dev_multiplier": self.std_dev_multiplier,
                "mean_period": self.mean_period
            }
        
        # Detect market regime
        regime = self.detect_market_regime(df)
        
        # Return parameters based on regime
        if regime == "low_volatility":
            return self.low_volatility_params
        elif regime == "high_volatility":
            return self.high_volatility_params
        else:
            return self.medium_volatility_params
    
    def generate_signals(self, df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Generate trading signals based on mean reversion strategy.
        
        Args:
            df: DataFrame with OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with signals
        """
        # Make a copy of the data
        signals = df.copy()
        
        # Get adaptive parameters
        params = self.get_adaptive_parameters(df)
        lookback_period = kwargs.get("lookback_period", params["lookback_period"])
        std_dev_multiplier = kwargs.get("std_dev_multiplier", params["std_dev_multiplier"])
        mean_period = kwargs.get("mean_period", params["mean_period"])
        
        # Check if we have enough data
        if len(signals) < max(lookback_period, mean_period):
            logger.warning(f"Not enough data for mean reversion calculation. Need at least {max(lookback_period, mean_period)} data points.")
            return pd.DataFrame()
        
        # Calculate moving average
        signals['ma'] = signals['close'].rolling(mean_period).mean()
        
        # Calculate Bollinger Bands
        signals['std'] = signals['close'].rolling(lookback_period).std()
        signals['upper_band'] = signals['ma'] + (signals['std'] * std_dev_multiplier)
        signals['lower_band'] = signals['ma'] - (signals['std'] * std_dev_multiplier)
        
        # Calculate %B (Bollinger Band percentage)
        signals['percent_b'] = (signals['close'] - signals['lower_band']) / (signals['upper_band'] - signals['lower_band'])
        
        # Calculate RSI for confirmation
        delta = signals['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        signals['rsi'] = 100 - (100 / (1 + rs))
        
        # Initialize signal column
        signals['signal'] = 0
        
        # Generate signals based on Bollinger Bands and RSI confirmation
        # Buy when price is below lower band and RSI is oversold
        signals.loc[(signals['close'] < signals['lower_band']) & (signals['rsi'] < 30), 'signal'] = 1
        
        # Sell when price is above upper band and RSI is overbought
        signals.loc[(signals['close'] > signals['upper_band']) & (signals['rsi'] > 70), 'signal'] = -1
        
        # Calculate distance from mean (for confidence calculation)
        signals['distance_from_mean'] = (signals['close'] - signals['ma']) / signals['ma']
        
        # Calculate confidence based on %B
        signals['confidence'] = 1 - signals['percent_b'].abs().clip(0, 1)
        
        # Generate position column for tracking changes
        signals['position'] = signals['signal'].diff()
        
        # Calculate ATR for position sizing
        high_low = signals['high'] - signals['low']
        high_close = np.abs(signals['high'] - signals['close'].shift())
        low_close = np.abs(signals['low'] - signals['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = np.max(ranges, axis=1)
        signals['atr'] = true_range.rolling(self.atr_period).mean()
        
        return signals
    
    def calculate_position_size(self, signals: pd.DataFrame, capital: float) -> pd.DataFrame:
        """
        Calculate position size based on risk management rules.
        
        Args:
            signals: DataFrame with signals
            capital: Available capital
            
        Returns:
            DataFrame with position sizes
        """
        # Make a copy of the signals
        result = signals.copy()
        
        # Initialize position size column
        result['position_size'] = 0.0
        
        # Calculate position size based on method
        if self.position_sizing_method == "fixed":
            # Fixed position size
            result.loc[result['signal'] != 0, 'position_size'] = capital * 0.1
        
        elif self.position_sizing_method == "percentage":
            # Percentage of capital
            result.loc[result['signal'] != 0, 'position_size'] = capital * self.risk_per_trade
        
        elif self.position_sizing_method == "risk_based":
            # Risk-based position sizing using ATR
            for idx in result.index:
                if result.loc[idx, 'signal'] != 0 and not np.isnan(result.loc[idx, 'atr']):
                    # Calculate risk amount
                    risk_amount = capital * self.risk_per_trade
                    
                    # Calculate stop loss distance in price terms
                    stop_distance = result.loc[idx, 'atr'] * self.atr_multiplier
                    
                    # Calculate position size
                    if stop_distance > 0:
                        position_size = risk_amount / stop_distance
                        result.loc[idx, 'position_size'] = position_size
        
        return result
    
    def backtest(self, data: pd.DataFrame, initial_capital: float = 10000.0) -> Dict[str, Any]:
        """
        Backtest the strategy on historical data.
        
        Args:
            data: DataFrame with OHLCV data
            initial_capital: Initial capital for backtesting
            
        Returns:
            Dictionary with backtest results
        """
        # Generate signals
        signals = self.generate_signals(data)
        
        if signals.empty:
            logger.warning("No signals generated for backtesting")
            return {
                "strategy": self.name,
                "version": self.version,
                "symbol": self.symbol,
                "initial_capital": initial_capital,
                "final_capital": initial_capital,
                "returns": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "win_rate": 0.0,
                "trades": []
            }
        
        # Calculate position sizes
        signals = self.calculate_position_size(signals, initial_capital)
        
        # Initialize portfolio and tracking variables
        portfolio = pd.DataFrame(index=signals.index)
        portfolio['holdings'] = 0.0
        portfolio['cash'] = initial_capital
        portfolio['total'] = initial_capital
        portfolio['returns'] = 0.0
        
        # Track trades
        trades = []
        current_position = 0
        entry_price = 0
        entry_date = None
        
        # Simulate trading
        for i, idx in enumerate(portfolio.index):
            if i == 0:  # Skip first row
                continue
                
            # Get signal
            signal = signals.loc[idx, 'signal']
            position_size = signals.loc[idx, 'position_size']
            
            # Update portfolio based on signal
            if signal == 1 and current_position == 0:  # Buy signal
                # Calculate shares to buy
                price = signals.loc[idx, 'close']
                shares = position_size / price
                
                # Update portfolio
                portfolio.loc[idx, 'holdings'] = shares * price
                portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] - shares * price
                
                # Record trade entry
                current_position = 1
                entry_price = price
                entry_date = idx
                
            elif signal == -1 and current_position == 0:  # Sell signal (short)
                # Calculate shares to sell
                price = signals.loc[idx, 'close']
                shares = position_size / price
                
                # Update portfolio
                portfolio.loc[idx, 'holdings'] = -shares * price
                portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] + shares * price
                
                # Record trade entry
                current_position = -1
                entry_price = price
                entry_date = idx
                
            elif (signal == -1 and current_position == 1) or (signal == 1 and current_position == -1):  # Exit and reverse
                # Calculate exit price
                price = signals.loc[idx, 'close']
                
                # Record trade exit
                exit_date = idx
                exit_price = price
                profit_loss = (exit_price - entry_price) * current_position
                profit_loss_pct = profit_loss / entry_price
                
                trades.append({
                    "entry_date": entry_date,
                    "exit_date": exit_date,
                    "entry_price": entry_price,
                    "exit_price": exit_price,
                    "position": "long" if current_position == 1 else "short",
                    "profit_loss": profit_loss,
                    "profit_loss_pct": profit_loss_pct
                })
                
                # Update portfolio for exit
                if current_position == 1:
                    shares = portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                    portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] + shares * price
                else:
                    shares = -portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                    portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] - shares * price
                
                # Update for new position
                new_shares = position_size / price
                if signal == 1:  # Buy
                    portfolio.loc[idx, 'holdings'] = new_shares * price
                    portfolio.loc[idx, 'cash'] -= new_shares * price
                else:  # Sell
                    portfolio.loc[idx, 'holdings'] = -new_shares * price
                    portfolio.loc[idx, 'cash'] += new_shares * price
                
                # Record new trade entry
                current_position = 1 if signal == 1 else -1
                entry_price = price
                entry_date = idx
            
            else:  # No signal or same direction
                # Update holdings value
                if current_position != 0:
                    if current_position == 1:
                        shares = portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        portfolio.loc[idx, 'holdings'] = shares * signals.loc[idx, 'close']
                    else:
                        shares = -portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        portfolio.loc[idx, 'holdings'] = -shares * signals.loc[idx, 'close']
                
                # Carry forward cash
                portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash']
            
            # Update total value and returns
            portfolio.loc[idx, 'total'] = portfolio.loc[idx, 'holdings'] + portfolio.loc[idx, 'cash']
            portfolio.loc[idx, 'returns'] = portfolio.loc[idx, 'total'] / portfolio.loc[portfolio.index[i-1], 'total'] - 1
        
        # Close any open position at the end
        if current_position != 0:
            exit_date = portfolio.index[-1]
            exit_price = signals.loc[exit_date, 'close']
            profit_loss = (exit_price - entry_price) * current_position
            profit_loss_pct = profit_loss / entry_price
            
            trades.append({
                "entry_date": entry_date,
                "exit_date": exit_date,
                "entry_price": entry_price,
                "exit_price": exit_price,
                "position": "long" if current_position == 1 else "short",
                "profit_loss": profit_loss,
                "profit_loss_pct": profit_loss_pct
            })
        
        # Calculate performance metrics
        total_return = portfolio['total'].iloc[-1] / initial_capital - 1
        daily_returns = portfolio['returns'].dropna()
        sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
        
        # Calculate drawdown
        portfolio['cumulative_return'] = (1 + portfolio['returns']).cumprod()
        portfolio['drawdown'] = portfolio['cumulative_return'] / portfolio['cumulative_return'].cummax() - 1
        max_drawdown = portfolio['drawdown'].min()
        
        # Calculate win rate
        winning_trades = sum(1 for trade in trades if trade['profit_loss'] > 0)
        total_trades = len(trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Calculate profit factor
        gross_profit = sum(trade['profit_loss'] for trade in trades if trade['profit_loss'] > 0)
        gross_loss = sum(abs(trade['profit_loss']) for trade in trades if trade['profit_loss'] < 0)
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        return {
            "strategy": self.name,
            "version": self.version,
            "symbol": self.symbol,
            "initial_capital": initial_capital,
            "final_capital": portfolio['total'].iloc[-1],
            "total_return": total_return,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "total_trades": total_trades,
            "trades": trades,
            "portfolio": portfolio
        }
