# Q5 Trading System Configuration
# This is an example configuration file. Copy to config.yaml and edit as needed.

# Core configuration
core:
  enabled: true
  binary_path: "bin/carbon_core"
  log_level: "info"
  max_memory_mb: 1024
  worker_threads: 4
  update_interval_ms: 100

# Mode settings
mode:
  # Only one mode can be enabled at a time
  live_trading: false
  paper_trading: true
  backtesting: false
  simulation: false

# Market Microstructure Settings
market_microstructure:
  enabled: true
  markets:
    - "SOL-USDC"
    - "JTO-USDC"
    - "BONK-USDC"
  order_book_depth: 20
  update_interval_ms: 100
  impact_window_size: 50
  liquidity_threshold: 10000

# API Settings
apis:
  helius:
    enabled: true
    api_key: "${HELIUS_API_KEY}"
    rpc_endpoint: "https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    ws_endpoint: "wss://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
  
  birdeye:
    enabled: true
    api_key: "${BIRDEYE_API_KEY}"
    endpoint: "https://api.birdeye.so/v1"
  
  jito:
    enabled: false
    rpc_url: "https://mainnet.block.jito.io"
    shredstream_url: "wss://mainnet.shredstream.jito.io/stream"
    keypair_path: "keys/jito_keypair.json"
  
  coingecko:
    enabled: true
    api_key: "${COINGECKO_API_KEY}"
    endpoint: "https://api.coingecko.com/api/v3"
  
  quicknode:
    enabled: false
    api_key: "${QUICKNODE_API_KEY}"
    endpoint: "https://lil-jito.quiknode.pro/${QUICKNODE_API_KEY}"

# RPC Settings
rpc:
  endpoint: "${HELIUS_RPC_URL}"
  commitment: "confirmed"
  max_retries: 3
  retry_delay_ms: 1000
  fallback_endpoint: "https://api.mainnet-beta.solana.com"

# Wallet Settings
wallet:
  address: "${WALLET_ADDRESS}"
  keypair_path: "keys/wallet_keypair.json"
  max_transaction_fee: 10000  # in lamports
  paper_trading_balance: 1000  # in SOL

# Signal Generation Settings
signal_generation:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000
  strategies:
    - name: "momentum_sol_usdc"
      type: "momentum"
      enabled: true
      markets:
        - "SOL-USDC"
      weight: 1.0
      parameters:
        window_size: 20
        threshold: 0.01
        max_value: 0.05
        smoothing_factor: 0.1
        max_position_size: 0.1
    
    - name: "order_book_imbalance_sol_usdc"
      type: "order_book_imbalance"
      enabled: true
      markets:
        - "SOL-USDC"
      weight: 1.0
      parameters:
        window_size: 20
        threshold: 0.1
        max_value: 0.5
        depth: 10
        smoothing_factor: 0.2
        max_position_size: 0.1

# Risk Management Settings
risk_management:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000
  metrics_interval_ms: 5000
  max_position_size: 0.1  # as a fraction of total portfolio value
  max_exposure: 0.5  # maximum total exposure as a fraction of portfolio value
  max_drawdown: 0.1  # maximum allowed drawdown before stopping trading
  var_threshold: 0.05  # Value at Risk threshold
  stop_loss_pct: 0.05  # Stop loss percentage
  take_profit_pct: 0.1  # Take profit percentage

# Transaction Preparation Settings
transaction_preparation:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000
  max_priority_fee: 1000  # in lamports
  use_rust_implementation: true  # Use Rust implementation for transaction preparation
  fallback_to_python: true  # Fall back to Python implementation if Rust fails

# Transaction Execution Settings
transaction_execution:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000
  simulation_enabled: true  # Simulate transactions before sending
  dry_run: true  # Set to false for real trading
  max_retries: 3  # Maximum number of retries for failed transactions
  retry_delay_ms: 1000  # Delay between retries in milliseconds
  slippage_tolerance: 0.01  # Maximum allowed slippage (1%)
  max_spread_pct: 0.02  # Maximum allowed spread (2%)
  order_type: "market"  # "market" or "limit"
  retry_failed_orders: true  # Whether to retry failed orders

# Communication Settings
communication:
  protocol: "zeromq"
  zeromq:
    pub_endpoint: "tcp://127.0.0.1:5555"
    sub_endpoint: "tcp://127.0.0.1:5556"
    req_endpoint: "tcp://127.0.0.1:5557"
  message_format: "json"
  max_message_size: 1048576
  heartbeat_interval_ms: 1000

# Monitoring Settings
monitoring:
  enabled: true
  log_level: "info"
  metrics_interval_ms: 5000
  health_check_interval_ms: 10000
  telegram_alerts: true
  telegram_chat_id: "${TELEGRAM_CHAT_ID}"
  telegram_bot_token: "${TELEGRAM_BOT_TOKEN}"
  streamlit:
    enabled: true
    port: 8501
    headless: false
    update_interval_ms: 1000

# Data Storage Settings
data_storage:
  base_dir: "data"
  historical_data_dir: "data/historical"
  raw_events_dir: "data/raw_events"
  output_dir: "output"
  cache_dir: "cache"
  max_cache_size_mb: 1024
  purge_older_than_days: 30

# Logging Settings
logging:
  level: "INFO"
  file: "logs/system.log"
  max_size_mb: 10
  backup_count: 5
  console: true
  file_logging: true

# Deployment Settings
deployment:
  docker:
    enabled: false
    image_name: "q5-trading-system"
    tag: "latest"
    restart_policy: "unless-stopped"
  
  streamlit:
    port: 8501
    headless: false
  
  health_server:
    enabled: true
    port: 8080
    endpoints:
      - "/health"
      - "/metrics"
      - "/readiness"
  
  environment: "development"  # "development", "staging", "production"

# Phase-specific Settings
phases:
  phase_0_env_setup:
    enabled: true
    data_sources:
      - name: "helius"
        enabled: true
        endpoint: "${HELIUS_RPC_URL}"
      - name: "birdeye"
        enabled: true
        endpoint: "https://api.birdeye.so/v1"
  
  phase_1_strategy_runner:
    enabled: true
    strategies_dir: "phase_1_strategy_runner/strategies"
    output_dir: "phase_1_strategy_runner/outputs"
  
  phase_2_backtest_engine:
    enabled: true
    vectorbt_enabled: true
    data_dir: "phase_2_backtest_engine/data"
    output_dir: "phase_2_backtest_engine/output"
  
  phase_3_rl_agent_training:
    enabled: false
    model_dir: "phase_3_rl_agent_training/models"
    output_dir: "phase_3_rl_agent_training/output"
  
  phase_4_deployment:
    enabled: true
    carbon_core_enabled: true
    carbon_core_config_path: "carbon_core_config.yaml"
