#!/usr/bin/env python3
"""
Test script for Q5 Trading System monitoring.

This script tests the monitoring components of the Q5 Trading System,
including metrics, alerts, and health checks.
"""

import os
import sys
import time
import asyncio
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_monitoring")

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import monitoring modules
from shared.utils.monitoring import get_monitoring_service, setup_telegram_alerts
from phase_4_deployment.utils.health_server import start_health_server

async def test_metrics():
    """Test metrics collection."""
    logger.info("Testing metrics collection...")
    
    # Get monitoring service
    monitoring = get_monitoring_service()
    
    # Test API request metrics
    logger.info("Testing API request metrics...")
    monitoring.track_api_request('test_api', 'test_endpoint', 'success', 0.1)
    monitoring.track_api_request('test_api', 'test_endpoint', 'error', 0.2)
    
    # Test trading signal metrics
    logger.info("Testing trading signal metrics...")
    monitoring.track_trading_signal('test_source', 'BUY', 'SOL-USDC')
    monitoring.track_trading_signal('test_source', 'SELL', 'SOL-USDC')
    
    # Test transaction metrics
    logger.info("Testing transaction metrics...")
    monitoring.track_transaction('success', 'swap', 0.5)
    monitoring.track_transaction('error', 'swap', 1.0)
    monitoring.track_transaction('success', 'swap', 0.3, paper_trading=True)
    
    # Test wallet balance metrics
    logger.info("Testing wallet balance metrics...")
    monitoring.update_wallet_balance('test_wallet', 10.5)
    await asyncio.sleep(1)
    monitoring.update_wallet_balance('test_wallet', 10.0)  # Decrease to trigger change
    
    # Test circuit breaker metrics
    logger.info("Testing circuit breaker metrics...")
    monitoring.update_circuit_breaker_status('test_api', 'CLOSED')
    
    logger.info("Metrics collection tests completed")

async def test_alerts():
    """Test alert triggering."""
    logger.info("Testing alert triggering...")
    
    # Get monitoring service
    monitoring = get_monitoring_service()
    
    # Set up Telegram alerts if credentials are available
    telegram_bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
    telegram_chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    
    if telegram_bot_token and telegram_chat_id:
        logger.info("Telegram credentials found, setting up alerts...")
        alert_handler = setup_telegram_alerts(telegram_bot_token, telegram_chat_id)
        
        # Register alert handlers
        monitoring.register_alert_handler('component_unhealthy', alert_handler)
        monitoring.register_alert_handler('low_balance', alert_handler)
        monitoring.register_alert_handler('transaction_error', alert_handler)
        monitoring.register_alert_handler('circuit_breaker_open', alert_handler)
        monitoring.register_alert_handler('system_resources', alert_handler)
        
        # Test component unhealthy alert
        logger.info("Testing component unhealthy alert...")
        monitoring._trigger_alert('component_unhealthy', {
            'component': 'test_component',
            'timestamp': datetime.now().isoformat()
        })
        
        # Wait a bit between alerts
        await asyncio.sleep(2)
        
        # Test low balance alert
        logger.info("Testing low balance alert...")
        monitoring._trigger_alert('low_balance', {
            'wallet': 'test_wallet',
            'balance': 0.5,
            'timestamp': datetime.now().isoformat()
        })
        
        # Wait a bit between alerts
        await asyncio.sleep(2)
        
        # Test transaction error alert
        logger.info("Testing transaction error alert...")
        monitoring._trigger_alert('transaction_error', {
            'type': 'swap',
            'error': 'Test error message',
            'timestamp': datetime.now().isoformat()
        })
        
        # Wait a bit between alerts
        await asyncio.sleep(2)
        
        # Test circuit breaker alert
        logger.info("Testing circuit breaker alert...")
        monitoring._trigger_alert('circuit_breaker_open', {
            'api': 'test_api',
            'timestamp': datetime.now().isoformat()
        })
        
        # Wait a bit between alerts
        await asyncio.sleep(2)
        
        # Test system resources alert
        logger.info("Testing system resources alert...")
        monitoring._trigger_alert('system_resources', {
            'resource': 'memory',
            'usage': '95%',
            'threshold': '90%',
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info("Alert tests completed")
    else:
        logger.warning("Telegram credentials not found, skipping alert tests")
        logger.info("To test alerts, set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables")

async def test_health_checks():
    """Test health checks."""
    logger.info("Testing health checks...")
    
    # Get monitoring service
    monitoring = get_monitoring_service()
    
    # Register test components
    monitoring.register_component('test_healthy', lambda: True)
    monitoring.register_component('test_unhealthy', lambda: False)
    
    # Start health check server
    health_server = start_health_server(host='0.0.0.0', port=8080)
    
    # Start health checks
    monitoring.start_health_checks()
    
    # Wait for health checks to run
    logger.info("Waiting for health checks to run...")
    await asyncio.sleep(5)
    
    # Run health checks manually
    results = monitoring.run_health_checks()
    logger.info(f"Health check results: {results}")
    
    logger.info("Health check tests completed")

async def main():
    """Main function to run all tests."""
    print("\n======================================")
    print("TESTING Q5 SYSTEM MONITORING")
    print("======================================\n")
    
    try:
        # Test metrics
        await test_metrics()
        print()
        
        # Test alerts
        await test_alerts()
        print()
        
        # Test health checks
        await test_health_checks()
        print()
        
        print("All monitoring tests completed successfully!")
        print("\nTo view metrics in Prometheus and Grafana:")
        print("1. Start Prometheus and Grafana using Docker Compose:")
        print("   docker-compose -f monitoring/docker-compose.yml up -d")
        print("2. Access Prometheus at http://localhost:9090")
        print("3. Access Grafana at http://localhost:3000 (admin/admin)")
        print("4. Import the dashboard from monitoring/grafana_dashboard.json")
        
    except Exception as e:
        logger.error(f"Error in monitoring tests: {str(e)}")
    
if __name__ == "__main__":
    asyncio.run(main())
