#!/usr/bin/env python3
import os
import sys
import time
from dotenv import load_dotenv

def test_transaction_components():
    """Test transaction components."""
    print("Testing transaction components...")
    
    # Test solana_tx_utils
    try:
        import shared.solana_utils.tx_utils
        print("✓ solana_tx_utils imported successfully")
        
        # Check if using native or fallback implementation
        if hasattr(solana_tx_utils, 'fallback'):
            print("⚠ Using fallback implementation")
        else:
            print("✓ Using native implementation")
        
        # Test keypair generation
        from shared.solana_utils.tx_utils import Keypair
        keypair = Keypair()
        pubkey = keypair.pubkey()
        print(f"✓ Generated keypair with pubkey: {pubkey}")
        
        # Test encoding functions
        from shared.solana_utils.tx_utils import encode_base58, decode_base58
        test_data = b"Hello, Solana!"
        encoded = encode_base58(test_data)
        decoded = decode_base58(encoded)
        
        if test_data == decoded:
            print("✓ Encoding/decoding test passed")
        else:
            print("✗ Encoding/decoding test failed")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Error testing solana_tx_utils: {str(e)}")
        return False

def test_transaction_executor():
    """Test transaction executor."""
    print("\nTesting transaction executor...")
    
    try:
        # Check if transaction executor exists
        import importlib.util
        
        # Try to import the transaction executor
        spec = importlib.util.find_spec("rpc_execution.transaction_executor")
        if spec is None:
            print("✗ transaction_executor module not found")
            return False
        
        print("✓ transaction_executor module found")
        
        # Check if circuit breaker is implemented
        spec = importlib.util.find_spec("core.circuit_breaker")
        if spec is None:
            print("✗ circuit_breaker module not found")
            return False
        
        print("✓ circuit_breaker module found")
        
        return True
    except Exception as e:
        print(f"✗ Error testing transaction executor: {str(e)}")
        return False

def main():
    """Main function."""
    # Load environment variables
    load_dotenv()
    
    # Run tests
    tx_utils_result = test_transaction_components()
    tx_executor_result = test_transaction_executor()
    
    # Print summary
    print("\nTest Summary:")
    print(f"Transaction Components: {'✓ Passed' if tx_utils_result else '✗ Failed'}")
    print(f"Transaction Executor: {'✓ Passed' if tx_executor_result else '✗ Failed'}")
    
    # Return exit code
    return 0 if tx_utils_result and tx_executor_result else 1

if __name__ == "__main__":
    sys.exit(main())
