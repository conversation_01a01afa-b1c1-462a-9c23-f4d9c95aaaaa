#!/usr/bin/env python3
"""
Test Stream Data Ingestion Service

This script tests the Stream Data Ingestion Service with the mock Carbon Core.
"""

import os
import sys
import json
import time
import logging
import asyncio
import subprocess
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable

# Install required packages
try:
    import yaml
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyyaml"])
    import yaml

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import stream data ingestor
from phase_4_deployment.stream_data_ingestor.sources import HeliusDataSource, BirdeyeDataSource, JitoDataSource
from phase_4_deployment.stream_data_ingestor.processors import OrderBookProcessor, TransactionProcessor, AccountProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_helius_data_source():
    """Test the Helius data source."""
    logger.info("Testing Helius data source...")
    
    # Create Helius data source
    helius = HeliusDataSource(
        api_key=os.environ.get("HELIUS_API_KEY", "dda9f776-9a40-447d-9ca4-22a27c21169e"),
    )
    
    # Create account processor
    account_processor = AccountProcessor()
    
    # Create transaction processor
    transaction_processor = TransactionProcessor()
    
    # Define callback for account updates
    async def handle_account_update(account_data: Dict[str, Any]) -> None:
        # Process account data
        processed_data = account_processor.process(account_data)
        
        # Log processed data
        logger.info(f"Processed account update: {json.dumps(processed_data, indent=2)}")
    
    # Define callback for transaction updates
    async def handle_transaction_update(transaction_data: Dict[str, Any]) -> None:
        # Process transaction data
        processed_data = transaction_processor.process(transaction_data)
        
        # Log processed data
        logger.info(f"Processed transaction update: {json.dumps(processed_data, indent=2)}")
    
    try:
        # Subscribe to account updates
        await helius.subscribe_to_account_updates(handle_account_update)
        
        # Subscribe to transaction updates
        await helius.subscribe_to_transaction_updates(handle_transaction_update)
        
        # Run for 30 seconds
        logger.info("Running for 30 seconds...")
        await asyncio.sleep(30)
        
        # Get account metrics
        account_metrics = account_processor.get_metrics()
        logger.info(f"Account metrics: {json.dumps(account_metrics, indent=2)}")
        
        # Get transaction metrics
        transaction_metrics = transaction_processor.get_metrics()
        logger.info(f"Transaction metrics: {json.dumps(transaction_metrics, indent=2)}")
    finally:
        # Close the data source
        await helius.close()

async def test_birdeye_data_source():
    """Test the Birdeye data source."""
    logger.info("Testing Birdeye data source...")
    
    # Create Birdeye data source
    birdeye = BirdeyeDataSource(
        api_key=os.environ.get("BIRDEYE_API_KEY", "a2679724762a47b58dde41b20fb55ce9"),
    )
    
    # Create order book processor
    orderbook_processor = OrderBookProcessor()
    
    try:
        # Get order book for SOL-USDC
        order_book = await birdeye.get_order_book("SOL-USDC")
        
        # Process order book
        processed_data = orderbook_processor.process("SOL-USDC", order_book)
        
        # Log processed data
        logger.info(f"Processed order book: {json.dumps(processed_data, indent=2)}")
        
        # Get token price for SOL
        price_data = await birdeye.get_token_price("SOL")
        logger.info(f"SOL price data: {json.dumps(price_data, indent=2)}")
        
        # Get token metadata for SOL
        metadata = await birdeye.get_token_metadata("SOL")
        logger.info(f"SOL metadata: {json.dumps(metadata, indent=2)}")
        
        # Get order book metrics
        metrics = orderbook_processor.get_metrics("SOL-USDC")
        logger.info(f"Order book metrics: {json.dumps(metrics, indent=2)}")
    finally:
        # Close the data source
        await birdeye.close()

async def test_jito_data_source():
    """Test the Jito data source."""
    logger.info("Testing Jito data source...")
    
    # Create Jito data source
    jito = JitoDataSource()
    
    # Create transaction processor
    transaction_processor = TransactionProcessor()
    
    # Define callback for transaction updates
    async def handle_transaction_update(transaction_data: Dict[str, Any]) -> None:
        # Process transaction data
        processed_data = transaction_processor.process(transaction_data)
        
        # Log processed data
        logger.info(f"Processed transaction update: {json.dumps(processed_data, indent=2)}")
    
    try:
        # Subscribe to transaction updates
        await jito.subscribe_to_transaction_updates(handle_transaction_update)
        
        # Get tip accounts
        tip_accounts = await jito.get_tip_accounts()
        logger.info(f"Tip accounts: {json.dumps(tip_accounts, indent=2)}")
        
        # Get bundle fee
        bundle_fee = await jito.get_bundle_fee()
        logger.info(f"Bundle fee: {json.dumps(bundle_fee, indent=2)}")
        
        # Run for 30 seconds
        logger.info("Running for 30 seconds...")
        await asyncio.sleep(30)
        
        # Get transaction metrics
        transaction_metrics = transaction_processor.get_metrics()
        logger.info(f"Transaction metrics: {json.dumps(transaction_metrics, indent=2)}")
    finally:
        # Close the data source
        await jito.close()

async def main():
    """Main function."""
    # Test Helius data source
    await test_helius_data_source()
    
    # Test Birdeye data source
    await test_birdeye_data_source()
    
    # Test Jito data source
    await test_jito_data_source()

if __name__ == "__main__":
    asyncio.run(main())
