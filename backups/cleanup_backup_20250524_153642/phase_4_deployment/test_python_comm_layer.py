#!/usr/bin/env python3
"""
Test script for Python communication layer

This script tests the Python communication layer with the mock Carbon Core.
"""

import os
import sys
import json
import time
import asyncio
import logging
import subprocess
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Callable

# Install required packages
try:
    import zmq
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyzmq"])
    import zmq

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import communication layer client
from phase_4_deployment.python_comm_layer.client import RustCommClient, TransactionPrepClient, CommunicationError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_comm_client():
    """Test the communication layer client."""
    logger.info("Testing communication layer client...")

    # Create communication client
    client = RustCommClient(
        pub_endpoint="tcp://127.0.0.1:5555",
        sub_endpoint="tcp://127.0.0.1:5556",
        req_endpoint="tcp://127.0.0.1:5557",
    )

    try:
        # Connect to the mock Carbon Core
        await client.connect()
        logger.info("Connected to mock Carbon Core")

        # Subscribe to test_response topic
        await client.subscribe("test_response", handle_test_response)
        logger.info("Subscribed to test_response topic")

        # Subscribe to order_book topic
        await client.subscribe("order_book/SOL-USDC", handle_order_book)
        logger.info("Subscribed to order_book/SOL-USDC topic")

        # Subscribe to signals topic
        await client.subscribe("signals/SOL-USDC", handle_signals)
        logger.info("Subscribed to signals/SOL-USDC topic")

        # Subscribe to metrics topic
        await client.subscribe("metrics", handle_metrics)
        logger.info("Subscribed to metrics topic")

        # Publish a test message
        await client.publish("test", {
            "message": "Hello from Python!",
            "timestamp": datetime.utcnow().isoformat(),
        })
        logger.info("Published test message")

        # Send a test request
        response = await client.request("test", {
            "message": "Hello from Python!",
            "timestamp": datetime.utcnow().isoformat(),
        })
        logger.info(f"Received response: {response}")

        # Send a get_signals request
        response = await client.request("get_signals", {
            "market": "SOL-USDC",
        })
        logger.info(f"Received signals: {response}")

        # Send a get_order_book request
        response = await client.request("get_order_book", {
            "market": "SOL-USDC",
        })
        logger.info(f"Received order book: {response}")

        # Send a get_metrics request
        response = await client.request("get_metrics", {})
        logger.info(f"Received metrics: {response}")

        # Wait for a while to receive subscription messages
        logger.info("Waiting for subscription messages...")
        await asyncio.sleep(10)

        logger.info("Test completed successfully")
    except CommunicationError as e:
        logger.error(f"Communication error: {str(e)}")
    except Exception as e:
        logger.error(f"Error: {str(e)}")
    finally:
        # Disconnect from the mock Carbon Core
        await client.disconnect()
        logger.info("Disconnected from mock Carbon Core")

async def test_transaction_prep_client():
    """Test the transaction preparation client."""
    logger.info("Testing transaction preparation client...")

    # Create transaction preparation client
    client = TransactionPrepClient(
        pub_endpoint="tcp://127.0.0.1:5555",
        sub_endpoint="tcp://127.0.0.1:5556",
        req_endpoint="tcp://127.0.0.1:5557",
    )

    try:
        # Connect to the mock Carbon Core
        await client.connect()
        logger.info("Connected to mock Carbon Core")

        # Prepare a transaction
        transaction_data = await client.prepare_transaction(
            instructions=[
                {
                    "program_id": "11111111111111111111111111111111",
                    "accounts": [
                        {
                            "pubkey": "Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr",
                            "is_signer": True,
                            "is_writable": True,
                        },
                        {
                            "pubkey": "6VH3sMggfBTVUWqtLwRGbGdEacfCHKsUKxYGJKZLUtMV",
                            "is_signer": False,
                            "is_writable": True,
                        },
                    ],
                    "data": "3Bxs4h24hBtQy9rw",
                },
            ],
            signers=["Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr"],
            recent_blockhash="EETubP5AKHgjPAhzPAFcb8BAY1hMH639CWCFTqi3hq1k",
            fee_payer="Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr",
        )
        logger.info(f"Prepared transaction: {transaction_data}")

        # Sign the transaction
        signed_transaction = await client.sign_transaction(
            transaction_data=transaction_data,
            keypair_path="keys/keypair.json",
        )
        logger.info(f"Signed transaction: {signed_transaction}")

        # Serialize the transaction
        serialized_transaction = await client.serialize_transaction(
            transaction_data=signed_transaction,
            encoding="base64",
        )
        logger.info(f"Serialized transaction: {serialized_transaction}")

        logger.info("Test completed successfully")
    except CommunicationError as e:
        logger.error(f"Communication error: {str(e)}")
    except Exception as e:
        logger.error(f"Error: {str(e)}")
    finally:
        # Disconnect from the mock Carbon Core
        await client.disconnect()
        logger.info("Disconnected from mock Carbon Core")

def handle_test_response(message: Dict[str, Any]):
    """
    Handle test_response message.

    Args:
        message: Message data
    """
    logger.info(f"Received test_response: {message}")

def handle_order_book(message: Dict[str, Any]):
    """
    Handle order_book message.

    Args:
        message: Message data
    """
    data = message.get("data", {})
    market = data.get("market", "")
    bids = data.get("bids", [])
    asks = data.get("asks", [])

    if bids and asks:
        best_bid = bids[0]["price"]
        best_ask = asks[0]["price"]
        mid_price = (best_bid + best_ask) / 2
        spread = best_ask - best_bid
        spread_pct = spread / mid_price if mid_price > 0 else 0

        logger.info(f"Order book for {market}: mid_price={mid_price:.6f}, spread={spread:.6f}, spread_pct={spread_pct:.6f}")
    else:
        logger.info(f"Empty order book for {market}")

def handle_signals(message: Dict[str, Any]):
    """
    Handle signals message.

    Args:
        message: Message data
    """
    data = message.get("data", {})
    market = data.get("market", "")
    signals = data.get("signals", {})

    if signals:
        logger.info(f"Signals for {market}: {signals}")
    else:
        logger.info(f"No signals for {market}")

def handle_metrics(message: Dict[str, Any]):
    """
    Handle metrics message.

    Args:
        message: Message data
    """
    data = message.get("data", {})

    if data:
        logger.info(f"Metrics: cpu_usage={data.get('cpu_usage', 0):.1f}%, memory_usage={data.get('memory_usage', 0):.1f}MB, processing_latency={data.get('processing_latency', 0):.6f}s")
    else:
        logger.info("No metrics")

async def main():
    """Main function."""
    # Test the communication layer client
    await test_comm_client()

    # Test the transaction preparation client
    await test_transaction_prep_client()

if __name__ == "__main__":
    asyncio.run(main())
