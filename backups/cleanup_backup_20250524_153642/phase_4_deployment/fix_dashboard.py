#!/usr/bin/env python3
"""
Fix the dashboard app.py file.
This script fixes the _repr_html_() error in the dashboard.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("dashboard_fix")

def fix_app_py():
    """Fix the app.py file in the GUI dashboard."""
    app_py_path = "/app/gui_dashboard/app.py"
    
    try:
        # Read the file
        with open(app_py_path, 'r') as f:
            content = f.read()
        
        # Find the problematic line
        problematic_line = "st.sidebar.success(system_health) if system_health.startswith(\"🟢\") else st.sidebar.warning(system_health) if system_health.startswith(\"🟡\") else st.sidebar.error(system_health)"
        
        # Create the fixed line
        fixed_line = """    # Display system health with proper conditional handling
    if system_health.startswith("🟢"):
        st.sidebar.success(system_health)
    elif system_health.startswith("🟡"):
        st.sidebar.warning(system_health)
    else:
        st.sidebar.error(system_health)"""
        
        # Replace the problematic line with the fixed line
        if problematic_line in content:
            new_content = content.replace(problematic_line, fixed_line)
            
            # Write the fixed content back to the file
            with open(app_py_path, 'w') as f:
                f.write(new_content)
            
            logger.info(f"Successfully fixed app.py")
            return True
        else:
            logger.warning(f"Problematic line not found in app.py")
            return False
    
    except Exception as e:
        logger.error(f"Error fixing app.py: {str(e)}")
        return False

def restart_dashboard():
    """Restart the Streamlit dashboard."""
    try:
        # Kill existing Streamlit processes
        os.system("pkill -f 'streamlit run'")
        
        # Start the dashboard in the background
        os.system("nohup streamlit run /app/gui_dashboard/app.py > /app/output/streamlit.log 2>&1 &")
        
        logger.info("Dashboard restarted successfully")
        return True
    
    except Exception as e:
        logger.error(f"Error restarting dashboard: {str(e)}")
        return False

def main():
    """Main function to fix the dashboard."""
    logger.info("Starting dashboard fix")
    
    # Fix app.py
    fixed = fix_app_py()
    
    if fixed:
        # Restart the dashboard
        restarted = restart_dashboard()
        
        if restarted:
            logger.info("Dashboard fixed and restarted successfully")
            return 0
        else:
            logger.error("Failed to restart dashboard")
            return 1
    else:
        logger.error("Failed to fix app.py")
        return 1

if __name__ == "__main__":
    sys.exit(main())
