#!/usr/bin/env python3
"""
Verify Metrics Dashboard Script

This script verifies that metrics are being properly collected and displayed in the Streamlit dashboard.
It generates test metrics, saves them to the appropriate files, and then checks if they're displayed correctly.
"""

import os
import json
import time
import logging
import asyncio
import subprocess
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('verify_metrics')

# Constants
HELIUS_METRICS_PATH = "output/helius_metrics.json"
JITO_METRICS_PATH = "output/jito_metrics.json"
STREAMLIT_URL = "http://localhost:8501"
STREAMLIT_DASHBOARD_PATH = "monitoring/streamlit_dashboard.py"

def generate_test_metrics() -> None:
    """Generate test metrics for verification."""
    logger.info("Generating test metrics")
    
    # Create output directory if it doesn't exist
    os.makedirs("output", exist_ok=True)
    
    # Generate Helius metrics
    helius_metrics = {
        "metrics": {
            "total_transactions": 10,
            "successful_transactions": 8,
            "failed_transactions": 2,
            "paper_trading_transactions": 5,
            "circuit_breaks": 1,
            "avg_execution_time": 0.25,
            "last_execution_time": 0.18,
            "last_updated": time.time()
        },
        "tx_history": [
            {
                "timestamp": datetime.now().isoformat(),
                "signature": f"test_sig_{i}",
                "provider": "helius" if i % 2 == 0 else "paper_trading",
                "execution_time": 0.2 + (i / 100),
                "success": i < 8  # 8 successful, 2 failed
            }
            for i in range(10)
        ]
    }
    
    # Save Helius metrics
    with open(HELIUS_METRICS_PATH, 'w') as f:
        json.dump(helius_metrics, f, indent=2)
    
    # Generate Jito metrics
    jito_metrics = {
        "timestamp": time.time(),
        "client": {
            "total_requests": 15,
            "successful_requests": 12,
            "failed_requests": 3,
            "fallback_requests": 1,
            "avg_response_time": 0.15,
            "last_request_time": time.time() - 60
        },
        "executor": {
            "tx_history_count": 5,
            "dry_run": False
        }
    }
    
    # Save Jito metrics
    with open(JITO_METRICS_PATH, 'w') as f:
        json.dump(jito_metrics, f, indent=2)
    
    logger.info("Test metrics generated and saved")

def start_streamlit_dashboard() -> subprocess.Popen:
    """
    Start the Streamlit dashboard.
    
    Returns:
        Streamlit process
    """
    logger.info(f"Starting Streamlit dashboard: {STREAMLIT_DASHBOARD_PATH}")
    
    # Start the dashboard in a separate process
    process = subprocess.Popen(
        ["streamlit", "run", STREAMLIT_DASHBOARD_PATH, "--server.port", "8501", "--server.headless", "false"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    logger.info(f"Streamlit dashboard started with PID {process.pid}")
    return process

def check_streamlit_running() -> bool:
    """
    Check if Streamlit is running.
    
    Returns:
        True if running, False otherwise
    """
    try:
        response = requests.get(STREAMLIT_URL)
        return response.status_code == 200
    except Exception:
        return False

def verify_metrics_displayed() -> bool:
    """
    Verify that metrics are being displayed in the Streamlit dashboard.
    
    Returns:
        True if metrics are displayed, False otherwise
    """
    logger.info("Verifying metrics are displayed in Streamlit dashboard")
    
    # Check if Streamlit is running
    if not check_streamlit_running():
        logger.error("Streamlit dashboard is not running")
        return False
    
    # Check if metrics files exist
    if not os.path.exists(HELIUS_METRICS_PATH):
        logger.error(f"Helius metrics file not found: {HELIUS_METRICS_PATH}")
        return False
    
    if not os.path.exists(JITO_METRICS_PATH):
        logger.error(f"Jito metrics file not found: {JITO_METRICS_PATH}")
        return False
    
    # Load metrics from files
    with open(HELIUS_METRICS_PATH, 'r') as f:
        helius_metrics = json.load(f)
    
    with open(JITO_METRICS_PATH, 'r') as f:
        jito_metrics = json.load(f)
    
    # Print metrics for verification
    logger.info("Helius Metrics:")
    logger.info(f"  Total Transactions: {helius_metrics['metrics']['total_transactions']}")
    logger.info(f"  Successful Transactions: {helius_metrics['metrics']['successful_transactions']}")
    logger.info(f"  Failed Transactions: {helius_metrics['metrics']['failed_transactions']}")
    
    logger.info("Jito Metrics:")
    logger.info(f"  Total Requests: {jito_metrics['client']['total_requests']}")
    logger.info(f"  Successful Requests: {jito_metrics['client']['successful_requests']}")
    logger.info(f"  Failed Requests: {jito_metrics['client']['failed_requests']}")
    
    logger.info("Metrics verification complete")
    logger.info(f"Please check the Streamlit dashboard at {STREAMLIT_URL} to verify metrics are displayed correctly")
    
    return True

def main() -> int:
    """Main function."""
    try:
        # Generate test metrics
        generate_test_metrics()
        
        # Check if Streamlit is already running
        streamlit_process = None
        if not check_streamlit_running():
            # Start Streamlit dashboard
            streamlit_process = start_streamlit_dashboard()
            
            # Wait for Streamlit to start
            logger.info("Waiting for Streamlit to start...")
            for _ in range(10):
                if check_streamlit_running():
                    break
                time.sleep(1)
        
        # Verify metrics are displayed
        if verify_metrics_displayed():
            logger.info("Metrics verification successful")
            
            # Open the dashboard in the browser
            import webbrowser
            webbrowser.open(STREAMLIT_URL)
            
            # Wait for user to check the dashboard
            input("Press Enter to continue after checking the dashboard...")
            
            return 0
        else:
            logger.error("Metrics verification failed")
            return 1
    except Exception as e:
        logger.error(f"Error verifying metrics: {str(e)}")
        return 1
    finally:
        # Clean up
        if streamlit_process:
            logger.info("Stopping Streamlit dashboard")
            streamlit_process.terminate()
            try:
                streamlit_process.wait(timeout=5)
                logger.info("Streamlit dashboard stopped")
            except subprocess.TimeoutExpired:
                logger.warning("Streamlit dashboard did not terminate, killing it")
                streamlit_process.kill()
                streamlit_process.wait()
                logger.info("Streamlit dashboard killed")

if __name__ == "__main__":
    exit(main())
