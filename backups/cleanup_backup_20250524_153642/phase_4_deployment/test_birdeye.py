#!/usr/bin/env python3
import os
import asyncio
import httpx
from dotenv import load_dotenv

async def test_birdeye():
    # Load environment variables
    load_dotenv()
    
    # Get API key
    api_key = os.getenv("BIRDEYE_API_KEY")
    
    if not api_key:
        print("Error: BIRDEYE_API_KEY not found in environment variables")
        return False
    
    print(f"Testing Birdeye API")
    
    # Create HTTP client
    async with httpx.AsyncClient() as client:
        try:
            # Test getting SOL price
            url = "https://api.birdeye.so/v1/defi/price?address=So11111111111111111111111111111111111111112"
            headers = {"X-API-KEY": api_key}
            response = await client.get(url, headers=headers)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("Birdeye API test successful!")
                print(f"SOL Price: ${data.get('data', {}).get('value', 'N/A')}")
                return True
            else:
                print(f"Error: {response.text}")
                return False
        except Exception as e:
            print(f"Error testing Birdeye API: {str(e)}")
            return False

if __name__ == "__main__":
    result = asyncio.run(test_birdeye())
    exit(0 if result else 1)
