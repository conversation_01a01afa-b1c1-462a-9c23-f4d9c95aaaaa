#!/usr/bin/env python3
"""
Streamlit Dashboard for Synergy7 Trading System

This script creates a Streamlit dashboard to visualize metrics from the Synergy7 Trading System.
It replaces the Grafana dashboard with a more lightweight and Python-native solution.
"""

import os
import sys
import time
import json
import logging
import datetime
import pandas as pd
import numpy as np
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import requests
from dotenv import load_dotenv

# Add project root directory to path to import shared modules
# This ensures we can import the shared module from anywhere
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
sys.path.insert(0, project_root)
print(f"Added project root to Python path: {project_root}")

try:
    from shared.utils.monitoring import get_monitoring_service
    print("Successfully imported monitoring service")
except ImportError as e:
    print(f"Error importing monitoring service: {e}")
    print(f"Python path: {sys.path}")
    # Fallback implementation if the import fails
    def get_monitoring_service():
        """Fallback implementation of get_monitoring_service"""
        class DummyMonitoringService:
            def get_metrics(self):
                return {}
        return DummyMonitoringService()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("streamlit_dashboard")

# Load environment variables
load_dotenv()

# Constants
REFRESH_INTERVAL = int(os.environ.get("REFRESH_INTERVAL", "5"))
METRICS_FILE = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
    'output',
    'metrics.json'
)

# Set page config
st.set_page_config(
    page_title="Q5 Trading System Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Sidebar
st.sidebar.title("Q5 Trading System")
st.sidebar.image("https://via.placeholder.com/150x150.png?text=Q5", width=150)

# Refresh interval
refresh_interval = st.sidebar.slider(
    "Refresh Interval (seconds)",
    min_value=1,
    max_value=60,
    value=REFRESH_INTERVAL,
)

# Time range
time_range = st.sidebar.selectbox(
    "Time Range",
    options=["Last 15 minutes", "Last hour", "Last 3 hours", "Last 12 hours", "Last 24 hours"],
    index=0,
)

# Function to get metrics from file
def get_metrics():
    """Get metrics from file."""
    try:
        if os.path.exists(METRICS_FILE):
            with open(METRICS_FILE, 'r') as f:
                return json.load(f)
        else:
            # Try to get metrics from monitoring service
            monitoring_service = get_monitoring_service()
            return monitoring_service.get_metrics()
    except Exception as e:
        logger.error(f"Error getting metrics: {str(e)}")
        return {}

# Function to create a dataframe from metrics
def create_dataframe(metrics, metric_name):
    """Create a dataframe from metrics."""
    if metric_name not in metrics:
        return pd.DataFrame()

    data = []
    for key, value in metrics[metric_name].items():
        try:
            # Parse the key as JSON to get labels
            labels = json.loads(key)

            # For histogram metrics
            if isinstance(value, dict) and 'count' in value and 'sum' in value:
                row = labels.copy()
                row['count'] = value['count']
                row['sum'] = value['sum']
                row['avg'] = value['sum'] / value['count'] if value['count'] > 0 else 0
                data.append(row)
            else:
                # For counter and gauge metrics
                row = labels.copy()
                row['value'] = value
                data.append(row)
        except Exception as e:
            logger.debug(f"Error parsing metric key {key}: {str(e)}")
            # For metrics without labels
            if not isinstance(value, dict):
                data.append({'value': value})

    return pd.DataFrame(data)

# Main dashboard
st.title("Q5 Trading System Dashboard")
st.markdown("Real-time monitoring of the Q5 Trading System")

# Create tabs
tab1, tab2, tab3, tab4, tab5 = st.tabs(["Overview", "API Performance", "Trading Activity", "System Resources", "Stream Data"])

# Auto-refresh
auto_refresh = st.sidebar.checkbox("Auto-refresh", value=True)

# Function to create the dashboard
def create_dashboard():
    # Get metrics
    metrics = get_metrics()

    # Overview tab
    with tab1:
        st.header("System Overview")

        # System health
        health_df = create_dataframe(metrics, "system_health")
        if not health_df.empty:
            col1, col2, col3 = st.columns(3)

            # Overall health
            overall_health_rows = health_df[health_df["component"] == "overall"]
            if not overall_health_rows.empty:
                overall_health = overall_health_rows["value"].values[0]
                col1.metric(
                    "System Health",
                    "Healthy" if overall_health == 1 else "Unhealthy",
                    delta=None,
                    delta_color="normal",
                )

            # API health
            api_health_rows = health_df[health_df["component"] == "api"]
            if not api_health_rows.empty:
                api_health = api_health_rows["value"].values[0]
                col2.metric(
                    "API Health",
                    "Healthy" if api_health == 1 else "Unhealthy",
                    delta=None,
                    delta_color="normal",
                )

            # Database health
            db_health_rows = health_df[health_df["component"] == "database"]
            if not db_health_rows.empty:
                db_health = db_health_rows["value"].values[0]
                col3.metric(
                    "Database Health",
                    "Healthy" if db_health == 1 else "Unhealthy",
                    delta=None,
                    delta_color="normal",
                )

        # Wallet balances
        st.subheader("Wallet Balances")
        wallet_df = create_dataframe(metrics, "wallet_balance")
        if not wallet_df.empty:
            col1, col2 = st.columns(2)

            # SOL balance (assuming all balances are in SOL)
            sol_balance = wallet_df["value"].sum()
            col1.metric(
                "Total SOL Balance",
                f"{sol_balance:.4f} SOL",
                delta=None,
                delta_color="normal",
            )

            # Wallet balance chart
            if not wallet_df.empty:
                fig = px.bar(
                    wallet_df,
                    x="wallet",
                    y="value",
                    title="Wallet Balances",
                    labels={"wallet": "Wallet", "value": "Balance (SOL)"},
                )
                st.plotly_chart(fig, use_container_width=True)

    # API Performance tab
    with tab2:
        st.header("API Performance")

        # API requests
        api_df = create_dataframe(metrics, "api_requests")
        if not api_df.empty:
            # Group by API and status
            api_summary = api_df.groupby(["api", "status"])["value"].sum().reset_index()

            # Create a bar chart
            fig = px.bar(
                api_summary,
                x="api",
                y="value",
                color="status",
                title="API Requests by Status",
                labels={"api": "API", "value": "Count", "status": "Status"},
            )
            st.plotly_chart(fig, use_container_width=True)

            # API latency
            api_latency_df = create_dataframe(metrics, "api_response_time")
            if not api_latency_df.empty and 'avg' in api_latency_df.columns:
                # Create a bar chart
                fig = px.bar(
                    api_latency_df,
                    x="api",
                    y="avg",
                    title="Average API Latency",
                    labels={"api": "API", "avg": "Average Latency (s)"},
                )
                st.plotly_chart(fig, use_container_width=True)

    # Trading Activity tab
    with tab3:
        st.header("Trading Activity")

        # Trading signals
        signals_df = create_dataframe(metrics, "trading_signals")
        if not signals_df.empty:
            # Group by source and action
            signals_summary = signals_df.groupby(["source", "action"])["value"].sum().reset_index()

            # Create a bar chart
            fig = px.bar(
                signals_summary,
                x="source",
                y="value",
                color="action",
                title="Trading Signals by Source and Action",
                labels={"source": "Source", "value": "Count", "action": "Action"},
            )
            st.plotly_chart(fig, use_container_width=True)

        # Transactions
        tx_df = create_dataframe(metrics, "transactions")
        if not tx_df.empty:
            # Group by type and status
            tx_summary = tx_df.groupby(["type", "status"])["value"].sum().reset_index()

            # Create a bar chart
            fig = px.bar(
                tx_summary,
                x="type",
                y="value",
                color="status",
                title="Transactions by Type and Status",
                labels={"type": "Type", "value": "Count", "status": "Status"},
            )
            st.plotly_chart(fig, use_container_width=True)

    # System Resources tab
    with tab4:
        st.header("System Resources")

        # CPU usage
        if 'system_cpu_usage' in metrics:
            cpu_usage = metrics['system_cpu_usage']
            st.metric(
                "CPU Usage",
                f"{cpu_usage:.2f}%",
                delta=None,
                delta_color="normal",
            )

            # Create a gauge chart
            fig = go.Figure(go.Indicator(
                mode="gauge+number",
                value=cpu_usage,
                domain={"x": [0, 1], "y": [0, 1]},
                title={"text": "CPU Usage"},
                gauge={
                    "axis": {"range": [0, 100]},
                    "bar": {"color": "darkblue"},
                    "steps": [
                        {"range": [0, 50], "color": "lightgreen"},
                        {"range": [50, 80], "color": "yellow"},
                        {"range": [80, 100], "color": "red"},
                    ],
                    "threshold": {
                        "line": {"color": "red", "width": 4},
                        "thickness": 0.75,
                        "value": 90,
                    },
                },
            ))
            st.plotly_chart(fig, use_container_width=True)

        # Memory usage
        if 'system_memory_usage' in metrics:
            memory_usage = metrics['system_memory_usage'] / (1024 * 1024 * 1024)  # Convert to GB
            st.metric(
                "Memory Usage",
                f"{memory_usage:.2f} GB",
                delta=None,
                delta_color="normal",
            )

            # Create a gauge chart
            fig = go.Figure(go.Indicator(
                mode="gauge+number",
                value=memory_usage,
                domain={"x": [0, 1], "y": [0, 1]},
                title={"text": "Memory Usage (GB)"},
                gauge={
                    "axis": {"range": [0, 16]},  # Assuming 16GB total memory
                    "bar": {"color": "darkblue"},
                    "steps": [
                        {"range": [0, 8], "color": "lightgreen"},
                        {"range": [8, 12], "color": "yellow"},
                        {"range": [12, 16], "color": "red"},
                    ],
                    "threshold": {
                        "line": {"color": "red", "width": 4},
                        "thickness": 0.75,
                        "value": 14,
                    },
                },
            ))
            st.plotly_chart(fig, use_container_width=True)

        # Disk usage
        disk_df = create_dataframe(metrics, "system_disk_usage")
        if not disk_df.empty:
            disk_usage = disk_df["value"].values[0] / (1024 * 1024 * 1024)  # Convert to GB
            st.metric(
                "Disk Usage",
                f"{disk_usage:.2f} GB",
                delta=None,
                delta_color="normal",
            )

            # Create a gauge chart
            fig = go.Figure(go.Indicator(
                mode="gauge+number",
                value=disk_usage,
                domain={"x": [0, 1], "y": [0, 1]},
                title={"text": "Disk Usage (GB)"},
                gauge={
                    "axis": {"range": [0, 100]},  # Assuming 100GB total disk
                    "bar": {"color": "darkblue"},
                    "steps": [
                        {"range": [0, 50], "color": "lightgreen"},
                        {"range": [50, 80], "color": "yellow"},
                        {"range": [80, 100], "color": "red"},
                    ],
                    "threshold": {
                        "line": {"color": "red", "width": 4},
                        "thickness": 0.75,
                        "value": 90,
                    },
                },
            ))
            st.plotly_chart(fig, use_container_width=True)

    # Stream Data tab
    with tab5:
        st.header("Stream Data")

        # Stream data metrics
        stream_data_df = create_dataframe(metrics, "stream_data_messages")
        if not stream_data_df.empty:
            # Group by source and status
            stream_data_summary = stream_data_df.groupby(["source", "status"])["value"].sum().reset_index()

            # Create a bar chart
            fig = px.bar(
                stream_data_summary,
                x="source",
                y="value",
                color="status",
                title="Stream Data Messages by Source and Status",
                labels={"source": "Source", "value": "Count", "status": "Status"},
            )
            st.plotly_chart(fig, use_container_width=True)

        # Stream data latency
        stream_latency_df = create_dataframe(metrics, "stream_data_latency")
        if not stream_latency_df.empty and 'avg' in stream_latency_df.columns:
            # Create a bar chart
            fig = px.bar(
                stream_latency_df,
                x="source",
                y="avg",
                title="Average Stream Data Latency",
                labels={"source": "Source", "avg": "Average Latency (s)"},
            )
            st.plotly_chart(fig, use_container_width=True)

        # Lil' Jito metrics
        liljito_df = create_dataframe(metrics, "liljito_requests")
        if not liljito_df.empty:
            # Group by method and status
            liljito_summary = liljito_df.groupby(["method", "status"])["value"].sum().reset_index()

            # Create a bar chart
            fig = px.bar(
                liljito_summary,
                x="method",
                y="value",
                color="status",
                title="Lil' Jito Requests by Method and Status",
                labels={"method": "Method", "value": "Count", "status": "Status"},
            )
            st.plotly_chart(fig, use_container_width=True)

        # Lil' Jito bundle metrics
        liljito_bundle_df = create_dataframe(metrics, "liljito_bundles")
        if not liljito_bundle_df.empty:
            # Group by status
            liljito_bundle_summary = liljito_bundle_df.groupby(["status"])["value"].sum().reset_index()

            # Create a pie chart
            fig = px.pie(
                liljito_bundle_summary,
                values="value",
                names="status",
                title="Lil' Jito Bundles by Status",
            )
            st.plotly_chart(fig, use_container_width=True)

# Create the dashboard
create_dashboard()

# Auto-refresh
if auto_refresh:
    st.empty()
    time.sleep(refresh_interval)
    st.experimental_rerun()

if __name__ == "__main__":
    # This will only run when the script is executed directly
    pass
