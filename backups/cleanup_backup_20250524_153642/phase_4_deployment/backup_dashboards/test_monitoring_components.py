#!/usr/bin/env python3
import os
import sys
import time
from dotenv import load_dotenv

def test_monitoring_components():
    """Test monitoring components."""
    print("Testing monitoring components...")
    
    try:
        # Import monitoring service
        from shared.utils.monitoring import get_monitoring_service
        
        # Get monitoring service
        monitoring = get_monitoring_service()
        print("✓ Monitoring service imported successfully")
        
        # Register a test component
        monitoring.register_component("test_component", lambda: True)
        print("✓ Registered test component")
        
        # Run health checks
        health_results = monitoring.run_health_checks()
        print(f"✓ Health check results: {health_results}")
        
        # Get metrics
        metrics = monitoring.get_metrics()
        print(f"✓ Metrics retrieved: {len(metrics)} metrics")
        
        # Test Telegram alerter if configured
        telegram_bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        telegram_chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if telegram_bot_token and telegram_chat_id:
            print("✓ Telegram alerter is configured")
        else:
            print("⚠ Telegram alerter is not configured")
        
        return True
    except Exception as e:
        print(f"✗ Error testing monitoring components: {str(e)}")
        return False

def test_dashboard():
    """Test dashboard components."""
    print("\nTesting dashboard components...")
    
    try:
        # Check if dashboard exists
        dashboard_path = os.path.join("gui_dashboard", "app.py")
        if os.path.exists(dashboard_path):
            print(f"✓ Dashboard found at {dashboard_path}")
        else:
            print(f"✗ Dashboard not found at {dashboard_path}")
            return False
        
        # Check if Streamlit is installed
        try:
            import streamlit
            print("✓ Streamlit is installed")
        except ImportError:
            print("✗ Streamlit is not installed")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Error testing dashboard components: {str(e)}")
        return False

def main():
    """Main function."""
    # Load environment variables
    load_dotenv()
    
    # Run tests
    monitoring_result = test_monitoring_components()
    dashboard_result = test_dashboard()
    
    # Print summary
    print("\nTest Summary:")
    print(f"Monitoring Components: {'✓ Passed' if monitoring_result else '✗ Failed'}")
    print(f"Dashboard Components: {'✓ Passed' if dashboard_result else '✗ Failed'}")
    
    # Return exit code
    return 0 if monitoring_result and dashboard_result else 1

if __name__ == "__main__":
    sys.exit(main())
