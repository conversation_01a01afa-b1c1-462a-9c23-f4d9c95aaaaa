#!/usr/bin/env python3
"""
Run Streamlit Dashboard

This script runs the Streamlit dashboard for the trading system.
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function."""
    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description="Run Streamlit dashboard")
    parser.add_argument("--port", type=int, default=8501, help="Port to run Streamlit on")
    parser.add_argument("--host", default="0.0.0.0", help="Host to run Streamlit on")

    args = parser.parse_args()

    # Get the path to the project root
    project_root = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))

    # Add the project root to the Python path
    sys.path.insert(0, project_root)

    # Set the PYTHONPATH environment variable
    os.environ["PYTHONPATH"] = project_root + os.pathsep + os.environ.get("PYTHONPATH", "")

    # Get the path to the Streamlit dashboard script
    dashboard_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "monitoring",
        "streamlit_dashboard.py",
    )

    # Check if the dashboard script exists
    if not os.path.exists(dashboard_path):
        logger.error(f"Dashboard script not found: {dashboard_path}")
        return 1

    # Run Streamlit
    logger.info(f"Running Streamlit dashboard on {args.host}:{args.port}")
    logger.info(f"Project root: {project_root}")
    logger.info(f"Python path: {sys.path}")

    try:
        # Install Streamlit if not already installed
        try:
            import streamlit
            logger.info("Streamlit is already installed")
        except ImportError:
            logger.info("Installing Streamlit...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])

        # Check if the shared module is accessible
        try:
            import shared
            logger.info("Shared module is accessible")
        except ImportError as e:
            logger.warning(f"Shared module is not accessible: {e}")
            logger.warning("This may cause issues with the dashboard")

        # Run Streamlit with the updated environment
        env = os.environ.copy()
        env["PYTHONPATH"] = project_root + os.pathsep + env.get("PYTHONPATH", "")

        logger.info(f"Running Streamlit with PYTHONPATH: {env['PYTHONPATH']}")

        subprocess.run([
            "streamlit",
            "run",
            dashboard_path,
            "--server.port",
            str(args.port),
            "--server.address",
            args.host,
        ], env=env)
    except Exception as e:
        logger.error(f"Error running Streamlit dashboard: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
