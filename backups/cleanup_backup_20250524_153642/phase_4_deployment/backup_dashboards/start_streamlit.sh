#!/bin/bash
# Script to start the Streamlit dashboard for the Q5 Trading System

# Determine the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Check if virtual environment exists
if [ -d "$PROJECT_ROOT/venv" ]; then
    VENV_PATH="$PROJECT_ROOT/venv"
    echo "Found virtual environment at $VENV_PATH"
else
    echo "Virtual environment not found at $PROJECT_ROOT/venv"
    echo "Creating new virtual environment..."
    python3 -m venv "$PROJECT_ROOT/venv"
    VENV_PATH="$PROJECT_ROOT/venv"
fi

# Activate virtual environment
source "$VENV_PATH/bin/activate"

# Install required packages if not already installed
if ! pip show streamlit > /dev/null 2>&1; then
    echo "Installing Streamlit requirements..."
    pip install -r "$SCRIPT_DIR/streamlit_requirements.txt"
fi

# Set environment variables
export PROMETHEUS_URL=${PROMETHEUS_URL:-"http://localhost:9090"}
export METRICS_PORT=${METRICS_PORT:-"9091"}
export REFRESH_INTERVAL=${REFRESH_INTERVAL:-"5"}

# Start Streamlit dashboard
echo "Starting Streamlit dashboard..."
echo "Dashboard will be available at http://localhost:8501"
echo "Press Ctrl+C to stop"

# Change to the monitoring directory
cd "$SCRIPT_DIR"

# Start Streamlit
streamlit run streamlit_dashboard.py
