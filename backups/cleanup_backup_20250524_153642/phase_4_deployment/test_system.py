#!/usr/bin/env python3
"""
Test System

This script tests the complete trading system with simulated data.
"""

import os
import sys
import json
import time
import logging
import asyncio
import subprocess
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable

# Install required packages
try:
    import yaml
    import httpx
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyyaml", "httpx"])
    import yaml
    import httpx

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemTester:
    """Tester for the trading system."""
    
    def __init__(self, config_path: str = "test_config.yaml"):
        """
        Initialize the system tester.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize state
        self.running = False
        self.process = None
        self.health_check_url = "http://localhost:8080"
        
        logger.info("Initialized system tester")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file.
        
        Returns:
            Dict[str, Any]: Configuration
        """
        try:
            with open(self.config_path, "r") as f:
                config = yaml.safe_load(f)
            
            logger.info(f"Loaded configuration from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return {}
    
    async def start_system(self):
        """Start the trading system."""
        if self.running:
            logger.warning("Trading system is already running")
            return
        
        logger.info("Starting trading system...")
        
        # Get the path to the run_complete_system.py script
        script_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "run_complete_system.py",
        )
        
        # Start the trading system
        self.process = subprocess.Popen(
            [sys.executable, script_path, "--config", self.config_path, "--no-dashboard"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            bufsize=1,
        )
        
        # Start threads to read stdout and stderr
        import threading
        threading.Thread(target=self._read_output, args=(self.process.stdout, "stdout"), daemon=True).start()
        threading.Thread(target=self._read_output, args=(self.process.stderr, "stderr"), daemon=True).start()
        
        # Set running flag
        self.running = True
        
        # Wait for the system to start
        logger.info("Waiting for the trading system to start...")
        await asyncio.sleep(10)
        
        logger.info("Trading system started")
    
    def _read_output(self, pipe, stream_name):
        """
        Read output from a pipe.
        
        Args:
            pipe: Pipe to read from
            stream_name: Stream name
        """
        for line in pipe:
            logger.info(f"Trading system ({stream_name}): {line.strip()}")
    
    async def stop_system(self):
        """Stop the trading system."""
        if not self.running:
            logger.warning("Trading system is not running")
            return
        
        logger.info("Stopping trading system...")
        
        # Stop the trading system
        if self.process:
            self.process.terminate()
            try:
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning("Trading system did not terminate gracefully")
                self.process.kill()
            
            self.process = None
        
        # Set running flag
        self.running = False
        
        logger.info("Trading system stopped")
    
    async def check_health(self) -> bool:
        """
        Check the health of the trading system.
        
        Returns:
            bool: True if the system is healthy, False otherwise
        """
        try:
            # Check health endpoint
            response = await self._http_get("/health")
            
            if response.status_code == 200:
                logger.info("Trading system is healthy")
                return True
            else:
                logger.warning(f"Trading system is not healthy: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error checking health: {str(e)}")
            return False
    
    async def check_component_status(self) -> Dict[str, Any]:
        """
        Check the status of the trading system components.
        
        Returns:
            Dict[str, Any]: Component status
        """
        try:
            # Check status endpoint
            response = await self._http_get("/status")
            
            if response.status_code == 200:
                status = response.json()
                logger.info(f"Component status: {json.dumps(status, indent=2)}")
                return status
            else:
                logger.warning(f"Error getting component status: {response.text}")
                return {}
        except Exception as e:
            logger.error(f"Error checking component status: {str(e)}")
            return {}
    
    async def check_metrics(self) -> Dict[str, Any]:
        """
        Check the metrics of the trading system.
        
        Returns:
            Dict[str, Any]: Metrics
        """
        try:
            # Check metrics endpoint
            response = await self._http_get("/metrics")
            
            if response.status_code == 200:
                metrics = response.json()
                logger.info(f"Metrics: {json.dumps(metrics, indent=2)}")
                return metrics
            else:
                logger.warning(f"Error getting metrics: {response.text}")
                return {}
        except Exception as e:
            logger.error(f"Error checking metrics: {str(e)}")
            return {}
    
    async def check_alerts(self) -> List[Dict[str, Any]]:
        """
        Check the alerts of the trading system.
        
        Returns:
            List[Dict[str, Any]]: Alerts
        """
        try:
            # Check alerts endpoint
            response = await self._http_get("/alerts")
            
            if response.status_code == 200:
                alerts = response.json()
                logger.info(f"Alerts: {json.dumps(alerts, indent=2)}")
                return alerts
            else:
                logger.warning(f"Error getting alerts: {response.text}")
                return []
        except Exception as e:
            logger.error(f"Error checking alerts: {str(e)}")
            return []
    
    async def _http_get(self, path: str):
        """
        Send an HTTP GET request.
        
        Args:
            path: Request path
            
        Returns:
            httpx.Response: Response
        """
        async with httpx.AsyncClient() as client:
            return await client.get(f"{self.health_check_url}{path}", timeout=10.0)
    
    async def run_test(self):
        """Run the test."""
        try:
            # Start the trading system
            await self.start_system()
            
            # Get test duration
            test_duration = self.config.get("test", {}).get("duration_seconds", 300)
            
            # Calculate end time
            end_time = datetime.now() + timedelta(seconds=test_duration)
            
            # Run until test duration expires
            logger.info(f"Running test for {test_duration} seconds...")
            
            while datetime.now() < end_time:
                # Check health
                health = await self.check_health()
                
                if not health:
                    logger.error("Trading system is not healthy")
                    break
                
                # Check component status
                status = await self.check_component_status()
                
                # Check if any component is in error state
                if any(component["status"] == "error" for component in status.values()):
                    logger.error("One or more components are in error state")
                    break
                
                # Check metrics
                metrics = await self.check_metrics()
                
                # Check alerts
                alerts = await self.check_alerts()
                
                # Wait for a while
                await asyncio.sleep(10)
            
            # Check final metrics
            logger.info("Test completed. Checking final metrics...")
            metrics = await self.check_metrics()
            
            # Check if any trades were executed
            tx_metrics = metrics.get("transaction_executor", {})
            executed_transactions = tx_metrics.get("executed_transactions", {})
            
            if executed_transactions:
                logger.info(f"Executed transactions: {json.dumps(executed_transactions, indent=2)}")
            else:
                logger.warning("No transactions were executed during the test")
            
            # Check final alerts
            alerts = await self.check_alerts()
            
            if alerts:
                logger.info(f"Alerts: {json.dumps(alerts, indent=2)}")
            else:
                logger.info("No alerts were generated during the test")
            
            logger.info("Test completed successfully")
            return True
        except Exception as e:
            logger.error(f"Error running test: {str(e)}")
            return False
        finally:
            # Stop the trading system
            await self.stop_system()

async def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Test the trading system")
    parser.add_argument("--config", default="test_config.yaml", help="Path to configuration file")
    
    args = parser.parse_args()
    
    # Create system tester
    tester = SystemTester(config_path=args.config)
    
    # Run test
    success = await tester.run_test()
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
