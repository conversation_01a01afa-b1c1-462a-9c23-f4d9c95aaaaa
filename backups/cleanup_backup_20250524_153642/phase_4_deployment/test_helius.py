#!/usr/bin/env python3
import os
import asyncio
import httpx
from dotenv import load_dotenv

async def test_helius():
    # Load environment variables
    load_dotenv()
    
    # Get API key and wallet address
    api_key = os.getenv("HELIUS_API_KEY")
    wallet_address = os.getenv("WALLET_ADDRESS")
    
    if not api_key:
        print("Error: HELIUS_API_KEY not found in environment variables")
        return False
    
    if not wallet_address:
        print("Error: WALLET_ADDRESS not found in environment variables")
        return False
    
    print(f"Testing Helius API with wallet: {wallet_address}")
    
    # Create HTTP client
    async with httpx.AsyncClient() as client:
        try:
            # Test getting wallet balances
            url = f"https://api.helius.xyz/v0/addresses/{wallet_address}/balances?api-key={api_key}"
            response = await client.get(url)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("Helius API test successful!")
                print(f"Native Balance: {data.get('nativeBalance', 'N/A')} lamports")
                print(f"Tokens: {len(data.get('tokens', []))} tokens found")
                return True
            else:
                print(f"Error: {response.text}")
                return False
        except Exception as e:
            print(f"Error testing Helius API: {str(e)}")
            return False

if __name__ == "__main__":
    result = asyncio.run(test_helius())
    exit(0 if result else 1)
