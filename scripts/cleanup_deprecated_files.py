#!/usr/bin/env python3
"""
Cleanup Deprecated Files Script

This script safely removes deprecated files listed in depr.txt to avoid confusion
with our new production-ready Jupiter integration system.

IMPORTANT: This script creates a backup before removing files.
"""

import os
import sys
import shutil
import logging
from datetime import datetime
from pathlib import Path
from typing import List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def read_deprecated_files() -> List[str]:
    """Read the list of deprecated files from depr.txt."""
    depr_file = Path("depr.txt")
    if not depr_file.exists():
        logger.error("depr.txt file not found!")
        return []

    deprecated_files = []
    with open(depr_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            # Skip comments and empty lines
            if line and not line.startswith('#'):
                # Remove inline comments (everything after #)
                if '#' in line:
                    line = line.split('#')[0].strip()
                if line:  # Only add if there's still content after removing comments
                    deprecated_files.append(line)

    return deprecated_files

def create_backup(files_to_remove: List[str]) -> str:
    """Create a backup of files before removal."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backups/cleanup_backup_{timestamp}"

    logger.info(f"Creating backup directory: {backup_dir}")
    os.makedirs(backup_dir, exist_ok=True)

    backed_up_files = 0
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                # Create backup directory structure
                backup_path = os.path.join(backup_dir, file_path)
                backup_parent = os.path.dirname(backup_path)
                os.makedirs(backup_parent, exist_ok=True)

                if os.path.isdir(file_path):
                    shutil.copytree(file_path, backup_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(file_path, backup_path)

                backed_up_files += 1
                logger.debug(f"Backed up: {file_path}")

            except Exception as e:
                logger.warning(f"Failed to backup {file_path}: {e}")

    logger.info(f"Backed up {backed_up_files} files/directories to {backup_dir}")
    return backup_dir

def get_existing_files(deprecated_files: List[str]) -> List[str]:
    """Filter the list to only include files that actually exist."""
    existing_files = []

    for file_path in deprecated_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
        else:
            logger.debug(f"File not found (already removed?): {file_path}")

    return existing_files

def categorize_files(files: List[str]) -> dict:
    """Categorize files by priority for removal."""
    categories = {
        'high_priority': [],  # Critical conflicts with working system
        'medium_priority': [],  # Old implementations and configs
        'low_priority': []  # Documentation and backup directories
    }

    # Critical conflicts that could break our working system
    high_priority_keywords = [
        'transaction', 'jupiter', 'tx_builder', 'tx_executor',
        'enhanced_tx', 'solana_tx_utils', 'wallet', 'logging_config.json',
        '.env.example', '.env.paper', '.env.production', '.env.simulation',
        'pytest.ini', 'requirements-test.txt', 'requirements.txt.backup'
    ]

    # Old implementations and conflicting configs
    medium_priority_keywords = [
        'enhanced_live_trading', 'run_synergy7', 'run_q5_system',
        'config_example', 'test_', 'simple_paper_trading_monitor.py',
        'setup_fallback.py', 'simulation_results.json', 'validation_report.json',
        'config/whale_config.yaml', 'config/schemas/', 'phase_4_deployment/configs/'
    ]

    # Documentation and directories (lowest priority)
    low_priority_keywords = [
        '.md', 'backups/', 'configs/', 'utils/', 'tests/unit/', 'tests/core/',
        'tests/integration/', 'tests/performance/', 'tests/e2e/', 'tests/functional/',
        'tests/shared/', 'tests/utils/', 'BUSINESS_RISK_ASSESSMENT.md',
        'COMPLETE_INTEGRATION_SUMMARY.md', 'DEPLOYMENT_COMPLETE.md'
    ]

    for file_path in files:
        file_lower = file_path.lower()

        if any(keyword in file_lower for keyword in high_priority_keywords):
            categories['high_priority'].append(file_path)
        elif any(keyword in file_lower for keyword in medium_priority_keywords):
            categories['medium_priority'].append(file_path)
        elif any(keyword in file_lower for keyword in low_priority_keywords):
            categories['low_priority'].append(file_path)
        else:
            # Default to low priority for unmatched files
            categories['low_priority'].append(file_path)

    return categories

def remove_files(files: List[str], dry_run: bool = False) -> int:
    """Remove the specified files and directories."""
    removed_count = 0

    for file_path in files:
        try:
            if dry_run:
                logger.info(f"[DRY RUN] Would remove: {file_path}")
                removed_count += 1
            else:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    logger.info(f"Removed directory: {file_path}")
                else:
                    os.remove(file_path)
                    logger.info(f"Removed file: {file_path}")
                removed_count += 1

        except Exception as e:
            logger.error(f"Failed to remove {file_path}: {e}")

    return removed_count

def main():
    """Main cleanup function."""
    logger.info("🧹 STARTING DEPRECATED FILES CLEANUP")
    logger.info("=" * 60)

    # Check if we're in the right directory
    if not os.path.exists("depr.txt"):
        logger.error("Please run this script from the project root directory")
        sys.exit(1)

    # Read deprecated files list
    logger.info("📋 Reading deprecated files list...")
    deprecated_files = read_deprecated_files()
    logger.info(f"Found {len(deprecated_files)} entries in depr.txt")

    # Filter to existing files
    logger.info("🔍 Checking which files actually exist...")
    existing_files = get_existing_files(deprecated_files)
    logger.info(f"Found {len(existing_files)} files/directories that exist")

    if not existing_files:
        logger.info("✅ No deprecated files found to remove!")
        return

    # Categorize files by priority
    categories = categorize_files(existing_files)

    logger.info("\n📊 CLEANUP SUMMARY:")
    logger.info(f"  High Priority (Jupiter/Transaction conflicts): {len(categories['high_priority'])}")
    logger.info(f"  Medium Priority (Old implementations): {len(categories['medium_priority'])}")
    logger.info(f"  Low Priority (Backup/output directories): {len(categories['low_priority'])}")

    # Show high priority files
    if categories['high_priority']:
        logger.info("\n🚨 HIGH PRIORITY FILES (Jupiter/Transaction conflicts):")
        for file_path in categories['high_priority']:
            logger.info(f"  - {file_path}")

    # Ask for confirmation
    print("\n" + "=" * 60)
    print("⚠️  WARNING: This will permanently remove the above files!")
    print("⚠️  A backup will be created before removal.")
    print("=" * 60)

    response = input("\nProceed with cleanup? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        logger.info("❌ Cleanup cancelled by user")
        return

    # Create backup
    logger.info("\n💾 Creating backup...")
    backup_dir = create_backup(existing_files)

    # Remove files by priority
    total_removed = 0

    for priority, files in categories.items():
        if files:
            logger.info(f"\n🗑️  Removing {priority.replace('_', ' ')} files...")
            removed = remove_files(files)
            total_removed += removed
            logger.info(f"Removed {removed} {priority.replace('_', ' ')} files")

    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("✅ CLEANUP COMPLETED!")
    logger.info(f"📊 Total files/directories removed: {total_removed}")
    logger.info(f"💾 Backup created at: {backup_dir}")
    logger.info("=" * 60)

    logger.info("\n🚀 NEXT STEPS:")
    logger.info("1. Test the system: python scripts/test_fixed_live_trading.py")
    logger.info("2. Test live trading: python scripts/unified_live_trading.py --duration 0.05 --test-mode")
    logger.info("3. Run final system test: python scripts/final_system_test.py")
    logger.info("4. If everything works, you can remove the backup directory")

if __name__ == "__main__":
    main()
