#!/usr/bin/env python3
"""
Update Dashboard with Real Wallet Balance

This script fetches the real wallet balance and updates the dashboard
to show accurate SOL/USDC balances instead of placeholder data.
"""

import asyncio
import json
import os
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def get_real_wallet_balance():
    """Get real wallet balance from Helius."""
    try:
        # Load environment variables
        wallet_address = os.getenv('WALLET_ADDRESS', 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz')
        helius_api_key = os.getenv('HELIUS_API_KEY', 'dda9f776-9a40-447d-9ca4-22a27c21169e')
        
        # Import Helius client
        import sys
        sys.path.append('.')
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient
        
        logger.info(f"🔍 Fetching real balance for wallet: {wallet_address}")
        
        # Get balance
        client = HeliusClient(api_key=helius_api_key)
        balance_data = await client.get_balance(wallet_address)
        
        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            sol_balance = balance_data['balance_sol']
            sol_price = 180.0  # Approximate SOL price
            usd_value = sol_balance * sol_price
            
            logger.info(f"✅ Real SOL Balance: {sol_balance:.6f} SOL")
            logger.info(f"✅ Real USD Value: ${usd_value:.2f}")
            
            return {
                'sol_balance': sol_balance,
                'usdc_balance': 0.0,  # We're not holding USDC in this wallet
                'sol_price': sol_price,
                'usd_value': usd_value,
                'wallet_address': wallet_address,
                'timestamp': datetime.now().isoformat()
            }
        else:
            logger.error(f"❌ Failed to get balance: {balance_data}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Error getting real balance: {e}")
        return None

async def update_dashboard_balance_data():
    """Update dashboard data with real wallet balance."""
    try:
        # Get real balance
        balance_data = await get_real_wallet_balance()
        if not balance_data:
            logger.error("❌ Could not get real balance data")
            return False
        
        # Create real wallet metrics for dashboard
        real_wallet_metrics = {
            'timestamp': balance_data['timestamp'],
            'wallet_balance': {
                'trading_wallet': balance_data['sol_balance'],
                'total_sol': balance_data['sol_balance'],
                'total_usdc': balance_data['usdc_balance'],
                'total_usd_value': balance_data['usd_value']
            },
            'wallet_info': {
                'address': balance_data['wallet_address'],
                'sol_price': balance_data['sol_price'],
                'last_updated': balance_data['timestamp']
            }
        }
        
        # Save to phase_4_deployment output directory
        output_dir = 'phase_4_deployment/output'
        os.makedirs(output_dir, exist_ok=True)
        
        # Update wallet_balance.json
        wallet_balance_file = os.path.join(output_dir, 'wallet_balance.json')
        with open(wallet_balance_file, 'w') as f:
            json.dump(real_wallet_metrics, f, indent=2)
        
        logger.info(f"✅ Updated wallet balance file: {wallet_balance_file}")
        
        # Update latest_metrics.json to include real wallet data
        latest_metrics_file = os.path.join(output_dir, 'latest_metrics.json')
        if os.path.exists(latest_metrics_file):
            with open(latest_metrics_file, 'r') as f:
                latest_data = json.load(f)
        else:
            latest_data = {}
        
        # Add real wallet data to latest metrics
        latest_data['real_wallet_balance'] = real_wallet_metrics['wallet_balance']
        latest_data['wallet_info'] = real_wallet_metrics['wallet_info']
        
        # Save updated latest metrics
        with open(latest_metrics_file, 'w') as f:
            json.dump(latest_data, f, indent=2)
        
        logger.info(f"✅ Updated latest metrics with real wallet data")
        
        # Also create a specific real_balance.json for the dashboard
        real_balance_file = os.path.join(output_dir, 'real_balance.json')
        with open(real_balance_file, 'w') as f:
            json.dump({
                'sol_balance': balance_data['sol_balance'],
                'usdc_balance': balance_data['usdc_balance'],
                'total_usd_value': balance_data['usd_value'],
                'sol_price': balance_data['sol_price'],
                'wallet_address': balance_data['wallet_address'],
                'last_updated': balance_data['timestamp'],
                'display_format': {
                    'sol_display': f"{balance_data['sol_balance']:.4f} SOL",
                    'usdc_display': f"{balance_data['usdc_balance']:.2f} USDC",
                    'usd_display': f"${balance_data['usd_value']:.2f}"
                }
            }, f, indent=2)
        
        logger.info(f"✅ Created real balance file: {real_balance_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating dashboard balance data: {e}")
        return False

async def main():
    """Main function to update dashboard with real balance."""
    print("🔧 UPDATING DASHBOARD WITH REAL WALLET BALANCE")
    print("=" * 50)
    
    # Load environment variables from .env file
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    except Exception as e:
        logger.warning(f"Could not load .env file: {e}")
    
    # Update dashboard balance data
    success = await update_dashboard_balance_data()
    
    if success:
        print("✅ DASHBOARD BALANCE UPDATE SUCCESSFUL!")
        print("🔄 Dashboard will now show real wallet balances")
        print("📊 Refresh your dashboard to see updated data")
        return 0
    else:
        print("❌ DASHBOARD BALANCE UPDATE FAILED!")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
