#!/usr/bin/env python3
"""
Configuration Validation Script for Enhanced Trading System

This script validates the configuration files and environment variables.
"""

import os
import yaml
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('validate_config')

def main():
    """Main validation function."""
    logger.info("🔧 CONFIGURATION VALIDATION")
    logger.info("=" * 50)

    success = True

    # Check config.yaml
    if os.path.exists('config.yaml'):
        try:
            with open('config.yaml', 'r') as f:
                config = yaml.safe_load(f)
            logger.info("✅ config.yaml - Valid")

            # Check required sections
            required_sections = ['mode', 'solana', 'wallet', 'risk', 'execution']
            for section in required_sections:
                if section in config:
                    logger.info(f"✅ {section} section - Present")
                else:
                    logger.warning(f"⚠️ {section} section - Missing")

        except Exception as e:
            logger.error(f"❌ config.yaml - Error: {e}")
            success = False
    else:
        logger.error("❌ config.yaml - Not found")
        success = False

    # Check .env file
    if os.path.exists('.env'):
        try:
            load_dotenv()
            logger.info("✅ .env file - Valid")

            # Check required environment variables
            required_vars = ['HELIUS_API_KEY', 'WALLET_ADDRESS', 'TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID']
            for var in required_vars:
                if os.getenv(var):
                    logger.info(f"✅ {var} - Set")
                else:
                    logger.warning(f"⚠️ {var} - Not set")

        except Exception as e:
            logger.error(f"❌ .env file - Error: {e}")
            success = False
    else:
        logger.error("❌ .env file - Not found")
        success = False

    # Check additional config files
    config_files = [
        'config/jupiter_config.yaml',
        'config/token_registry.yaml'
    ]

    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    yaml.safe_load(f)
                logger.info(f"✅ {config_file} - Valid")
            except Exception as e:
                logger.error(f"❌ {config_file} - Error: {e}")
                success = False
        else:
            logger.warning(f"⚠️ {config_file} - Not found")

    # Check wallet directory
    if os.path.exists('wallet/trading_wallet_keypair.json'):
        logger.info("✅ Wallet keypair - Present")
    else:
        logger.warning("⚠️ Wallet keypair - Not found")

    logger.info("=" * 50)
    if success:
        logger.info("✅ CONFIGURATION VALIDATION PASSED")
        return 0
    else:
        logger.error("❌ CONFIGURATION VALIDATION FAILED")
        return 1

if __name__ == "__main__":
    exit(main())
