#!/usr/bin/env python3
"""
Sync Live Trading Data with Dashboard Metrics

This script reads the live trading data and updates the dashboard metrics
to ensure the dashboard displays current live trading performance.
"""

import os
import json
import glob
from datetime import datetime, timedelta
from pathlib import Path

def load_live_trades():
    """Load all live trading trades."""
    trades_dir = "output/live_production/trades"
    if not os.path.exists(trades_dir):
        return []
    
    trades = []
    trade_files = glob.glob(os.path.join(trades_dir, "trade_*.json"))
    
    for trade_file in sorted(trade_files):
        try:
            with open(trade_file, 'r') as f:
                trade_data = json.load(f)
            trades.append(trade_data)
        except Exception as e:
            print(f"Error loading {trade_file}: {e}")
    
    return trades

def calculate_metrics(trades):
    """Calculate comprehensive metrics from trades."""
    if not trades:
        return {
            "cycles_completed": 0,
            "cycles_successful": 0,
            "trades_attempted": 0,
            "trades_executed": 0,
            "trades_rejected": 0,
            "success_rate": 0.0,
            "average_execution_time": 0.0,
            "total_volume_usd": 0.0,
            "total_volume_sol": 0.0,
            "total_fees_sol": 0.0,
            "total_pnl_sol": 0.0,
            "total_pnl_usd": 0.0
        }
    
    # Basic counts
    total_trades = len(trades)
    successful_trades = sum(1 for t in trades if t.get('result', {}).get('success', False))
    
    # Execution times
    execution_times = [t.get('execution_time', 0) for t in trades if t.get('execution_time')]
    avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
    
    # Volume calculations
    total_volume_sol = 0
    total_volume_usd = 0
    
    for trade in trades:
        signal = trade.get('signal', {})
        size = signal.get('size', 0)
        price = signal.get('price', 180.0)  # Default SOL price
        
        total_volume_sol += size
        total_volume_usd += size * price
    
    # Estimate fees (0.000005 SOL per transaction)
    total_fees_sol = successful_trades * 0.000005
    
    return {
        "cycles_completed": total_trades,
        "cycles_successful": successful_trades,
        "trades_attempted": total_trades,
        "trades_executed": successful_trades,
        "trades_rejected": total_trades - successful_trades,
        "success_rate": successful_trades / total_trades if total_trades > 0 else 0.0,
        "average_execution_time": avg_execution_time,
        "total_volume_usd": total_volume_usd,
        "total_volume_sol": total_volume_sol,
        "total_fees_sol": total_fees_sol,
        "total_pnl_sol": 0.0,  # Would need price tracking for real PnL
        "total_pnl_usd": 0.0
    }

def update_dashboard_metrics(trades, metrics):
    """Update dashboard metrics files."""
    
    # Ensure dashboard directory exists
    dashboard_dir = "output/live_production/dashboard"
    os.makedirs(dashboard_dir, exist_ok=True)
    
    # Get session info
    session_start = None
    session_end = None
    
    if trades:
        session_start = trades[0].get('timestamp')
        session_end = trades[-1].get('timestamp')
    
    # Calculate session duration
    session_duration_minutes = 0
    if session_start and session_end:
        try:
            start_dt = datetime.fromisoformat(session_start.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(session_end.replace('Z', '+00:00'))
            session_duration_minutes = (end_dt - start_dt).total_seconds() / 60
        except:
            pass
    
    # Update latest_cycle.json
    latest_cycle = {
        "session_start": session_start or datetime.now().isoformat(),
        "session_end": session_end,
        "session_duration_minutes": session_duration_minutes,
        "metrics": {
            "cycles_completed": metrics["cycles_completed"],
            "cycles_successful": metrics["cycles_successful"],
            "trades_attempted": metrics["trades_attempted"],
            "trades_executed": metrics["trades_executed"],
            "trades_rejected": metrics["trades_rejected"]
        },
        "executor_metrics": {
            "success_rate": metrics["success_rate"],
            "average_execution_time": metrics["average_execution_time"],
            "total_volume_usd": metrics["total_volume_usd"],
            "total_volume_sol": metrics["total_volume_sol"],
            "total_fees_sol": metrics["total_fees_sol"]
        },
        "pnl_metrics": {
            "total_pnl_sol": metrics["total_pnl_sol"],
            "total_pnl_usd": metrics["total_pnl_usd"],
            "realized_pnl_sol": 0.0,
            "unrealized_pnl_sol": 0.0,
            "session_pnl_sol": metrics["total_pnl_sol"],
            "session_pnl_usd": metrics["total_pnl_usd"]
        },
        "risk_metrics": {
            "max_drawdown": 0.0,
            "current_exposure": metrics["total_volume_sol"],
            "risk_score": min(metrics["success_rate"] * 100, 100)
        }
    }
    
    # Save latest_cycle.json
    with open(os.path.join(dashboard_dir, "latest_cycle.json"), 'w') as f:
        json.dump(latest_cycle, f, indent=2)
    
    # Update performance_metrics.json
    performance_metrics = {
        "timestamp": datetime.now().isoformat(),
        "live_trading": {
            "status": "active" if trades else "inactive",
            "total_trades": len(trades),
            "successful_trades": metrics["trades_executed"],
            "success_rate": metrics["success_rate"],
            "total_volume_sol": metrics["total_volume_sol"],
            "total_volume_usd": metrics["total_volume_usd"],
            "average_execution_time": metrics["average_execution_time"]
        },
        "recent_trades": [
            {
                "timestamp": trade.get('timestamp'),
                "action": trade.get('signal', {}).get('action'),
                "market": trade.get('signal', {}).get('market'),
                "size": trade.get('signal', {}).get('size'),
                "success": trade.get('result', {}).get('success'),
                "signature": trade.get('result', {}).get('signature', '')[:16] + '...' if trade.get('result', {}).get('signature') else None
            }
            for trade in trades[-10:]  # Last 10 trades
        ]
    }
    
    # Save performance_metrics.json
    with open(os.path.join(dashboard_dir, "performance_metrics.json"), 'w') as f:
        json.dump(performance_metrics, f, indent=2)
    
    return latest_cycle, performance_metrics

def main():
    """Main sync function."""
    print("🔄 SYNCING LIVE TRADING DATA WITH DASHBOARD")
    print("=" * 50)
    
    # Load live trades
    print("📊 Loading live trading data...")
    trades = load_live_trades()
    print(f"✅ Loaded {len(trades)} trades")
    
    if trades:
        latest_trade = trades[-1]
        print(f"📈 Latest trade: {latest_trade.get('timestamp')}")
        print(f"🎯 Latest action: {latest_trade.get('signal', {}).get('action')} {latest_trade.get('signal', {}).get('size')} SOL")
    
    # Calculate metrics
    print("\n📊 Calculating metrics...")
    metrics = calculate_metrics(trades)
    print(f"✅ Success rate: {metrics['success_rate']*100:.1f}%")
    print(f"✅ Total volume: {metrics['total_volume_sol']:.6f} SOL (${metrics['total_volume_usd']:.2f})")
    print(f"✅ Avg execution time: {metrics['average_execution_time']:.3f}s")
    
    # Update dashboard
    print("\n📊 Updating dashboard metrics...")
    latest_cycle, performance_metrics = update_dashboard_metrics(trades, metrics)
    print("✅ Dashboard metrics updated")
    
    # Also update phase_4_deployment dashboard data
    phase4_dir = "phase_4_deployment/output"
    os.makedirs(phase4_dir, exist_ok=True)
    
    # Create latest_metrics.json for phase_4_deployment dashboard
    phase4_metrics = {
        "timestamp": datetime.now().isoformat(),
        "live_trading": {
            "status": "active" if trades else "inactive",
            "trades": trades[-20:] if len(trades) > 20 else trades,  # Last 20 trades
            "real_transactions": [t for t in trades if t.get('result', {}).get('success')],
            "metrics": metrics
        },
        "real_time_metrics": {
            "execution_stats": {
                "total_trades": metrics["trades_executed"],
                "success_rate": metrics["success_rate"],
                "avg_execution_time": metrics["average_execution_time"]
            },
            "live_pnl": {
                "total_volume_sol": metrics["total_volume_sol"],
                "total_volume_usd": metrics["total_volume_usd"],
                "total_fees_sol": metrics["total_fees_sol"]
            }
        }
    }
    
    with open(os.path.join(phase4_dir, "latest_metrics.json"), 'w') as f:
        json.dump(phase4_metrics, f, indent=2)
    
    print("✅ Phase 4 dashboard metrics updated")
    
    print("\n🎉 DASHBOARD SYNC COMPLETE!")
    print(f"📊 Dashboard will now show {len(trades)} live trades")
    print("🔄 Refresh your dashboard to see updated metrics")
    
    return 0

if __name__ == "__main__":
    exit(main())
