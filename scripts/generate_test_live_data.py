#!/usr/bin/env python3
"""
Generate Test Live Trading Data

This script generates sample live trading data to test the dashboard and trade analyzers.
"""

import json
import os
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List

def generate_test_trade(trade_id: int, timestamp: datetime) -> Dict[str, Any]:
    """Generate a test trade."""
    actions = ['BUY', 'SELL']
    action = random.choice(actions)
    
    # Generate realistic position sizes
    sol_amount = round(random.uniform(0.001, 0.1), 6)
    sol_price = round(random.uniform(170, 190), 2)
    usd_amount = round(sol_amount * sol_price, 2)
    
    # Generate confidence
    confidence = round(random.uniform(0.6, 0.95), 2)
    
    # Simulate execution success (90% success rate)
    success = random.random() < 0.9
    
    trade = {
        'trade_id': f"trade_{trade_id:06d}",
        'timestamp': timestamp.isoformat(),
        'signal': {
            'action': action,
            'market': 'SOL-USDC',
            'price': sol_price,
            'size': sol_amount,
            'confidence': confidence,
            'strategy': random.choice(['momentum', 'mean_reversion', 'whale_follow'])
        },
        'position_data': {
            'position_size_sol': sol_amount,
            'position_size_usd': usd_amount,
            'risk_score': round(random.uniform(0.1, 0.8), 2)
        },
        'transaction_result': {
            'success': success,
            'execution_time': round(random.uniform(0.5, 3.0), 3),
            'signature': f"{''.join(random.choices('0123456789abcdef', k=88))}" if success else None,
            'confirmation': {
                'status': 'confirmed' if success else 'failed',
                'slot': random.randint(200000000, 300000000) if success else None
            }
        }
    }
    
    return trade

def generate_test_metrics(trades: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate test metrics from trades."""
    total_trades = len(trades)
    successful_trades = sum(1 for t in trades if t['transaction_result']['success'])
    real_transactions = successful_trades  # All successful trades have signatures
    
    # Calculate volumes
    total_volume_usd = sum(t['position_data']['position_size_usd'] for t in trades)
    total_volume_sol = sum(t['position_data']['position_size_sol'] for t in trades)
    
    # Calculate execution times
    execution_times = [t['transaction_result']['execution_time'] for t in trades if t['transaction_result']['success']]
    avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
    
    session_start = trades[0]['timestamp'] if trades else datetime.now().isoformat()
    session_end = trades[-1]['timestamp'] if trades else datetime.now().isoformat()
    
    # Calculate session duration
    if trades:
        start_dt = datetime.fromisoformat(session_start.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(session_end.replace('Z', '+00:00'))
        duration_minutes = (end_dt - start_dt).total_seconds() / 60
    else:
        duration_minutes = 0
    
    metrics = {
        'session_start': session_start,
        'session_end': session_end,
        'session_duration_minutes': round(duration_minutes, 1),
        'metrics': {
            'cycles_completed': random.randint(50, 200),
            'cycles_successful': random.randint(45, 190),
            'trades_attempted': total_trades,
            'trades_executed': successful_trades
        },
        'executor_metrics': {
            'success_rate': successful_trades / total_trades if total_trades > 0 else 0,
            'average_execution_time': round(avg_execution_time, 3),
            'total_volume_usd': round(total_volume_usd, 2),
            'total_volume_sol': round(total_volume_sol, 6)
        }
    }
    
    return metrics

def create_test_data():
    """Create test live trading data."""
    print("🚀 Generating test live trading data...")
    
    # Create output directories
    enhanced_live_dir = "output/enhanced_live_trading"
    trades_dir = os.path.join(enhanced_live_dir, "trades")
    
    os.makedirs(trades_dir, exist_ok=True)
    
    # Generate trades over the last 2 hours
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=2)
    
    trades = []
    trade_count = random.randint(15, 30)  # 15-30 trades
    
    for i in range(trade_count):
        # Distribute trades over the time period
        trade_time = start_time + timedelta(seconds=random.randint(0, 7200))  # 2 hours = 7200 seconds
        trade = generate_test_trade(i + 1, trade_time)
        trades.append(trade)
        
        # Save individual trade file
        trade_file = os.path.join(trades_dir, f"trade_{i+1:06d}.json")
        with open(trade_file, 'w') as f:
            json.dump(trade, f, indent=2)
    
    # Sort trades by timestamp
    trades.sort(key=lambda x: x['timestamp'])
    
    # Generate and save metrics
    metrics = generate_test_metrics(trades)
    metrics_file = os.path.join(enhanced_live_dir, "latest_metrics.json")
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Generate session info
    session_info = {
        'start_time': trades[0]['timestamp'] if trades else datetime.now().isoformat(),
        'end_time': trades[-1]['timestamp'] if trades else datetime.now().isoformat(),
        'total_trades': len(trades),
        'real_transactions': sum(1 for t in trades if t['transaction_result']['success']),
        'status': 'active'
    }
    
    session_file = os.path.join(enhanced_live_dir, "session_info.json")
    with open(session_file, 'w') as f:
        json.dump(session_info, f, indent=2)
    
    print(f"✅ Generated {len(trades)} test trades")
    print(f"📊 Success rate: {metrics['executor_metrics']['success_rate']*100:.1f}%")
    print(f"💰 Total volume: ${metrics['executor_metrics']['total_volume_usd']:.2f}")
    print(f"📁 Data saved to: {enhanced_live_dir}")
    
    # Also create some data in the phase_4_deployment output directory
    phase4_enhanced_dir = "phase_4_deployment/output/enhanced_live_trading"
    phase4_trades_dir = os.path.join(phase4_enhanced_dir, "trades")
    
    os.makedirs(phase4_trades_dir, exist_ok=True)
    
    # Copy some trades to phase_4_deployment directory
    for i, trade in enumerate(trades[-5:]):  # Last 5 trades
        trade_file = os.path.join(phase4_trades_dir, f"trade_{i+1:06d}.json")
        with open(trade_file, 'w') as f:
            json.dump(trade, f, indent=2)
    
    # Copy metrics
    phase4_metrics_file = os.path.join(phase4_enhanced_dir, "latest_metrics.json")
    with open(phase4_metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    print(f"📁 Also saved data to: {phase4_enhanced_dir}")

def create_test_production_data():
    """Create test production data."""
    print("🏭 Generating test production data...")
    
    # Create production output directory
    live_prod_dir = "output/live_production"
    os.makedirs(live_prod_dir, exist_ok=True)
    
    # Generate production metrics
    production_metrics = {
        'wallet_balance_sol': round(random.uniform(1.0, 10.0), 4),
        'wallet_balance_usd': round(random.uniform(180, 1800), 2),
        'total_trades_today': random.randint(20, 50),
        'successful_trades_today': random.randint(18, 45),
        'daily_pnl_sol': round(random.uniform(-0.1, 0.5), 6),
        'daily_pnl_usd': round(random.uniform(-18, 90), 2),
        'last_update': datetime.now().isoformat()
    }
    
    prod_metrics_file = os.path.join(live_prod_dir, "production_metrics.json")
    with open(prod_metrics_file, 'w') as f:
        json.dump(production_metrics, f, indent=2)
    
    # Generate wallet info
    wallet_info = {
        'address': '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',  # Example address
        'balance_sol': production_metrics['wallet_balance_sol'],
        'balance_usd': production_metrics['wallet_balance_usd'],
        'last_updated': datetime.now().isoformat()
    }
    
    wallet_file = os.path.join(live_prod_dir, "wallet_info.json")
    with open(wallet_file, 'w') as f:
        json.dump(wallet_info, f, indent=2)
    
    print(f"✅ Generated production data")
    print(f"💰 Wallet balance: {production_metrics['wallet_balance_sol']:.4f} SOL")
    print(f"📊 Daily P&L: ${production_metrics['daily_pnl_usd']:.2f}")

def main():
    """Main function."""
    print("🎯 GENERATING TEST DATA FOR DASHBOARD AND ANALYZERS")
    print("=" * 60)
    
    create_test_data()
    print()
    create_test_production_data()
    
    print()
    print("🎉 Test data generation complete!")
    print("📊 You can now:")
    print("   1. View the dashboard at http://localhost:8501")
    print("   2. Run the rich trade analyzer: python scripts/rich_trade_analyzer.py")
    print("   3. Run the enhanced trade analyzer: python scripts/analyze_trades.py")

if __name__ == "__main__":
    main()
