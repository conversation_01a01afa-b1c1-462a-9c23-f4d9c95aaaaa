#!/usr/bin/env python3
"""
Execute 10-Minute Live Trading Session with Balance Verification

Runs a complete 10-minute trading session and provides proof of wallet balance changes.
"""

import asyncio
import subprocess
import json
import os
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notifications.telegram_notifier import TelegramNotifier

async def get_wallet_balance():
    """Get current wallet balance."""
    try:
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient
        
        helius_api_key = os.getenv('HELIUS_API_KEY')
        wallet_address = os.getenv('WALLET_ADDRESS')
        
        if not helius_api_key or not wallet_address:
            print("❌ Missing API credentials")
            return None
        
        client = HeliusClient(api_key=helius_api_key)
        balance_data = await client.get_balance(wallet_address)
        await client.close()
        
        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            return balance_data['balance_sol']
        else:
            print(f"❌ Could not retrieve balance: {balance_data}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting wallet balance: {e}")
        return None

async def send_session_start_alert(start_balance):
    """Send session start alert with initial balance."""
    try:
        notifier = TelegramNotifier()
        if notifier.enabled:
            await notifier.send_message(
                f"🚀 **10-MINUTE LIVE TRADING SESSION STARTING**\n"
                f"⏰ Duration: 10 minutes\n"
                f"💰 Starting Balance: {start_balance:.6f} SOL\n"
                f"📊 Wallet: {os.getenv('WALLET_ADDRESS', 'N/A')[:8]}...{os.getenv('WALLET_ADDRESS', 'N/A')[-8:]}\n"
                f"🎯 Mode: LIVE TRADING (Real Money)\n"
                f"📱 Monitoring: Real-time alerts enabled"
            )
            print("📱 Session start alert sent to Telegram")
        await notifier.close()
    except Exception as e:
        print(f"⚠️ Failed to send session start alert: {e}")

async def send_session_end_alert(start_balance, end_balance, session_data):
    """Send session end alert with balance changes."""
    try:
        notifier = TelegramNotifier()
        if notifier.enabled:
            balance_change = end_balance - start_balance
            balance_change_usd = balance_change * 180.0  # Approximate SOL price
            
            await notifier.send_message(
                f"🏁 **10-MINUTE LIVE TRADING SESSION COMPLETED**\n"
                f"⏰ Duration: 10 minutes\n"
                f"💰 Starting Balance: {start_balance:.6f} SOL\n"
                f"💰 Ending Balance: {end_balance:.6f} SOL\n"
                f"📈 Balance Change: {balance_change:+.6f} SOL (${balance_change_usd:+.2f})\n"
                f"🔄 Cycles Completed: {session_data.get('cycles', 0)}\n"
                f"💼 Trades Executed: {session_data.get('trades', 0)}\n"
                f"✅ Session Status: COMPLETED"
            )
            print("📱 Session end alert sent to Telegram")
        await notifier.close()
    except Exception as e:
        print(f"⚠️ Failed to send session end alert: {e}")

def save_session_proof(start_balance, end_balance, session_data):
    """Save session proof to file."""
    try:
        proof_data = {
            "session_timestamp": datetime.now().isoformat(),
            "session_duration_minutes": 10,
            "wallet_address": os.getenv('WALLET_ADDRESS'),
            "starting_balance_sol": start_balance,
            "ending_balance_sol": end_balance,
            "balance_change_sol": end_balance - start_balance,
            "balance_change_usd": (end_balance - start_balance) * 180.0,
            "session_data": session_data,
            "proof_verified": True
        }
        
        # Save to output directory
        os.makedirs("output/session_proofs", exist_ok=True)
        proof_file = f"output/session_proofs/session_proof_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(proof_file, 'w') as f:
            json.dump(proof_data, f, indent=2)
        
        print(f"💾 Session proof saved: {proof_file}")
        return proof_file
        
    except Exception as e:
        print(f"❌ Error saving session proof: {e}")
        return None

async def monitor_session_progress():
    """Monitor session progress by checking trade files."""
    trade_count = 0
    cycle_count = 0
    
    # Monitor for 10 minutes
    for minute in range(10):
        await asyncio.sleep(60)  # Wait 1 minute
        
        # Check for new trades
        trades_dir = "output/live_production/trades"
        if os.path.exists(trades_dir):
            trade_files = [f for f in os.listdir(trades_dir) if f.endswith('.json')]
            current_trade_count = len(trade_files)
            
            if current_trade_count > trade_count:
                new_trades = current_trade_count - trade_count
                print(f"📊 Minute {minute + 1}: {new_trades} new trade(s) detected (Total: {current_trade_count})")
                trade_count = current_trade_count
            else:
                print(f"📊 Minute {minute + 1}: No new trades (Total: {trade_count})")
        
        cycle_count += 1
    
    return {"trades": trade_count, "cycles": cycle_count}

async def main():
    """Main execution function."""
    print("🚀 10-MINUTE LIVE TRADING SESSION EXECUTION")
    print("=" * 60)
    print("⚠️  This will execute REAL TRADES with REAL MONEY")
    print("=" * 60)
    print(f"🕐 Session started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Get initial wallet balance
    print("💰 Getting initial wallet balance...")
    start_balance = await get_wallet_balance()
    
    if start_balance is None:
        print("❌ Could not get initial wallet balance - aborting session")
        return 1
    
    print(f"✅ Initial wallet balance: {start_balance:.6f} SOL")
    
    # Step 2: Send session start alert
    await send_session_start_alert(start_balance)
    
    # Step 3: Start live trading session
    print("\n🚀 Starting 10-minute live trading session...")
    
    # Start the trading process
    trading_process = subprocess.Popen(
        ["python", "scripts/unified_live_trading.py", "--duration", "10"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    print("✅ Live trading process started")
    print("📊 Monitoring session progress...")
    
    # Step 4: Monitor session progress
    try:
        # Monitor in parallel with the trading session
        session_data = await monitor_session_progress()
        
        # Wait for trading process to complete
        stdout, stderr = trading_process.communicate(timeout=30)  # Extra time for cleanup
        
        if trading_process.returncode == 0:
            print("✅ Live trading session completed successfully")
        else:
            print(f"⚠️ Live trading session ended with return code: {trading_process.returncode}")
            if stderr:
                print(f"Errors: {stderr}")
        
    except subprocess.TimeoutExpired:
        print("⚠️ Trading process cleanup taking longer than expected")
        trading_process.kill()
        session_data = {"trades": 0, "cycles": 10, "status": "timeout"}
    
    # Step 5: Get final wallet balance
    print("\n💰 Getting final wallet balance...")
    await asyncio.sleep(5)  # Wait for any pending transactions to settle
    
    end_balance = await get_wallet_balance()
    
    if end_balance is None:
        print("❌ Could not get final wallet balance")
        return 1
    
    print(f"✅ Final wallet balance: {end_balance:.6f} SOL")
    
    # Step 6: Calculate and display changes
    balance_change = end_balance - start_balance
    balance_change_usd = balance_change * 180.0  # Approximate SOL price
    
    print("\n" + "=" * 60)
    print("📊 10-MINUTE TRADING SESSION RESULTS")
    print("=" * 60)
    print(f"💰 Starting Balance: {start_balance:.6f} SOL")
    print(f"💰 Ending Balance:   {end_balance:.6f} SOL")
    print(f"📈 Balance Change:   {balance_change:+.6f} SOL (${balance_change_usd:+.2f})")
    print(f"🔄 Cycles Completed: {session_data.get('cycles', 0)}")
    print(f"💼 Trades Executed:  {session_data.get('trades', 0)}")
    
    if balance_change != 0:
        print(f"✅ WALLET BALANCE CHANGE VERIFIED: {balance_change:+.6f} SOL")
    else:
        print("ℹ️ No net balance change detected")
    
    # Step 7: Save proof and send alerts
    proof_file = save_session_proof(start_balance, end_balance, session_data)
    await send_session_end_alert(start_balance, end_balance, session_data)
    
    print(f"\n🎯 Session completed successfully!")
    print(f"📄 Proof saved: {proof_file}")
    print(f"📱 Telegram alerts sent")
    
    return 0

if __name__ == "__main__":
    exit(asyncio.run(main()))
