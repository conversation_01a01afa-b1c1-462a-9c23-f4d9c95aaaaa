#!/usr/bin/env python3
"""
10-Minute Live Trading System Test

Comprehensive test of all live trading systems for 10 minutes.
"""

import asyncio
import subprocess
import time
import os
import sys
import json
import signal
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notifications.telegram_notifier import TelegramNotifier

class LiveTradingSystemTest:
    def __init__(self):
        self.processes = {}
        self.test_start_time = None
        self.test_duration_minutes = 10
        self.telegram_notifier = None

    async def setup_test(self):
        """Setup the test environment."""
        print("🔧 Setting up 10-minute live trading test...")

        # Initialize Telegram notifier
        try:
            self.telegram_notifier = TelegramNotifier()
            if self.telegram_notifier.enabled:
                await self.telegram_notifier.send_message(
                    "🧪 **10-MINUTE LIVE TRADING SYSTEM TEST STARTING**\n"
                    f"⏰ Duration: {self.test_duration_minutes} minutes\n"
                    f"🎯 Testing: Live Trading + Dashboard + Alerts\n"
                    f"📊 Monitoring: All system components"
                )
                print("✅ Telegram test notification sent")
            else:
                print("⚠️ Telegram not configured")
        except Exception as e:
            print(f"❌ Telegram setup failed: {e}")

        # Clear previous test data
        print("🔄 Clearing previous test data...")
        test_files = [
            "output/enhanced_live_trading/latest_metrics.json",
            "output/enhanced_live_trading/session_info.json"
        ]

        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ Cleared: {file_path}")

        return True

    def start_dashboard(self):
        """Start the dashboard."""
        print("🌐 Starting dashboard...")

        try:
            dashboard_cmd = [
                "streamlit", "run",
                "phase_4_deployment/unified_dashboard/app.py",
                "--server.port", "8505",
                "--server.headless", "true"
            ]

            dashboard_process = subprocess.Popen(
                dashboard_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )

            self.processes['dashboard'] = dashboard_process
            time.sleep(3)  # Wait for startup

            if dashboard_process.poll() is None:
                print("✅ Dashboard started on port 8505")
                return True
            else:
                print("❌ Dashboard failed to start")
                return False

        except Exception as e:
            print(f"❌ Dashboard startup error: {e}")
            return False

    def start_trade_monitor(self):
        """Start the trade monitor (macOS compatible)."""
        print("📊 Trade monitor will run periodically during test...")
        # On macOS, we'll run the analyzer periodically instead of using watch
        print("✅ Trade monitor configured (periodic execution)")
        return True

    async def start_live_trading(self):
        """Start live trading."""
        print("🚀 Starting live trading...")

        try:
            # Convert minutes to hours for the script
            duration_hours = self.test_duration_minutes / 60.0

            trading_cmd = [
                "python", "scripts/unified_live_trading.py",
                "--duration", str(duration_hours)
            ]

            trading_process = subprocess.Popen(
                trading_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            self.processes['trading'] = trading_process
            print(f"✅ Live trading started for {self.test_duration_minutes} minutes")
            return True

        except Exception as e:
            print(f"❌ Live trading startup error: {e}")
            return False

    async def monitor_test_progress(self):
        """Monitor test progress."""
        print("👀 Monitoring test progress...")

        end_time = datetime.now() + timedelta(minutes=self.test_duration_minutes)
        check_interval = 30  # Check every 30 seconds

        while datetime.now() < end_time:
            remaining_time = (end_time - datetime.now()).total_seconds()
            remaining_minutes = remaining_time / 60

            print(f"⏰ Test progress: {remaining_minutes:.1f} minutes remaining")

            # Check process health
            self.check_process_health()

            # Check for new trades
            await self.check_trading_activity()

            # Run trade analyzer every 2 minutes
            if int(remaining_time) % 120 == 0:
                await self.run_trade_analyzer()

            # Wait for next check
            await asyncio.sleep(check_interval)

        print("⏰ 10-minute test period completed!")

    def check_process_health(self):
        """Check if all processes are still running."""
        for name, process in self.processes.items():
            if process.poll() is None:
                print(f"✅ {name.title()}: Running")
            else:
                print(f"❌ {name.title()}: Stopped")

    async def check_trading_activity(self):
        """Check for trading activity."""
        try:
            # Check latest metrics
            metrics_file = "output/enhanced_live_trading/latest_metrics.json"
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    metrics = json.load(f)

                trades_executed = metrics.get('metrics', {}).get('trades_executed', 0)
                cycles_completed = metrics.get('metrics', {}).get('cycles_completed', 0)

                print(f"📊 Activity: {cycles_completed} cycles, {trades_executed} trades")
            else:
                print("📊 Activity: No metrics file yet")

        except Exception as e:
            print(f"⚠️ Error checking activity: {e}")

    async def run_trade_analyzer(self):
        """Run trade analyzer."""
        try:
            print("📊 Running trade analyzer...")
            result = subprocess.run(
                ["python", "scripts/rich_trade_analyzer.py"],
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode == 0:
                print("✅ Trade analyzer completed")
            else:
                print("⚠️ Trade analyzer had warnings")
        except Exception as e:
            print(f"⚠️ Trade analyzer error: {e}")

    async def generate_test_report(self):
        """Generate final test report."""
        print("\n" + "="*60)
        print("📊 10-MINUTE LIVE TRADING TEST REPORT")
        print("="*60)

        # Check final metrics
        try:
            metrics_file = "output/enhanced_live_trading/latest_metrics.json"
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    metrics = json.load(f)

                test_metrics = metrics.get('metrics', {})
                executor_metrics = metrics.get('executor_metrics', {})

                print("🎯 TRADING PERFORMANCE:")
                print(f"   Cycles Completed: {test_metrics.get('cycles_completed', 0)}")
                print(f"   Trades Executed: {test_metrics.get('trades_executed', 0)}")
                print(f"   Success Rate: {executor_metrics.get('success_rate', 0)*100:.1f}%")
                print(f"   Total Volume: {executor_metrics.get('total_volume_sol', 0):.6f} SOL")

            else:
                print("❌ No metrics file found")

        except Exception as e:
            print(f"❌ Error reading metrics: {e}")

        # Check process status
        print("\n🔧 SYSTEM COMPONENTS:")
        for name, process in self.processes.items():
            status = "✅ RUNNING" if process.poll() is None else "❌ STOPPED"
            print(f"   {name.title()}: {status}")

        # Check dashboard accessibility
        print("\n🌐 DASHBOARD STATUS:")
        try:
            import requests
            response = requests.get("http://localhost:8505", timeout=5)
            if response.status_code == 200:
                print("   Dashboard: ✅ ACCESSIBLE at http://localhost:8505")
            else:
                print(f"   Dashboard: ❌ ERROR {response.status_code}")
        except:
            print("   Dashboard: ❌ NOT ACCESSIBLE")

        # Send final Telegram report
        if self.telegram_notifier and self.telegram_notifier.enabled:
            try:
                await self.telegram_notifier.send_message(
                    "✅ **10-MINUTE LIVE TRADING TEST COMPLETED**\n"
                    f"⏰ Duration: {self.test_duration_minutes} minutes\n"
                    f"🎯 All systems tested successfully\n"
                    f"📊 Check dashboard at http://localhost:8505"
                )
                print("📱 Final Telegram report sent")
            except Exception as e:
                print(f"⚠️ Failed to send final report: {e}")

    def cleanup_processes(self):
        """Clean up all processes."""
        print("\n🧹 Cleaning up processes...")

        for name, process in self.processes.items():
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
                    print(f"✅ Stopped {name}")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 Force killed {name}")
            except Exception as e:
                print(f"⚠️ Error stopping {name}: {e}")

    async def run_test(self):
        """Run the complete 10-minute test."""
        self.test_start_time = datetime.now()

        print("🚀 10-MINUTE LIVE TRADING SYSTEM TEST")
        print("="*60)
        print(f"🕐 Test started at: {self.test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ Duration: {self.test_duration_minutes} minutes")
        print("="*60)

        try:
            # Setup
            await self.setup_test()

            # Start all components
            dashboard_ok = self.start_dashboard()
            monitor_ok = self.start_trade_monitor()
            trading_ok = await self.start_live_trading()

            if not all([dashboard_ok, monitor_ok, trading_ok]):
                print("❌ Some components failed to start")
                return False

            print(f"\n✅ All systems started! Running for {self.test_duration_minutes} minutes...")
            print("🌐 Dashboard: http://localhost:8505")
            print("📊 Trade monitor: Running every 15 seconds")
            print("🚀 Live trading: Active")

            # Monitor progress
            await self.monitor_test_progress()

            # Generate report
            await self.generate_test_report()

            return True

        except KeyboardInterrupt:
            print("\n⚠️ Test interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            return False
        finally:
            self.cleanup_processes()
            if self.telegram_notifier:
                await self.telegram_notifier.close()

async def main():
    """Main test function."""
    test = LiveTradingSystemTest()
    success = await test.run_test()

    if success:
        print("\n🎉 10-MINUTE LIVE TRADING TEST COMPLETED SUCCESSFULLY!")
        print("✅ All systems verified and functional")
        return 0
    else:
        print("\n⚠️ 10-MINUTE LIVE TRADING TEST HAD ISSUES")
        print("❌ Some components may need attention")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
