#!/usr/bin/env python3
"""
Reset All Metrics to Zero

Clears all trading data, metrics, and dashboard files to start fresh.
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path

def reset_output_directories():
    """Reset all output directories and create fresh structure."""
    print("🔄 Resetting Output Directories...")
    
    # Directories to reset
    output_dirs = [
        "output/enhanced_live_trading",
        "output/live_production", 
        "output/rl_enhanced_live_trading",
        "output/wallet",
        "output/backtest",
        "output/signals",
        "output/trades",
        "phase_4_deployment/output"
    ]
    
    reset_count = 0
    for dir_path in output_dirs:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"✅ Cleared: {dir_path}")
                reset_count += 1
            except Exception as e:
                print(f"❌ Failed to clear {dir_path}: {e}")
        
        # Recreate directory structure
        try:
            os.makedirs(dir_path, exist_ok=True)
            os.makedirs(f"{dir_path}/trades", exist_ok=True)
            os.makedirs(f"{dir_path}/metrics", exist_ok=True)
            os.makedirs(f"{dir_path}/dashboard", exist_ok=True)
            print(f"✅ Recreated: {dir_path}")
        except Exception as e:
            print(f"❌ Failed to recreate {dir_path}: {e}")
    
    print(f"📊 Reset {reset_count} directories")
    return reset_count > 0

def create_fresh_metrics():
    """Create fresh metric files with zero values."""
    print("\n📊 Creating Fresh Metrics...")
    
    # Fresh metrics template
    fresh_metrics = {
        "session_start": datetime.now().isoformat(),
        "session_end": None,
        "session_duration_minutes": 0,
        "metrics": {
            "cycles_completed": 0,
            "cycles_successful": 0,
            "trades_attempted": 0,
            "trades_executed": 0,
            "trades_rejected": 0
        },
        "executor_metrics": {
            "success_rate": 0.0,
            "average_execution_time": 0.0,
            "total_volume_usd": 0.0,
            "total_volume_sol": 0.0,
            "total_fees_sol": 0.0
        },
        "pnl_metrics": {
            "total_pnl_sol": 0.0,
            "total_pnl_usd": 0.0,
            "realized_pnl_sol": 0.0,
            "unrealized_pnl_sol": 0.0,
            "session_pnl_sol": 0.0,
            "session_pnl_usd": 0.0
        },
        "risk_metrics": {
            "max_drawdown": 0.0,
            "current_exposure": 0.0,
            "risk_score": 0.0
        }
    }
    
    # Session info template
    fresh_session = {
        "start_time": datetime.now().isoformat(),
        "end_time": None,
        "total_trades": 0,
        "real_transactions": 0,
        "status": "ready"
    }
    
    # Create fresh files
    metric_files = [
        ("output/enhanced_live_trading/latest_metrics.json", fresh_metrics),
        ("output/enhanced_live_trading/session_info.json", fresh_session),
        ("output/live_production/dashboard/latest_cycle.json", fresh_metrics),
        ("output/live_production/dashboard/performance_metrics.json", fresh_metrics),
        ("output/rl_enhanced_live_trading/latest_metrics.json", fresh_metrics),
        ("phase_4_deployment/output/latest_metrics.json", fresh_metrics)
    ]
    
    created_count = 0
    for file_path, data in metric_files:
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
            print(f"✅ Created: {file_path}")
            created_count += 1
        except Exception as e:
            print(f"❌ Failed to create {file_path}: {e}")
    
    print(f"📊 Created {created_count} fresh metric files")
    return created_count > 0

def reset_wallet_data():
    """Reset wallet data to current state."""
    print("\n💰 Resetting Wallet Data...")
    
    try:
        # Create fresh wallet data structure
        wallet_data = {
            "wallet_balance": {
                "trading_wallet": 0.0,
                "last_updated": datetime.now().isoformat()
            },
            "wallet_address": os.getenv('WALLET_ADDRESS', 'not_configured'),
            "balance_sol": 0.0,
            "balance_usd": 0.0,
            "timestamp": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat()
        }
        
        os.makedirs("output/wallet", exist_ok=True)
        with open("output/wallet/wallet_balance.json", 'w') as f:
            json.dump(wallet_data, f, indent=2)
        
        print("✅ Wallet data reset")
        return True
        
    except Exception as e:
        print(f"❌ Failed to reset wallet data: {e}")
        return False

def reset_dashboard_cache():
    """Reset dashboard cache and temporary files."""
    print("\n🌐 Resetting Dashboard Cache...")
    
    # Dashboard cache files to remove
    cache_files = [
        "phase_4_deployment/unified_dashboard/.streamlit",
        "phase_4_deployment/unified_dashboard/__pycache__",
        ".streamlit",
        "__pycache__"
    ]
    
    removed_count = 0
    for cache_path in cache_files:
        if os.path.exists(cache_path):
            try:
                if os.path.isdir(cache_path):
                    shutil.rmtree(cache_path)
                else:
                    os.remove(cache_path)
                print(f"✅ Removed cache: {cache_path}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove {cache_path}: {e}")
    
    print(f"📊 Removed {removed_count} cache items")
    return True

def create_reset_summary():
    """Create a reset summary file."""
    print("\n📋 Creating Reset Summary...")
    
    reset_summary = {
        "reset_timestamp": datetime.now().isoformat(),
        "reset_type": "complete_metrics_reset",
        "directories_reset": [
            "output/enhanced_live_trading",
            "output/live_production", 
            "output/rl_enhanced_live_trading",
            "output/wallet",
            "phase_4_deployment/output"
        ],
        "metrics_reset": {
            "trades_executed": 0,
            "total_pnl": 0.0,
            "session_count": 0,
            "success_rate": 0.0
        },
        "next_steps": [
            "Start live trading with fresh metrics",
            "Monitor dashboard for real-time updates",
            "Verify Telegram alerts are working"
        ]
    }
    
    try:
        with open("output/reset_summary.json", 'w') as f:
            json.dump(reset_summary, f, indent=2)
        print("✅ Reset summary created")
        return True
    except Exception as e:
        print(f"❌ Failed to create reset summary: {e}")
        return False

def main():
    """Main reset function."""
    print("🔄 COMPREHENSIVE METRICS RESET")
    print("=" * 50)
    print("Resetting all trading data and metrics to start fresh")
    print("=" * 50)
    print(f"🕐 Reset started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = {}
    
    # Step 1: Reset output directories
    results['directories'] = reset_output_directories()
    
    # Step 2: Create fresh metrics
    results['metrics'] = create_fresh_metrics()
    
    # Step 3: Reset wallet data
    results['wallet'] = reset_wallet_data()
    
    # Step 4: Reset dashboard cache
    results['dashboard_cache'] = reset_dashboard_cache()
    
    # Step 5: Create reset summary
    results['summary'] = create_reset_summary()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 RESET SUMMARY")
    print("=" * 50)
    
    passed_steps = sum(results.values())
    total_steps = len(results)
    
    for step_name, result in results.items():
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{step_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Reset Score: {passed_steps}/{total_steps}")
    
    if passed_steps == total_steps:
        print("\n🎉 COMPLETE RESET SUCCESSFUL!")
        print("✅ All metrics reset to zero")
        print("✅ Fresh directory structure created")
        print("✅ Dashboard cache cleared")
        print("✅ Wallet data reset")
        print()
        print("🚀 READY FOR FRESH LIVE TRADING!")
        print("📊 Dashboard will show clean metrics")
        print("📱 Telegram alerts will track from zero")
        print("💰 All P&L calculations will start fresh")
        print()
        print("🎯 You can now start live trading with completely clean metrics!")
        return 0
    else:
        print("\n⚠️ PARTIAL RESET COMPLETED")
        print("Some steps failed but core reset was successful")
        return 1

if __name__ == "__main__":
    exit(main())
