#!/usr/bin/env python3
"""
Auto Sync Dashboard Metrics

Continuously syncs live trading data with dashboard metrics
to ensure real-time display of trading performance.
"""

import asyncio
import time
import subprocess
from datetime import datetime

async def sync_dashboard_metrics():
    """Run the dashboard sync script."""
    try:
        result = subprocess.run(
            ["python", "scripts/sync_live_dashboard_metrics.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print(f"✅ {datetime.now().strftime('%H:%M:%S')} - Dashboard sync successful")
            return True
        else:
            print(f"❌ {datetime.now().strftime('%H:%M:%S')} - Dashboard sync failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - Dashboard sync timed out")
        return False
    except Exception as e:
        print(f"❌ {datetime.now().strftime('%H:%M:%S')} - Dashboard sync error: {e}")
        return False

async def main():
    """Main auto-sync loop."""
    print("🔄 AUTO DASHBOARD SYNC STARTED")
    print("=" * 40)
    print("Syncing dashboard metrics every 30 seconds...")
    print("Press Ctrl+C to stop")
    print()
    
    sync_count = 0
    success_count = 0
    
    try:
        while True:
            sync_count += 1
            print(f"🔄 Sync #{sync_count} at {datetime.now().strftime('%H:%M:%S')}")
            
            success = await sync_dashboard_metrics()
            if success:
                success_count += 1
            
            print(f"📊 Success rate: {success_count}/{sync_count} ({success_count/sync_count*100:.1f}%)")
            print()
            
            # Wait 30 seconds before next sync
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Auto-sync stopped by user")
        print(f"📊 Final stats: {success_count}/{sync_count} syncs successful")
        return 0

if __name__ == "__main__":
    exit(asyncio.run(main()))
