#!/usr/bin/env python3
"""
Restart Opportunistic Trading with Enhanced ROI Tracking

This script restarts the opportunistic trading system with proper
Telegram ROI notifications without stopping the current session.
"""

import asyncio
import subprocess
import time
import os
import signal
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_current_trading_status():
    """Check if trading is currently running."""
    try:
        # Check for running opportunistic trading processes
        result = subprocess.run(
            ["pgrep", "-f", "opportunistic_live_trading.py"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            logger.info(f"Found {len(pids)} running opportunistic trading processes")
            return [int(pid) for pid in pids if pid]
        else:
            logger.info("No opportunistic trading processes found")
            return []
            
    except Exception as e:
        logger.error(f"Error checking trading status: {e}")
        return []

async def graceful_restart_trading():
    """Gracefully restart the opportunistic trading with ROI tracking."""
    print("🔄 RESTARTING OPPORTUNISTIC TRADING WITH ENHANCED ROI TRACKING")
    print("=" * 70)
    
    # Step 1: Check current status
    print("📊 Step 1: Checking current trading status...")
    current_pids = await check_current_trading_status()
    
    if current_pids:
        print(f"✅ Found running trading processes: {current_pids}")
        
        # Step 2: Send graceful shutdown signal
        print("⏹️ Step 2: Sending graceful shutdown signal...")
        for pid in current_pids:
            try:
                os.kill(pid, signal.SIGTERM)  # Graceful shutdown
                print(f"   Sent SIGTERM to PID {pid}")
            except ProcessLookupError:
                print(f"   PID {pid} already terminated")
            except Exception as e:
                print(f"   Error terminating PID {pid}: {e}")
        
        # Step 3: Wait for graceful shutdown
        print("⏳ Step 3: Waiting for graceful shutdown...")
        for i in range(10):  # Wait up to 10 seconds
            await asyncio.sleep(1)
            remaining_pids = await check_current_trading_status()
            if not remaining_pids:
                print("✅ All processes shut down gracefully")
                break
            print(f"   Still waiting... ({i+1}/10s)")
        else:
            # Force kill if necessary
            print("⚠️ Forcing shutdown of remaining processes...")
            remaining_pids = await check_current_trading_status()
            for pid in remaining_pids:
                try:
                    os.kill(pid, signal.SIGKILL)
                    print(f"   Force killed PID {pid}")
                except:
                    pass
    else:
        print("ℹ️ No running trading processes found")
    
    # Step 4: Start enhanced opportunistic trading
    print("🚀 Step 4: Starting enhanced opportunistic trading with ROI tracking...")
    
    # Calculate remaining time (assuming 6-hour session started at 03:11)
    import datetime
    now = datetime.datetime.now()
    session_start = now.replace(hour=3, minute=11, second=0, microsecond=0)
    
    # If current time is before 3:11, assume it's the next day's session
    if now.time() < datetime.time(3, 11):
        session_start = session_start - datetime.timedelta(days=1)
    
    session_end = session_start + datetime.timedelta(hours=6)
    remaining_time = (session_end - now).total_seconds() / 60  # minutes
    
    if remaining_time > 0:
        print(f"⏰ Remaining session time: {remaining_time:.1f} minutes")
        
        # Start the enhanced opportunistic trading
        cmd = [
            "python", "scripts/opportunistic_live_trading.py",
            "--duration", str(remaining_time),
            "--min-interval", "5",
            "--threshold", "0.7"
        ]
        
        print(f"🎯 Command: {' '.join(cmd)}")
        
        # Start in background
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ Enhanced opportunistic trading started with PID: {process.pid}")
        print(f"📱 ROI tracking enabled with Telegram notifications")
        print(f"⏱️ Session will run for {remaining_time:.1f} minutes")
        
        # Wait a moment to check if it started successfully
        await asyncio.sleep(3)
        
        if process.poll() is None:
            print("✅ Enhanced trading system is running successfully!")
            print("📊 ROI changes will now be visible in Telegram alerts")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Enhanced trading failed to start:")
            print(f"   STDOUT: {stdout}")
            print(f"   STDERR: {stderr}")
            return False
    else:
        print("⏰ Session time has expired. Starting new 6-hour session...")
        
        # Start new 6-hour session
        cmd = [
            "python", "scripts/opportunistic_live_trading.py",
            "--duration", "360",  # 6 hours
            "--min-interval", "5",
            "--threshold", "0.7"
        ]
        
        print(f"🎯 Command: {' '.join(cmd)}")
        
        # Start in background
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ New 6-hour enhanced trading session started with PID: {process.pid}")
        print(f"📱 ROI tracking enabled with Telegram notifications")
        
        # Wait a moment to check if it started successfully
        await asyncio.sleep(3)
        
        if process.poll() is None:
            print("✅ Enhanced trading system is running successfully!")
            print("📊 ROI changes will now be visible in Telegram alerts")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Enhanced trading failed to start:")
            print(f"   STDOUT: {stdout}")
            print(f"   STDERR: {stderr}")
            return False

async def main():
    """Main function."""
    print("🔧 OPPORTUNISTIC TRADING ROI ENHANCEMENT")
    print("=" * 50)
    print("This will restart your trading system with enhanced ROI tracking")
    print("📱 Telegram alerts will now show real-time ROI changes")
    print("=" * 50)
    
    success = await graceful_restart_trading()
    
    if success:
        print("\n🎉 SUCCESS!")
        print("✅ Enhanced opportunistic trading with ROI tracking is now active")
        print("📱 Check your Telegram for ROI updates with each trade")
        print("📊 Dashboard will continue to show live metrics")
        return 0
    else:
        print("\n❌ FAILED!")
        print("Please check the logs and try again")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
