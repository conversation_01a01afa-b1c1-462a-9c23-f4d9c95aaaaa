#!/usr/bin/env python3
"""
Test Fixed Live Trading System

Tests the improved and future-proofed live trading system.
"""

import asyncio
import subprocess
import time
import os
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notifications.telegram_notifier import TelegramNotifier

async def test_environment_validation():
    """Test environment validation."""
    print("🔧 Testing Environment Validation...")
    
    try:
        from scripts.unified_live_trading import UnifiedLiveTrader
        
        # Test initialization
        trader = UnifiedLiveTrader()
        
        if trader.validation_errors:
            print("⚠️ Environment validation issues found:")
            for error in trader.validation_errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ Environment validation passed")
            return True
            
    except Exception as e:
        print(f"❌ Environment validation test failed: {e}")
        return False

async def test_component_initialization():
    """Test component initialization."""
    print("\n🔧 Testing Component Initialization...")
    
    try:
        from scripts.unified_live_trading import UnifiedLiveTrader
        
        trader = UnifiedLiveTrader()
        
        # Test initialization
        init_success = await trader.initialize_components()
        
        if init_success:
            print("✅ Component initialization successful")
            
            # Test wallet balance check
            balance_ok = await trader.check_wallet_balance()
            if balance_ok:
                print("✅ Wallet balance check passed")
                return True
            else:
                print("❌ Wallet balance check failed")
                return False
        else:
            print("❌ Component initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Component initialization test failed: {e}")
        return False

async def test_fallback_trading_cycle():
    """Test fallback trading cycle."""
    print("\n🔄 Testing Fallback Trading Cycle...")
    
    try:
        from scripts.unified_live_trading import UnifiedLiveTrader
        
        trader = UnifiedLiveTrader()
        
        # Initialize components
        if not await trader.initialize_components():
            print("❌ Failed to initialize components")
            return False
        
        # Run fallback trading cycle
        result = await trader.run_fallback_trading_cycle()
        
        if result and 'mode' in result and result['mode'].startswith('fallback'):
            print("✅ Fallback trading cycle completed")
            print(f"   Mode: {result['mode']}")
            print(f"   Signals generated: {result['signals_generated']}")
            print(f"   Trade executed: {result['trade_executed']}")
            return True
        else:
            print("❌ Fallback trading cycle failed")
            return False
            
    except Exception as e:
        print(f"❌ Fallback trading cycle test failed: {e}")
        return False

async def test_short_live_session():
    """Test a very short live trading session."""
    print("\n🚀 Testing Short Live Trading Session...")
    
    try:
        # Run a 3-second live trading session
        process = subprocess.Popen(
            ["python", "scripts/unified_live_trading.py", "--duration", "0.05", "--test-mode"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for completion with timeout
        try:
            stdout, stderr = process.communicate(timeout=30)
            
            if process.returncode == 0:
                print("✅ Short live trading session completed successfully")
                
                # Check for key indicators in output
                if "Session start notification sent" in stdout:
                    print("✅ Telegram notifications working")
                
                if "Running fallback trading cycle" in stdout:
                    print("✅ Fallback trading cycle executed")
                
                if "Trading session completed" in stdout:
                    print("✅ Session completed properly")
                
                return True
            else:
                print("❌ Short live trading session failed")
                print(f"Return code: {process.returncode}")
                if stderr:
                    print(f"Errors: {stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            process.kill()
            print("❌ Short live trading session timed out")
            return False
            
    except Exception as e:
        print(f"❌ Short live trading session test failed: {e}")
        return False

async def test_telegram_integration():
    """Test Telegram integration."""
    print("\n📱 Testing Telegram Integration...")
    
    try:
        notifier = TelegramNotifier()
        
        if not notifier.enabled:
            print("⚠️ Telegram not configured - skipping test")
            return True
        
        # Test connection
        success = await notifier.test_connection()
        if success:
            print("✅ Telegram integration working")
            
            # Send test message
            await notifier.send_message("🧪 Live trading system test completed successfully!")
            print("✅ Test message sent")
            
            await notifier.close()
            return True
        else:
            print("❌ Telegram connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Telegram integration test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🧪 TESTING FIXED LIVE TRADING SYSTEM")
    print("=" * 60)
    print("Testing improved initialization and error handling")
    print("=" * 60)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all tests
    results = {}
    
    results['environment_validation'] = await test_environment_validation()
    results['component_initialization'] = await test_component_initialization()
    results['fallback_trading_cycle'] = await test_fallback_trading_cycle()
    results['short_live_session'] = await test_short_live_session()
    results['telegram_integration'] = await test_telegram_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FIXED LIVE TRADING SYSTEM TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    percentage = (passed_tests / total_tests) * 100
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Test Score: {passed_tests}/{total_tests} ({percentage:.1f}%)")
    
    if percentage == 100:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Live trading system is fully functional")
        print("✅ Initialization issues resolved")
        print("✅ Error handling working properly")
        print("✅ Fallback mechanisms operational")
        print("✅ Telegram integration verified")
        print("\n🚀 SYSTEM IS READY FOR LIVE TRADING!")
        return 0
    elif percentage >= 80:
        print("\n✅ MOST TESTS PASSED!")
        print("⚠️ Minor issues detected but core functionality working")
        return 0
    else:
        print("\n⚠️ SIGNIFICANT ISSUES DETECTED!")
        print("❌ System needs more work before live trading")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
