#!/usr/bin/env python3
"""
Production Live Trading System Tests
Tests for the complete production-ready live trading system.
"""

import pytest
import asyncio
import os
import sys
import json
import logging
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestProductionLiveTrading:
    """Test suite for production live trading system."""

    @pytest.fixture
    def mock_env_vars(self):
        """Mock environment variables for testing."""
        return {
            'WALLET_ADDRESS': 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz',
            'KEYPAIR_PATH': 'wallet/test_keypair.json',
            'HELIUS_API_KEY': 'test_helius_key',
            'BIRDEYE_API_KEY': 'test_birdeye_key',
            'TRADING_ENABLED': 'true',
            'DRY_RUN': 'false',
            'PAPER_TRADING': 'false'
        }

    @pytest.fixture
    def mock_keypair_file(self, tmp_path):
        """Create a mock keypair file for testing."""
        keypair_data = [1] * 64  # Mock 64-byte keypair
        keypair_file = tmp_path / "test_keypair.json"
        with open(keypair_file, 'w') as f:
            json.dump(keypair_data, f)
        return str(keypair_file)

    @pytest.mark.asyncio
    async def test_production_trader_initialization(self, mock_env_vars, mock_keypair_file):
        """Test production trader initialization."""
        with patch.dict(os.environ, mock_env_vars):
            with patch.dict(os.environ, {'KEYPAIR_PATH': mock_keypair_file}):
                from scripts.production_ready_trader import ProductionReadyTrader

                trader = ProductionReadyTrader()

                # Test basic initialization
                assert trader.wallet_address == mock_env_vars['WALLET_ADDRESS']
                assert trader.keypair_path == mock_keypair_file
                assert trader.trading_enabled == True
                assert trader.dry_run == False

    @pytest.mark.asyncio
    async def test_unified_live_trader_initialization(self, mock_env_vars, mock_keypair_file):
        """Test unified live trader initialization."""
        with patch.dict(os.environ, mock_env_vars):
            with patch.dict(os.environ, {'KEYPAIR_PATH': mock_keypair_file}):
                from scripts.unified_live_trading import UnifiedLiveTrader

                trader = UnifiedLiveTrader()

                # Test basic initialization
                assert trader.wallet_address == mock_env_vars['WALLET_ADDRESS']
                assert trader.keypair_path == mock_keypair_file
                assert trader.trading_enabled == True

    @pytest.mark.asyncio
    async def test_keypair_loading(self, mock_keypair_file):
        """Test keypair loading functionality."""
        from scripts.production_ready_trader import ProductionReadyTrader

        with patch.dict(os.environ, {'KEYPAIR_PATH': mock_keypair_file}):
            trader = ProductionReadyTrader()

            # Mock the solders import
            with patch('solders.keypair.Keypair') as mock_keypair_class:
                mock_keypair_instance = Mock()
                mock_keypair_class.from_bytes.return_value = mock_keypair_instance

                # Test component initialization
                result = await trader.initialize_all_components()

                # Should attempt to load keypair
                assert mock_keypair_class.from_bytes.called

    @pytest.mark.asyncio
    async def test_wallet_balance_check(self, mock_env_vars):
        """Test wallet balance checking."""
        with patch.dict(os.environ, mock_env_vars):
            from scripts.production_ready_trader import ProductionReadyTrader

            trader = ProductionReadyTrader()

            # Mock HeliusClient
            with patch('phase_4_deployment.rpc_execution.helius_client.HeliusClient') as mock_client_class:
                mock_client = Mock()
                mock_client.get_balance = AsyncMock(return_value={'balance_sol': 3.5})
                mock_client_class.return_value = mock_client

                # Test balance check
                result = await trader.check_wallet_balance()

                assert result == True
                mock_client.get_balance.assert_called_once_with(trader.wallet_address)

    @pytest.mark.asyncio
    async def test_wallet_insufficient_balance(self, mock_env_vars):
        """Test wallet insufficient balance handling."""
        with patch.dict(os.environ, mock_env_vars):
            from scripts.production_ready_trader import ProductionReadyTrader

            trader = ProductionReadyTrader()

            # Mock HeliusClient with insufficient balance
            with patch('phase_4_deployment.rpc_execution.helius_client.HeliusClient') as mock_client_class:
                mock_client = Mock()
                mock_client.get_balance = AsyncMock(return_value={'balance_sol': 0.05})
                mock_client_class.return_value = mock_client

                # Test balance check
                result = await trader.check_wallet_balance()

                assert result == False

    @pytest.mark.asyncio
    async def test_signal_generation(self, mock_env_vars):
        """Test signal generation in trading cycle."""
        with patch.dict(os.environ, mock_env_vars):
            from scripts.production_ready_trader import ProductionReadyTrader

            trader = ProductionReadyTrader()

            # Mock signal generation components
            mock_opportunities = [
                {'symbol': 'SOL', 'price': 180.0, 'score': 0.8},
                {'symbol': 'USDC', 'price': 1.0, 'score': 0.6}
            ]

            # Test signal generation by mocking the live trading function
            with patch('phase_4_deployment.start_live_trading.run_live_trading') as mock_live_trading:
                mock_live_trading.return_value = None  # Simulate successful run

                # Test that the trader can call the live trading function
                result = await trader.initialize_all_components()

                # This test validates that the production trader can integrate with live trading
                assert result is not None  # Test passes if initialization works

    @pytest.mark.asyncio
    async def test_transaction_building(self, mock_env_vars, mock_keypair_file):
        """Test transaction building functionality."""
        with patch.dict(os.environ, mock_env_vars):
            with patch.dict(os.environ, {'KEYPAIR_PATH': mock_keypair_file}):
                from scripts.production_ready_trader import ProductionReadyTrader

                trader = ProductionReadyTrader()

                # Mock signal
                signal = {
                    'action': 'BUY',
                    'market': 'SOL-USDC',
                    'size': 0.1,
                    'price': 180.0
                }

                # Mock transaction builders
                with patch('core.transaction.enhanced_tx_builder.EnhancedTxBuilder') as mock_enhanced_class:
                    mock_enhanced = Mock()
                    mock_enhanced.build_jupiter_swap = AsyncMock(return_value=b'mock_transaction')
                    mock_enhanced_class.return_value = mock_enhanced

                    # Test transaction building
                    result = await trader.build_production_jupiter_transaction(signal)

                    assert result == b'mock_transaction'
                    mock_enhanced.build_jupiter_swap.assert_called_once_with(signal)

    @pytest.mark.asyncio
    async def test_dry_run_execution(self, mock_env_vars, mock_keypair_file):
        """Test dry run trade execution."""
        with patch.dict(os.environ, mock_env_vars):
            with patch.dict(os.environ, {'KEYPAIR_PATH': mock_keypair_file, 'DRY_RUN': 'true'}):
                from scripts.production_ready_trader import ProductionReadyTrader

                trader = ProductionReadyTrader()
                trader.dry_run = True

                # Mock signal
                signal = {
                    'action': 'BUY',
                    'market': 'SOL-USDC',
                    'size': 0.1,
                    'price': 180.0
                }

                # Mock transaction building
                with patch.object(trader, 'build_production_jupiter_transaction') as mock_build:
                    mock_build.return_value = b'mock_transaction'

                    # Test dry run execution
                    result = await trader.execute_production_trade(signal)

                    assert result['success'] == True
                    assert result['signature'] == 'dry_run_production_test'
                    assert result['provider'] == 'dry_run'

    @pytest.mark.asyncio
    async def test_trade_record_saving(self, mock_env_vars, tmp_path):
        """Test trade record saving functionality."""
        with patch.dict(os.environ, mock_env_vars):
            from scripts.production_ready_trader import ProductionReadyTrader

            trader = ProductionReadyTrader()

            # Mock signal and result
            signal = {'action': 'BUY', 'market': 'SOL-USDC', 'size': 0.1}
            result = {'success': True, 'signature': 'test_signature'}
            execution_time = 1.5

            # Mock the output directory
            with patch('scripts.production_ready_trader.os.makedirs'):
                with patch('scripts.production_ready_trader.open', create=True) as mock_open:
                    mock_file = Mock()
                    mock_open.return_value.__enter__.return_value = mock_file

                    # Test trade record saving
                    await trader.save_production_trade_record(signal, result, execution_time)

                    # Verify file operations
                    mock_open.assert_called_once()
                    assert 'production_trade_' in mock_open.call_args[0][0]

    @pytest.mark.asyncio
    async def test_production_alerts(self, mock_env_vars):
        """Test production alert sending."""
        with patch.dict(os.environ, mock_env_vars):
            from scripts.production_ready_trader import ProductionReadyTrader

            trader = ProductionReadyTrader()

            # Mock signal and result
            signal = {'action': 'BUY', 'market': 'SOL-USDC', 'size': 0.1}
            result = {'success': True, 'signature': 'test_signature', 'provider': 'helius'}

            # Mock alert sending
            with patch('phase_4_deployment.utils.trading_alerts.send_trade_alert') as mock_alert:
                mock_alert.return_value = AsyncMock()

                # Test alert sending
                await trader.send_production_alerts(signal, result)

                # Verify alert was called
                mock_alert.assert_called_once()

    def test_environment_validation(self):
        """Test environment variable validation."""
        required_vars = [
            'WALLET_ADDRESS',
            'KEYPAIR_PATH',
            'HELIUS_API_KEY',
            'BIRDEYE_API_KEY'
        ]

        # Test missing environment variables
        for var in required_vars:
            with patch.dict(os.environ, {}, clear=True):
                from scripts.production_ready_trader import ProductionReadyTrader

                trader = ProductionReadyTrader()

                # Should handle missing environment variables gracefully
                assert getattr(trader, var.lower()) is None or getattr(trader, var.lower()) == ''


class TestLiveTradingComponents:
    """Test suite for live trading components."""

    @pytest.mark.asyncio
    async def test_birdeye_scanner_integration(self):
        """Test Birdeye scanner integration."""
        # Test that we can import and use the birdeye client
        try:
            from shared.utils.api_helpers import BirdeyeClient

            # Mock the client
            with patch('shared.utils.api_helpers.httpx.AsyncClient') as mock_client:
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    'data': [
                        {'symbol': 'SOL', 'price': 180.0, 'volume24h': 1000000}
                    ]
                }

                mock_client.return_value.__aenter__.return_value.get.return_value = mock_response

                client = BirdeyeClient('test_api_key')
                # Test passes if we can create the client
                assert client is not None

        except ImportError:
            # If the module doesn't exist, skip the test
            pytest.skip("BirdeyeClient not available")

    @pytest.mark.asyncio
    async def test_signal_enricher(self):
        """Test signal enricher functionality."""
        from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher

        enricher = SignalEnricher()

        # Test signal enrichment
        signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',
            'price': 180.0,
            'confidence': 0.7
        }

        enriched_signal = enricher.enrich_signal(signal)

        assert 'metadata' in enriched_signal
        assert 'priority_score' in enriched_signal['metadata']
        assert enriched_signal['metadata']['priority_score'] >= 0
        assert enriched_signal['metadata']['priority_score'] <= 1

    @pytest.mark.asyncio
    async def test_transaction_executor(self):
        """Test transaction executor functionality."""
        try:
            from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient

            with patch('phase_4_deployment.rpc_execution.helius_client.HeliusClient') as mock_client_class:
                mock_client_instance = Mock()
                mock_client_instance.send_transaction = AsyncMock(return_value={
                    'success': True,
                    'signature': 'test_signature'
                })
                mock_client_class.return_value = mock_client_instance

                executor = TransactionExecutor(
                    rpc_client=mock_client_instance,
                    keypair_path='test_keypair.json'
                )

                # Test transaction execution
                result = await executor.execute_transaction(b'mock_transaction')

                assert result['success'] == True
                assert 'signature' in result

                await executor.close()

        except ImportError:
            # If modules don't exist, skip the test
            pytest.skip("TransactionExecutor not available")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
