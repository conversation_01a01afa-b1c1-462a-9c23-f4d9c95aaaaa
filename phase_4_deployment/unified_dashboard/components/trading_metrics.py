#!/usr/bin/env python3
"""
Trading Metrics Component

This module provides components for displaying trading metrics in the Synergy7 Unified Dashboard.
"""

import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

def render_trading_metrics(data: Dict[str, Any]):
    """
    Render trading metrics components.

    Args:
        data: Dashboard data
    """
    st.header("Trading Metrics")

    # Check if we have live trading data
    live_trading = data.get('live_trading', {})
    real_time_metrics = data.get('real_time_metrics', {})
    has_live_data = bool(live_trading.get('trades') or real_time_metrics.get('execution_stats'))

    if has_live_data:
        st.info("📊 Displaying metrics with live trading data integration")

    # Create tabs for different trading metrics
    tab1, tab2, tab3, tab4 = st.tabs([
        "PnL Metrics",
        "Strategy Performance",
        "Transaction History",
        "Live Integration" if has_live_data else "Historical Data"
    ])

    with tab1:
        render_pnl_metrics(data)

    with tab2:
        render_strategy_performance(data)

    with tab3:
        render_transaction_history(data)

    with tab4:
        if has_live_data:
            render_live_integration_metrics(data)
        else:
            render_historical_data_metrics(data)

def render_pnl_metrics(data: Dict[str, Any]):
    """
    Render profit and loss metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Profit and Loss Metrics")

    # Create tabs for different profit metrics
    profit_tabs = st.tabs(["Net Profit", "Profit Factor", "Return on Capital", "Drawdown", "Sharpe Ratio"])

    with profit_tabs[0]:  # Net Profit
        st.subheader("Net Profit")

        # Create columns for metrics
        cols = st.columns(3)

        # Get SOL price (placeholder)
        sol_price_usd = 25.10  # This would be fetched from an API in production

        # Get profit data (placeholder)
        net_profit_sol = 125.42
        daily_profit_sol = 12.54
        profit_per_million = 1254

        with cols[0]:
            st.metric(
                "Net Profit (SOL)",
                f"{net_profit_sol:.2f}",
                delta="15.23",
                delta_color="normal"
            )

        with cols[1]:
            st.metric(
                "Profit per $1M Traded",
                f"${profit_per_million:,.0f}",
                delta="$152",
                delta_color="normal"
            )

        with cols[2]:
            st.metric(
                "Daily Profit (SOL)",
                f"{daily_profit_sol:.2f}",
                delta="1.52",
                delta_color="normal"
            )

        # Add USD metrics
        st.markdown(f"### USD Values (SOL Price: ${sol_price_usd:.2f})")
        usd_cols = st.columns(3)

        with usd_cols[0]:
            st.metric(
                "Net Profit (USD)",
                f"${net_profit_sol * sol_price_usd:,.2f}",
                delta=f"${15.23 * sol_price_usd:.2f}",
                delta_color="normal"
            )

        with usd_cols[1]:
            st.metric(
                "Profit per $1M Traded (USD)",
                f"${profit_per_million:,.0f}",
                delta="$152",
                delta_color="normal"
            )

        with usd_cols[2]:
            st.metric(
                "Daily Profit (USD)",
                f"${daily_profit_sol * sol_price_usd:,.2f}",
                delta=f"${1.52 * sol_price_usd:.2f}",
                delta_color="normal"
            )

        # Create a placeholder for the chart
        chart_placeholder = st.empty()

        # Create a dummy chart for net profit
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=[datetime.now() - timedelta(days=i) for i in range(30, 0, -1)],
            y=pd.Series(pd.Series(range(30)).cumsum() * 4.2 + 100).values,
            mode="lines",
            name="Cumulative Profit (SOL)"
        ))

        # Add USD line on secondary y-axis
        fig.add_trace(go.Scatter(
            x=[datetime.now() - timedelta(days=i) for i in range(30, 0, -1)],
            y=pd.Series(pd.Series(range(30)).cumsum() * 4.2 + 100).values * sol_price_usd,
            mode="lines",
            name="Cumulative Profit (USD)",
            yaxis="y2"
        ))

        fig.update_layout(
            title="Cumulative Profit (Last 30 Days)",
            xaxis_title="Date",
            yaxis_title="Profit (SOL)",
            yaxis2=dict(
                title="Profit (USD)",
                overlaying="y",
                side="right"
            ),
            height=400
        )

        chart_placeholder.plotly_chart(fig, use_container_width=True)

def render_strategy_performance(data: Dict[str, Any]):
    """
    Render strategy performance metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Strategy Performance")

    # Check if strategy profiles exist
    if 'strategy_profiles' in data and not data['strategy_profiles'].empty:
        # Sort by composite score (descending)
        top_strategies = data['strategy_profiles'].sort_values('composite_score', ascending=False).head(5)

        # Display top strategies
        st.dataframe(top_strategies[['strategy_id', 'sharpe_ratio', 'win_rate', 'composite_score']])

        # Plot strategy performance
        fig = px.bar(
            top_strategies,
            x='strategy_id',
            y='composite_score',
            color='sharpe_ratio',
            title='Top Strategy Performance',
            labels={'composite_score': 'Composite Score', 'strategy_id': 'Strategy ID', 'sharpe_ratio': 'Sharpe Ratio'}
        )
        st.plotly_chart(fig, use_container_width=True)

        # Strategy details
        st.subheader("Strategy Details")

        # Select strategy to view
        selected_strategy = st.selectbox(
            "Select Strategy",
            top_strategies['strategy_id'].tolist()
        )

        # Display selected strategy details
        if selected_strategy:
            selected_row = top_strategies[top_strategies['strategy_id'] == selected_strategy].iloc[0]

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Sharpe Ratio", f"{selected_row['sharpe_ratio']:.2f}")
                st.metric("Win Rate", f"{selected_row['win_rate']:.2%}")

            with col2:
                st.metric("Composite Score", f"{selected_row['composite_score']:.2f}")
                st.metric("Max Drawdown", f"{selected_row.get('max_drawdown', 0):.2%}")

            with col3:
                st.metric("Profit Factor", f"{selected_row.get('profit_factor', 0):.2f}")
                st.metric("Avg Trade", f"{selected_row.get('avg_trade', 0):.4f}")
    else:
        st.info("No strategy profiles found")

def render_transaction_history(data: Dict[str, Any]):
    """
    Render transaction history.

    Args:
        data: Dashboard data
    """
    st.subheader("Transaction History")

    if 'tx_history' in data and 'transactions' in data['tx_history'] and data['tx_history']['transactions']:
        # Convert to DataFrame for easier display
        tx_df = pd.DataFrame(data['tx_history']['transactions'])

        # Convert timestamp to datetime
        tx_df['datetime'] = pd.to_datetime(tx_df['timestamp'], unit='s')

        # Sort by timestamp (descending)
        tx_df = tx_df.sort_values('datetime', ascending=False)

        # Display transactions
        st.dataframe(tx_df)

        # Transaction status summary
        st.subheader("Transaction Status Summary")

        # Count transactions by status
        status_counts = tx_df['status'].value_counts().reset_index()
        status_counts.columns = ['Status', 'Count']

        # Create pie chart
        fig = px.pie(
            status_counts,
            values='Count',
            names='Status',
            title='Transaction Status Distribution'
        )
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No transaction history found")

def render_live_integration_metrics(data: Dict[str, Any]):
    """
    Render live trading integration metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("🚀 Live Trading Integration")

    live_trading = data.get('live_trading', {})
    real_time_metrics = data.get('real_time_metrics', {})

    # Live trading status
    st.markdown("#### 📊 Live Trading Status")
    status_cols = st.columns(3)

    with status_cols[0]:
        status = live_trading.get('status', 'inactive')
        if status == 'active':
            st.success("🟢 Live Trading Active")
        else:
            st.error("🔴 Live Trading Inactive")

    with status_cols[1]:
        execution_stats = real_time_metrics.get('execution_stats', {})
        total_trades = execution_stats.get('total_trades', 0)
        st.metric("Live Trades", f"{total_trades}")

    with status_cols[2]:
        real_tx_count = execution_stats.get('real_transactions', 0)
        st.metric("Real Transactions", f"{real_tx_count}")

    # Live P&L integration
    if real_time_metrics.get('live_pnl'):
        st.markdown("#### 💰 Live P&L Integration")
        live_pnl = real_time_metrics['live_pnl']

        pnl_cols = st.columns(2)
        with pnl_cols[0]:
            st.metric("Live Volume (SOL)", f"{live_pnl.get('total_volume_sol', 0):.4f}")
        with pnl_cols[1]:
            st.metric("Live Volume (USD)", f"${live_pnl.get('total_volume_usd', 0):.2f}")

    # Recent live trades
    if live_trading.get('trades'):
        st.markdown("#### 📋 Recent Live Trades")
        trades = live_trading['trades'][-5:]  # Last 5 trades

        trade_data = []
        for trade in trades:
            signal = trade.get('signal', {})
            position_data = trade.get('position_data', {})
            tx_result = trade.get('transaction_result', {})

            trade_data.append({
                'Time': trade.get('timestamp', '')[:19],
                'Action': signal.get('action', 'N/A'),
                'Size (SOL)': f"{position_data.get('position_size_sol', 0):.4f}",
                'Size (USD)': f"${position_data.get('position_size_usd', 0):.2f}",
                'Status': '✅ Success' if tx_result.get('success') else '❌ Failed'
            })

        if trade_data:
            trade_df = pd.DataFrame(trade_data)
            st.dataframe(trade_df, use_container_width=True)

def render_historical_data_metrics(data: Dict[str, Any]):
    """
    Render historical data metrics when live trading is not active.

    Args:
        data: Dashboard data
    """
    st.subheader("📊 Historical Data")

    st.info("🔄 Live trading is not currently active. Historical metrics are displayed below.")

    # Show transaction history if available
    tx_history = data.get('tx_history', {})
    if tx_history.get('transactions'):
        st.markdown("#### 📋 Historical Transactions")
        transactions = tx_history['transactions']

        # Create summary metrics
        hist_cols = st.columns(3)
        with hist_cols[0]:
            st.metric("Total Historical Transactions", len(transactions))
        with hist_cols[1]:
            confirmed_tx = sum(1 for tx in transactions if tx.get('status') in ['confirmed', 'finalized'])
            st.metric("Confirmed Transactions", confirmed_tx)
        with hist_cols[2]:
            success_rate = (confirmed_tx / len(transactions) * 100) if transactions else 0
            st.metric("Historical Success Rate", f"{success_rate:.1f}%")

        # Show recent transactions
        recent_tx = transactions[-10:] if len(transactions) > 10 else transactions
        tx_data = []
        for tx in recent_tx:
            tx_data.append({
                'Timestamp': datetime.fromtimestamp(tx.get('timestamp', 0)).strftime('%Y-%m-%d %H:%M:%S'),
                'Signature': tx.get('signature', 'N/A')[:16] + '...' if tx.get('signature') else 'N/A',
                'Status': tx.get('status', 'Unknown'),
                'Type': tx.get('type', 'Unknown')
            })

        if tx_data:
            tx_df = pd.DataFrame(tx_data)
            st.dataframe(tx_df, use_container_width=True)
    else:
        st.warning("No historical transaction data available.")
