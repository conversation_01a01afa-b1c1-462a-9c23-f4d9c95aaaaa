#!/usr/bin/env python3
"""
Data Service for Synergy7 Unified Dashboard

This module provides a centralized data service for the Synergy7 Unified Dashboard.
It handles loading data from various sources, caching, and fallback mechanisms.
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("data_service")

class DataService:
    """
    Centralized data service for the Synergy7 Unified Dashboard.
    """

    def __init__(self, base_dir: Optional[str] = None):
        """
        Initialize the data service.

        Args:
            base_dir: Base directory for data files. If None, uses the parent directory of this file.
        """
        if base_dir is None:
            # Use the parent directory of this file as the base directory
            self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        else:
            self.base_dir = base_dir

        # Use the standardized output directory
        self.output_dir = os.path.join(self.base_dir, 'output')

        # Fallback to the project root output directory if the phase_4_deployment output directory doesn't exist
        if not os.path.exists(self.output_dir):
            project_root = os.path.dirname(self.base_dir)
            fallback_output_dir = os.path.join(project_root, 'output')
            if os.path.exists(fallback_output_dir):
                self.output_dir = fallback_output_dir

        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)

        logger.info(f"Data service initialized with base directory: {self.base_dir}")
        logger.info(f"Output directory: {self.output_dir}")

    def load_json_file(self, file_name: str, default_value: Any = None) -> Any:
        """
        Load a JSON file from the output directory.

        Args:
            file_name: Name of the JSON file to load
            default_value: Default value to return if the file doesn't exist or can't be loaded

        Returns:
            The loaded JSON data or the default value
        """
        file_path = os.path.join(self.output_dir, file_name)
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
            else:
                logger.warning(f"File not found: {file_path}")
                return default_value
        except Exception as e:
            logger.error(f"Error loading {file_path}: {str(e)}")
            return default_value

    def load_csv_file(self, file_name: str, default_value: Any = None) -> pd.DataFrame:
        """
        Load a CSV file from the output directory.

        Args:
            file_name: Name of the CSV file to load
            default_value: Default value to return if the file doesn't exist or can't be loaded

        Returns:
            The loaded DataFrame or the default value
        """
        file_path = os.path.join(self.output_dir, file_name)
        try:
            if os.path.exists(file_path):
                return pd.read_csv(file_path)
            else:
                logger.warning(f"File not found: {file_path}")
                return default_value if default_value is not None else pd.DataFrame()
        except Exception as e:
            logger.error(f"Error loading {file_path}: {str(e)}")
            return default_value if default_value is not None else pd.DataFrame()

    def load_system_metrics(self) -> Dict[str, Any]:
        """
        Load system metrics from metrics.json.

        Returns:
            System metrics data
        """
        return self.load_json_file('metrics.json', {})

    def load_carbon_core_metrics(self) -> Dict[str, Any]:
        """
        Load Carbon Core metrics from carbon_core_metrics.json.

        Returns:
            Carbon Core metrics data
        """
        return self.load_json_file('carbon_core_metrics.json', {})

    def load_transaction_history(self) -> Dict[str, Any]:
        """
        Load transaction history from tx_history.json.

        Returns:
            Transaction history data
        """
        return self.load_json_file('tx_history.json', {'transactions': []})

    def load_enriched_signals(self) -> Dict[str, Any]:
        """
        Load enriched signals from enriched_signals.json.

        Returns:
            Enriched signals data
        """
        return self.load_json_file('enriched_signals.json', {'signals': []})

    def load_token_opportunities(self) -> Dict[str, Any]:
        """
        Load token opportunities from token_opportunities.json.

        Returns:
            Token opportunities data
        """
        return self.load_json_file('token_opportunities.json', {'opportunities': []})

    def load_whale_opportunities(self) -> Dict[str, Any]:
        """
        Load whale opportunities from whale_opportunities.json.

        Returns:
            Whale opportunities data
        """
        return self.load_json_file('whale_opportunities.json', {'opportunities': []})

    def load_strategy_metrics(self) -> Dict[str, Any]:
        """
        Load strategy metrics from strategy_metrics.json.

        Returns:
            Strategy metrics data
        """
        return self.load_json_file('strategy_metrics.json', {})

    def load_helius_metrics(self) -> Dict[str, Any]:
        """
        Load Helius metrics from helius_metrics.json.

        Returns:
            Helius metrics data
        """
        return self.load_json_file('helius_metrics.json', {})

    def load_jito_metrics(self) -> Dict[str, Any]:
        """
        Load Jito metrics from jito_metrics.json.

        Returns:
            Jito metrics data
        """
        return self.load_json_file('jito_metrics.json', {})

    def load_strategy_profiles(self) -> pd.DataFrame:
        """
        Load strategy profiles from strategy_profiles.csv.

        Returns:
            Strategy profiles DataFrame
        """
        # First try to load from phase_4_deployment/output
        df = self.load_csv_file('strategy_profiles.csv')

        if not df.empty:
            return df

        # If not found, try to load from phase_3_rl_agent_training
        try:
            file_path = os.path.join(
                os.path.dirname(self.base_dir),
                'phase_3_rl_agent_training',
                'phase_3_strategy_selector',
                'outputs',
                'strategy_profiles.csv'
            )
            if os.path.exists(file_path):
                return pd.read_csv(file_path)
        except Exception as e:
            logger.error(f"Error loading strategy profiles: {str(e)}")

        return pd.DataFrame()

    def load_live_trading_data(self) -> Dict[str, Any]:
        """
        Load live trading data from enhanced_live_trading output and latest_metrics.json.

        Returns:
            Dictionary containing live trading metrics and trade data
        """
        live_data = {
            'trades': [],
            'metrics': {},
            'session_info': {},
            'real_transactions': [],
            'status': 'inactive'
        }

        try:
            # First, try to load from latest_metrics.json in output directory
            latest_metrics_file = os.path.join(self.output_dir, 'latest_metrics.json')
            if os.path.exists(latest_metrics_file):
                with open(latest_metrics_file, 'r') as f:
                    latest_data = json.load(f)

                # Extract live trading data from latest_metrics.json
                if 'live_trading' in latest_data:
                    live_trading_data = latest_data['live_trading']
                    live_data['trades'] = live_trading_data.get('trades', [])
                    live_data['real_transactions'] = live_trading_data.get('real_transactions', [])
                    live_data['status'] = live_trading_data.get('status', 'inactive')
                    live_data['metrics'] = live_trading_data.get('metrics', {})

                    logger.info(f"Loaded live trading data from latest_metrics.json: {len(live_data['trades'])} trades, {len(live_data['real_transactions'])} real transactions")
                    return live_data

            # Fallback: Load from enhanced_live_trading directory
            enhanced_live_dir = os.path.join(self.output_dir, '..', 'enhanced_live_trading')

            # Load latest metrics
            metrics_file = os.path.join(enhanced_live_dir, 'latest_metrics.json')
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    live_data['metrics'] = json.load(f)
                    live_data['status'] = 'active'

            # Load trades from trades directory
            trades_dir = os.path.join(enhanced_live_dir, 'trades')
            if os.path.exists(trades_dir):
                trade_files = sorted([f for f in os.listdir(trades_dir) if f.startswith('trade_') and f.endswith('.json')])

                for trade_file in trade_files[-20:]:  # Last 20 trades
                    try:
                        with open(os.path.join(trades_dir, trade_file), 'r') as f:
                            trade_data = json.load(f)
                            live_data['trades'].append(trade_data)

                            # Track real transactions
                            if trade_data.get('transaction_result', {}).get('signature'):
                                live_data['real_transactions'].append(trade_data)
                    except Exception as e:
                        logger.warning(f"Error loading trade file {trade_file}: {e}")

            # Load session info
            session_file = os.path.join(enhanced_live_dir, 'session_info.json')
            if os.path.exists(session_file):
                with open(session_file, 'r') as f:
                    live_data['session_info'] = json.load(f)

            logger.info(f"Loaded live trading data: {len(live_data['trades'])} trades, {len(live_data['real_transactions'])} real transactions")

        except Exception as e:
            logger.error(f"Error loading live trading data: {str(e)}")

        return live_data

    def load_live_production_data(self) -> Dict[str, Any]:
        """
        Load live production data from live_production output.

        Returns:
            Dictionary containing live production metrics and data
        """
        production_data = {
            'trades': [],
            'metrics': {},
            'wallet_info': {},
            'performance': {},
            'status': 'inactive'
        }

        try:
            # Load from live_production directory
            live_prod_dir = os.path.join(self.output_dir, '..', 'live_production')

            if os.path.exists(live_prod_dir):
                # Load latest production metrics
                for metrics_file in ['latest_metrics.json', 'production_metrics.json', 'metrics.json']:
                    metrics_path = os.path.join(live_prod_dir, metrics_file)
                    if os.path.exists(metrics_path):
                        with open(metrics_path, 'r') as f:
                            production_data['metrics'] = json.load(f)
                            production_data['status'] = 'active'
                        break

                # Load wallet information
                wallet_file = os.path.join(live_prod_dir, 'wallet_info.json')
                if os.path.exists(wallet_file):
                    with open(wallet_file, 'r') as f:
                        production_data['wallet_info'] = json.load(f)

                # Load performance data
                performance_file = os.path.join(live_prod_dir, 'performance.json')
                if os.path.exists(performance_file):
                    with open(performance_file, 'r') as f:
                        production_data['performance'] = json.load(f)

                # Load recent trades
                trades_dir = os.path.join(live_prod_dir, 'trades')
                if os.path.exists(trades_dir):
                    trade_files = sorted([f for f in os.listdir(trades_dir) if f.endswith('.json')])

                    for trade_file in trade_files[-10:]:  # Last 10 trades
                        try:
                            with open(os.path.join(trades_dir, trade_file), 'r') as f:
                                trade_data = json.load(f)
                                production_data['trades'].append(trade_data)
                        except Exception as e:
                            logger.warning(f"Error loading production trade file {trade_file}: {e}")

            logger.info(f"Loaded live production data: {len(production_data['trades'])} trades")

        except Exception as e:
            logger.error(f"Error loading live production data: {str(e)}")

        return production_data

    def load_real_time_metrics(self) -> Dict[str, Any]:
        """
        Load real-time trading metrics from various sources.

        Returns:
            Dictionary containing real-time metrics
        """
        real_time_data = {
            'current_session': {},
            'live_pnl': {},
            'execution_stats': {},
            'system_health': {},
            'last_update': datetime.now().isoformat()
        }

        try:
            # Load current session data
            live_trading_data = self.load_live_trading_data()
            production_data = self.load_live_production_data()

            # Calculate real-time metrics
            if live_trading_data['trades']:
                trades = live_trading_data['trades']
                real_transactions = live_trading_data['real_transactions']

                # Calculate execution stats
                total_trades = len(trades)
                successful_trades = len([t for t in trades if t.get('result', {}).get('success', False)])
                real_tx_count = len(real_transactions)

                real_time_data['execution_stats'] = {
                    'total_trades': total_trades,
                    'successful_trades': successful_trades,
                    'real_transactions': real_tx_count,
                    'success_rate': successful_trades / total_trades if total_trades > 0 else 0,
                    'execution_rate': real_tx_count / total_trades if total_trades > 0 else 0
                }

                # Calculate live P&L (simplified)
                # Use signal size and price for volume calculation
                total_volume_sol = 0
                total_volume_usd = 0

                for trade in trades:
                    signal = trade.get('signal', {})
                    size = signal.get('size', 0)
                    price = signal.get('price', 180.0)

                    total_volume_sol += size
                    total_volume_usd += size * price

                real_time_data['live_pnl'] = {
                    'total_volume_usd': total_volume_usd,
                    'total_volume_sol': total_volume_sol,
                    'trade_count': total_trades,
                    'real_tx_count': real_tx_count
                }

            # System health from metrics
            if live_trading_data['metrics']:
                metrics = live_trading_data['metrics']
                real_time_data['system_health'] = {
                    'status': 'active' if metrics else 'inactive',
                    'last_trade': trades[-1]['timestamp'] if trades else None,
                    'session_duration': metrics.get('session_duration_minutes', 0),
                    'cycles_completed': metrics.get('metrics', {}).get('cycles_completed', 0)
                }

            logger.info("Loaded real-time metrics successfully")

        except Exception as e:
            logger.error(f"Error loading real-time metrics: {str(e)}")

        return real_time_data

    def load_all_data(self) -> Dict[str, Any]:
        """
        Load all data for the dashboard.

        Returns:
            Dictionary containing all data needed for the dashboard
        """
        data = {
            'system_metrics': self.load_system_metrics(),
            'carbon_core': self.load_carbon_core_metrics(),
            'tx_history': self.load_transaction_history(),
            'enriched_signals': self.load_enriched_signals(),
            'token_opportunities': self.load_token_opportunities(),
            'whale_opportunities': self.load_whale_opportunities(),
            'strategy_metrics': self.load_strategy_metrics(),
            'helius_metrics': self.load_helius_metrics(),
            'jito_metrics': self.load_jito_metrics(),
            'strategy_profiles': self.load_strategy_profiles(),
            'live_trading': self.load_live_trading_data(),
            'live_production': self.load_live_production_data(),
            'real_time_metrics': self.load_real_time_metrics(),
            'timestamp': datetime.now().isoformat()
        }

        logger.info("Successfully loaded all dashboard data")
        return data

# Create a singleton instance
data_service = DataService()
