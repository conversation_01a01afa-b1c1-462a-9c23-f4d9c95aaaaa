#!/usr/bin/env python3
"""
Transaction Executor Module

This module provides a unified interface for executing transactions using different RPC providers.
"""

import os
import json
import time
import logging
import asyncio
from typing import Dict, Any, Optional, Union, List
from pathlib import Path

# Import our transaction preparation service
try:
    from solana_tx_utils.tx_prep import TransactionPreparationService
    USING_RUST_UTILS = True
except ImportError:
    USING_RUST_UTILS = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('transaction_executor')

class RpcClientInterface:
    """
    Interface for RPC clients.

    All RPC clients should implement this interface.
    """

    async def send_transaction(self, transaction: str, opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Send a transaction to the RPC endpoint.

        Args:
            transaction: Serialized transaction
            opts: Transaction options

        Returns:
            Dictionary containing transaction result
        """
        raise NotImplementedError("Subclasses must implement send_transaction")

    async def get_transaction_status(self, signature: str) -> Dict[str, Any]:
        """
        Get the status of a transaction.

        Args:
            signature: Transaction signature

        Returns:
            Dictionary containing transaction status
        """
        raise NotImplementedError("Subclasses must implement get_transaction_status")

    async def simulate_transaction(self, transaction: str, opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Simulate a transaction.

        Args:
            transaction: Serialized transaction
            opts: Simulation options

        Returns:
            Dictionary containing simulation result
        """
        raise NotImplementedError("Subclasses must implement simulate_transaction")

    async def close(self) -> None:
        """
        Close the client and release resources.
        """
        raise NotImplementedError("Subclasses must implement close")

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get client metrics.

        Returns:
            Dictionary containing client metrics
        """
        raise NotImplementedError("Subclasses must implement get_metrics")


class TransactionExecutor:
    """
    Unified transaction executor for different RPC providers.
    """

    def __init__(self,
                 rpc_client: RpcClientInterface,
                 config_path: Optional[str] = None,
                 keypair_path: Optional[str] = None,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 circuit_breaker_threshold: int = 5,
                 circuit_breaker_reset_time: float = 60.0):
        """
        Initialize the transaction executor.

        Args:
            rpc_client: RPC client implementing RpcClientInterface
            config_path: Path to configuration file
            keypair_path: Path to keypair file
            max_retries: Maximum number of retries for failed transactions
            retry_delay: Delay between retries in seconds
            circuit_breaker_threshold: Number of failures before opening circuit breaker
            circuit_breaker_reset_time: Time in seconds before resetting circuit breaker
        """
        self.rpc_client = rpc_client
        self.config_path = config_path
        self.keypair_path = keypair_path
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Circuit breaker state
        self.circuit_breaker_threshold = circuit_breaker_threshold
        self.circuit_breaker_reset_time = circuit_breaker_reset_time
        self.failure_count = 0
        self.circuit_breaker_open = False
        self.last_failure_time = 0

        # Transaction history
        self.tx_history = []

        # Metrics
        self.metrics = {
            'total_transactions': 0,
            'successful_transactions': 0,
            'failed_transactions': 0,
            'retried_transactions': 0,
            'circuit_breaker_trips': 0,
            'average_execution_time': 0,
            'total_execution_time': 0,
        }

        # Initialize transaction preparation service if available
        self.tx_prep_service = None
        if USING_RUST_UTILS:
            # Get RPC URL from environment or use default
            rpc_url = os.environ.get('SOLANA_RPC_URL', 'https://api.mainnet-beta.solana.com')
            self.tx_prep_service = TransactionPreparationService(rpc_url)

            # Load keypair if provided
            if keypair_path:
                self.tx_prep_service.load_keypair('default', keypair_path)
                logger.info(f"Loaded keypair from {keypair_path}")

        logger.info(f"Initialized TransactionExecutor with {rpc_client.__class__.__name__}")

    async def execute_transaction(self,
                                 transaction: Union[str, bytes, Dict[str, Any]],
                                 opts: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a transaction.

        Args:
            transaction: Transaction to execute (serialized string, bytes, or transaction object)
            opts: Transaction options

        Returns:
            Dictionary containing transaction result
        """
        # Check circuit breaker
        if self.circuit_breaker_open:
            # Check if it's time to reset the circuit breaker
            if time.time() - self.last_failure_time > self.circuit_breaker_reset_time:
                logger.info("Resetting circuit breaker")
                self.circuit_breaker_open = False
                self.failure_count = 0
            else:
                logger.warning("Circuit breaker open, rejecting transaction")
                return {
                    'success': False,
                    'error': 'Circuit breaker open',
                    'provider': None,
                    'signature': None
                }

        # Update metrics and record start time
        self.metrics['total_transactions'] += 1
        start_time = time.time()

        # Prepare transaction if needed
        prepared_result = await self._prepare_transaction(transaction, opts)
        if not prepared_result:
            self.metrics['failed_transactions'] += 1
            return {
                'success': False,
                'error': 'Failed to prepare transaction',
                'provider': None,
                'signature': None
            }

        # Handle the prepared result
        if isinstance(prepared_result, tuple):
            serialized_tx, updated_opts = prepared_result
            # Use the updated opts that may contain skip_simulation flag
            opts = updated_opts or opts
        else:
            # Backward compatibility - just the serialized transaction
            serialized_tx = prepared_result

        # Execute transaction with retries
        result = None
        retries = 0

        while retries <= self.max_retries:
            try:
                result = await self.rpc_client.send_transaction(serialized_tx, opts)

                if result.get('success', False):
                    # Transaction succeeded
                    break

                # Transaction failed, retry if appropriate
                error = result.get('error', 'Unknown error')
                if self._should_retry(error):
                    retries += 1
                    self.metrics['retried_transactions'] += 1
                    if retries <= self.max_retries:
                        logger.info(f"Retrying transaction ({retries}/{self.max_retries}): {error}")
                        await asyncio.sleep(self.retry_delay * retries)  # Exponential backoff
                    else:
                        logger.warning(f"Max retries reached: {error}")
                        break
                else:
                    # Non-retryable error
                    logger.warning(f"Non-retryable error: {error}")
                    break
            except Exception as e:
                logger.error(f"Error executing transaction: {str(e)}")
                retries += 1
                if retries <= self.max_retries:
                    logger.info(f"Retrying transaction ({retries}/{self.max_retries}) after exception")
                    await asyncio.sleep(self.retry_delay * retries)  # Exponential backoff
                else:
                    logger.warning(f"Max retries reached after exception: {str(e)}")
                    result = {
                        'success': False,
                        'error': f"Exception: {str(e)}",
                        'provider': None,
                        'signature': None
                    }
                    break

        # Update metrics
        execution_time = time.time() - start_time
        self.metrics['total_execution_time'] += execution_time
        if self.metrics['total_transactions'] > 0:
            self.metrics['average_execution_time'] = (
                self.metrics['total_execution_time'] / self.metrics['total_transactions']
            )

        if result and result.get('success', False):
            self.metrics['successful_transactions'] += 1

            # Record in history
            self.tx_history.append({
                'timestamp': time.time(),
                'signature': result.get('signature'),
                'status': 'success',
                'provider': result.get('provider'),
                'execution_time': execution_time
            })

            # Reset failure count
            self.failure_count = 0
        else:
            self.metrics['failed_transactions'] += 1

            # Update circuit breaker
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.circuit_breaker_threshold:
                logger.warning(f"Circuit breaker tripped after {self.failure_count} failures")
                self.circuit_breaker_open = True
                self.metrics['circuit_breaker_trips'] += 1

            # Record in history
            self.tx_history.append({
                'timestamp': time.time(),
                'signature': result.get('signature') if result else None,
                'status': 'failure',
                'error': result.get('error') if result else 'Unknown error',
                'provider': result.get('provider') if result else None,
                'execution_time': execution_time
            })

        return result or {
            'success': False,
            'error': 'Failed to execute transaction',
            'provider': None,
            'signature': None
        }

    async def execute_bundle(self,
                           transactions: List[Union[str, bytes, Dict[str, Any]]],
                           opts: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a bundle of transactions.

        Args:
            transactions: List of transactions to execute
            opts: Bundle options

        Returns:
            Dictionary containing bundle result
        """
        # Check if the RPC client supports bundles
        if not hasattr(self.rpc_client, 'send_bundle'):
            logger.warning("RPC client does not support bundles")
            return {
                'success': False,
                'error': 'RPC client does not support bundles',
                'provider': None,
                'bundle_id': None
            }

        # Check circuit breaker
        if self.circuit_breaker_open:
            # Check if it's time to reset the circuit breaker
            if time.time() - self.last_failure_time > self.circuit_breaker_reset_time:
                logger.info("Resetting circuit breaker")
                self.circuit_breaker_open = False
                self.failure_count = 0
            else:
                logger.warning("Circuit breaker open, rejecting bundle")
                return {
                    'success': False,
                    'error': 'Circuit breaker open',
                    'provider': None,
                    'bundle_id': None
                }

        # Update metrics and record start time
        self.metrics['total_transactions'] += len(transactions)
        start_time = time.time()

        # Prepare transactions
        serialized_txs = []
        for tx in transactions:
            serialized_tx = await self._prepare_transaction(tx, opts)
            if not serialized_tx:
                self.metrics['failed_transactions'] += 1
                return {
                    'success': False,
                    'error': 'Failed to prepare transaction in bundle',
                    'provider': None,
                    'bundle_id': None
                }
            serialized_txs.append(serialized_tx)

        # Execute bundle
        try:
            result = await self.rpc_client.send_bundle(serialized_txs, opts)

            # Update metrics
            execution_time = time.time() - start_time
            self.metrics['total_execution_time'] += execution_time
            if self.metrics['total_transactions'] > 0:
                self.metrics['average_execution_time'] = (
                    self.metrics['total_execution_time'] / self.metrics['total_transactions']
                )

            if result.get('success', False):
                self.metrics['successful_transactions'] += len(transactions)

                # Record in history
                self.tx_history.append({
                    'timestamp': time.time(),
                    'bundle_id': result.get('bundle_id'),
                    'status': 'success',
                    'provider': result.get('provider'),
                    'execution_time': execution_time,
                    'num_transactions': len(transactions)
                })

                # Reset failure count
                self.failure_count = 0
            else:
                self.metrics['failed_transactions'] += len(transactions)

                # Update circuit breaker
                self.failure_count += 1
                self.last_failure_time = time.time()

                if self.failure_count >= self.circuit_breaker_threshold:
                    logger.warning(f"Circuit breaker tripped after {self.failure_count} failures")
                    self.circuit_breaker_open = True
                    self.metrics['circuit_breaker_trips'] += 1

                # Record in history
                self.tx_history.append({
                    'timestamp': time.time(),
                    'bundle_id': result.get('bundle_id'),
                    'status': 'failure',
                    'error': result.get('error', 'Unknown error'),
                    'provider': result.get('provider'),
                    'execution_time': execution_time,
                    'num_transactions': len(transactions)
                })

            return result
        except Exception as e:
            logger.error(f"Error executing bundle: {str(e)}")
            self.metrics['failed_transactions'] += len(transactions)

            # Update circuit breaker
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.circuit_breaker_threshold:
                logger.warning(f"Circuit breaker tripped after {self.failure_count} failures")
                self.circuit_breaker_open = True
                self.metrics['circuit_breaker_trips'] += 1

            return {
                'success': False,
                'error': f"Exception: {str(e)}",
                'provider': None,
                'bundle_id': None
            }

    async def simulate_bundle(self,
                            transactions: List[Union[str, bytes, Dict[str, Any]]],
                            opts: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Simulate a bundle of transactions.

        Args:
            transactions: List of transactions to simulate
            opts: Simulation options

        Returns:
            Dictionary containing simulation result
        """
        # Check if the RPC client supports bundle simulation
        if not hasattr(self.rpc_client, 'simulate_bundle'):
            logger.warning("RPC client does not support bundle simulation")
            return {
                'success': False,
                'error': 'RPC client does not support bundle simulation',
                'provider': None,
                'results': None
            }

        # Prepare transactions
        serialized_txs = []
        for tx in transactions:
            serialized_tx = await self._prepare_transaction(tx, opts)
            if not serialized_tx:
                return {
                    'success': False,
                    'error': 'Failed to prepare transaction in bundle',
                    'provider': None,
                    'results': None
                }
            serialized_txs.append(serialized_tx)

        # Simulate bundle
        try:
            return await self.rpc_client.simulate_bundle(serialized_txs, opts)
        except Exception as e:
            logger.error(f"Error simulating bundle: {str(e)}")
            return {
                'success': False,
                'error': f"Exception: {str(e)}",
                'provider': None,
                'results': None
            }

    async def get_transaction_status(self, signature: str) -> Dict[str, Any]:
        """
        Get the status of a transaction.

        Args:
            signature: Transaction signature

        Returns:
            Dictionary containing transaction status
        """
        try:
            return await self.rpc_client.get_transaction_status(signature)
        except Exception as e:
            logger.error(f"Error getting transaction status: {str(e)}")
            return {
                'success': False,
                'error': f"Exception: {str(e)}",
                'provider': None,
                'status': None
            }

    async def close(self) -> None:
        """
        Close the executor and release resources.
        """
        await self.rpc_client.close()

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get executor metrics.

        Returns:
            Dictionary containing executor metrics
        """
        # Get client metrics
        client_metrics = self.rpc_client.get_metrics()

        # Combine with executor metrics
        return {
            **self.metrics,
            'client_metrics': client_metrics,
            'tx_history_length': len(self.tx_history),
            'circuit_breaker_status': 'open' if self.circuit_breaker_open else 'closed',
            'failure_count': self.failure_count
        }

    async def _prepare_transaction(self,
                                 transaction: Union[str, bytes, Dict[str, Any]],
                                 opts: Optional[Dict[str, Any]] = None) -> Optional[Union[str, tuple]]:
        """
        Prepare a transaction for submission.

        Args:
            transaction: Transaction to prepare
            opts: Transaction options

        Returns:
            Serialized transaction string
        """
        opts = opts or {}
        # FIXED: Use appropriate encoding based on RPC client type
        # Helius expects base58, Jito expects base64
        client_type = type(self.rpc_client).__name__
        if client_type == 'HeliusClient':
            encoding = opts.get('encoding', 'base58')  # Helius uses base58
        else:
            encoding = opts.get('encoding', 'base64')  # Jito uses base64

        # Add debug logging for transaction preparation
        logger.debug(f"Preparing transaction: type={type(transaction)}, encoding={encoding}, client={client_type}")
        if isinstance(transaction, dict) and 'transaction' in transaction:
            tx_bytes = transaction['transaction']
            logger.debug(f"Transaction dict contains {len(tx_bytes) if isinstance(tx_bytes, bytes) else 'non-bytes'} bytes")

        # If transaction is already a string, assume it's already serialized
        if isinstance(transaction, str):
            return transaction

        # If transaction is bytes, encode it
        if isinstance(transaction, bytes):
            if self.tx_prep_service:
                return self.tx_prep_service.prepare_for_submission(transaction, encoding)
            else:
                # Fallback encoding
                if encoding == 'base58':
                    import base58
                    return base58.b58encode(transaction).decode('utf-8')
                elif encoding == 'base64':
                    import base64
                    return base64.b64encode(transaction).decode('utf-8')
                else:
                    logger.error(f"Unsupported encoding: {encoding}")
                    return None

        # If transaction is a dictionary, handle special transaction formats
        if isinstance(transaction, dict):
            # Check if it's our new transaction format with metadata
            if 'transaction' in transaction:
                # Extract the actual transaction bytes
                tx_bytes = transaction['transaction']
                skip_simulation = transaction.get('skip_simulation', False)
                tx_type = transaction.get('transaction_type', 'unknown')

                logger.info(f"Processing {tx_type} transaction with skip_simulation={skip_simulation}")

                # Store skip_simulation flag in opts for the RPC client
                if opts is None:
                    opts = {}
                opts['skip_simulation'] = skip_simulation

                # FIXED: Enhanced encoding with validation
                if isinstance(tx_bytes, bytes):
                    logger.debug(f"Encoding {len(tx_bytes)} bytes to {encoding}")

                    try:
                        if encoding == 'base58':
                            import base58
                            serialized_tx = base58.b58encode(tx_bytes).decode('utf-8')
                            logger.debug(f"Base58 encoded: {len(serialized_tx)} chars")
                            return (serialized_tx, opts)
                        elif encoding == 'base64':
                            import base64
                            serialized_tx = base64.b64encode(tx_bytes).decode('utf-8')

                            # CRITICAL: Validate the base64 encoding
                            base64.b64decode(serialized_tx, validate=True)
                            logger.debug(f"Base64 encoded and validated: {len(serialized_tx)} chars")
                            return (serialized_tx, opts)
                        else:
                            logger.error(f"Unsupported encoding: {encoding}")
                            return None
                    except Exception as e:
                        logger.error(f"Failed to encode transaction bytes: {e}")
                        return None
                else:
                    logger.error(f"Expected bytes in transaction dict, got {type(tx_bytes)}")
                    return None
            else:
                logger.warning("Dictionary transactions not yet supported (missing 'transaction' key)")
                return None

        logger.error(f"Unsupported transaction type: {type(transaction)}")
        return None

    def _should_retry(self, error: str) -> bool:
        """
        Determine if a transaction should be retried based on the error.

        Args:
            error: Error message

        Returns:
            True if the transaction should be retried, False otherwise
        """
        # List of retryable errors
        retryable_errors = [
            'blockhash not found',
            'timeout',
            'timed out',
            'rate limited',
            'too many requests',
            'server busy',
            'connection',
            'network',
            'socket',
            'unavailable',
            'service unavailable',
            'internal server error',
            '500',
            '503',
            '504',
        ]

        # Check if any retryable error is in the error message
        error_lower = error.lower()
        return any(e in error_lower for e in retryable_errors)


async def main():
    """
    Main function to demonstrate the transaction executor.
    """
    # Import RPC clients
    from rpc_execution.jito_client import JitoClient

    # Create Jito client as primary (with built-in Helius fallback)
    jito_client = JitoClient(
        rpc_url="https://mainnet.block-engine.jito.wtf",
        fallback_rpc_url=f"https://mainnet.helius-rpc.com/?api-key={os.environ.get('HELIUS_API_KEY')}",
        auth_keypair_path=None,
        max_retries=3,
        retry_delay=1.0,
        timeout=30.0
    )

    # Create transaction executor with Jito client
    executor = TransactionExecutor(
        rpc_client=jito_client,
        keypair_path=os.environ.get('KEYPAIR_PATH'),
        max_retries=3,
        retry_delay=1.0
    )

    # Example transaction (this is just a placeholder)
    import base58
    example_tx = base58.b58encode(b"example_transaction").decode('utf-8')

    # Execute transaction
    logger.info("Executing example transaction...")
    result = await executor.execute_transaction(example_tx)

    if result['success']:
        logger.info(f"Transaction executed successfully: {result['signature']}")

        # Get transaction status
        status_result = await executor.get_transaction_status(result['signature'])
        logger.info(f"Transaction status: {status_result}")
    else:
        logger.error(f"Failed to execute transaction: {result.get('error')}")

    # Get metrics
    metrics = executor.get_metrics()
    logger.info(f"Executor metrics: {metrics}")

    # Close executor
    await executor.close()


if __name__ == "__main__":
    # Run main function
    asyncio.run(main())
