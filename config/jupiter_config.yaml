# Jupiter Integration Configuration
# Comprehensive configuration for Jupiter swap integration

# API Configuration
api:
  # Jupiter Quote API endpoint
  quote_url: "https://quote-api.jup.ag/v6/quote"
  
  # Jupiter Swap API endpoint
  swap_url: "https://quote-api.jup.ag/v6/swap"
  
  # Jupiter Price API endpoint
  price_url: "https://price.jup.ag/v4/price"
  
  # Jupiter Token List API
  tokens_url: "https://token.jup.ag/all"
  
  # Request timeout in seconds
  timeout: 30.0
  
  # Maximum retries for API calls
  max_retries: 3
  
  # Retry delay in seconds
  retry_delay: 1.0

# Transaction Configuration
transaction:
  # Default slippage tolerance in basis points (50 = 0.5%)
  default_slippage_bps: 50
  
  # Maximum slippage tolerance in basis points (300 = 3%)
  max_slippage_bps: 300
  
  # Minimum slippage tolerance in basis points (10 = 0.1%)
  min_slippage_bps: 10
  
  # Whether to wrap/unwrap SOL automatically
  wrap_and_unwrap_sol: true
  
  # Whether to use shared accounts for better pricing
  use_shared_accounts: true
  
  # Whether to use legacy transactions (false for versioned transactions)
  as_legacy_transaction: false
  
  # Whether to use token ledger for better routing
  use_token_ledger: false
  
  # Compute unit price setting ("auto" or specific micro-lamports)
  compute_unit_price_micro_lamports: "auto"
  
  # Prioritization fee setting ("auto" or specific lamports)
  prioritization_fee_lamports: "auto"
  
  # Whether to skip preflight checks
  skip_preflight: false
  
  # Maximum confirmation blocks to wait
  max_confirmation_blocks: 32

# Routing Configuration
routing:
  # Whether to use only direct routes (faster but potentially worse pricing)
  only_direct_routes: false
  
  # Maximum number of routes to consider
  max_routes: 3
  
  # Whether to exclude DEXes with high fees
  exclude_high_fee_dexes: false
  
  # Preferred DEXes (empty means use all available)
  preferred_dexes: []
  
  # Excluded DEXes
  excluded_dexes: []

# Risk Management
risk:
  # Minimum liquidity required in USD
  min_liquidity_usd: 10000
  
  # Maximum price impact allowed (in percentage)
  max_price_impact_pct: 2.0
  
  # Minimum output amount protection (in percentage of expected)
  min_output_amount_pct: 95.0
  
  # Maximum transaction fee in SOL
  max_transaction_fee_sol: 0.01

# Token Configuration
tokens:
  # Default token decimals if not found in registry
  default_decimals: 9
  
  # Known token mints for quick reference
  known_mints:
    SOL: "So11111111111111111111111111111111111111112"
    USDC: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    USDT: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"
    JTO: "jtojtomepa8beP8AuQc6eXt5FriJwfFMwQx2v2f9mCL"
    BONK: "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
    WIF: "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"
    PYTH: "HZ1JovNiVvGrGNiiYvEozEVgZ58xaU3RKwX8eACQBCt3"
    JUP: "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN"

# Monitoring Configuration
monitoring:
  # Whether to enable detailed logging
  verbose_logging: false
  
  # Whether to track swap metrics
  track_metrics: true
  
  # Metrics file path
  metrics_path: "output/jupiter_metrics.json"
  
  # Metrics update interval in seconds
  update_interval: 60.0
  
  # Whether to log all API requests/responses
  log_api_calls: false

# Circuit Breaker Configuration
circuit_breaker:
  # Whether to enable circuit breaker
  enabled: true
  
  # Maximum consecutive failures before breaking
  failure_threshold: 5
  
  # Time to wait before attempting reset (seconds)
  reset_timeout: 300.0
  
  # Whether to fail fast on known error conditions
  fail_fast: true

# Fee Configuration
fees:
  # Optional fee account for revenue sharing
  fee_account: null
  
  # Optional tracking account for analytics
  tracking_account: null
  
  # Fee percentage in basis points (0 = no fee)
  fee_bps: 0

# Environment-Specific Overrides
environments:
  development:
    transaction:
      default_slippage_bps: 100  # Higher slippage for testing
      skip_preflight: true       # Skip preflight in dev
    monitoring:
      verbose_logging: true      # More logging in dev
      log_api_calls: true       # Log all API calls in dev
  
  staging:
    transaction:
      default_slippage_bps: 75   # Medium slippage for staging
    risk:
      max_price_impact_pct: 1.5  # Stricter risk controls
  
  production:
    transaction:
      default_slippage_bps: 50   # Conservative slippage
    risk:
      max_price_impact_pct: 1.0  # Strict risk controls
    monitoring:
      verbose_logging: false     # Minimal logging in prod
      log_api_calls: false      # No API logging in prod
